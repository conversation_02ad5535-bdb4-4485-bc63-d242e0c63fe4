#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/fix_ops.h>

namespace at {


// aten::fix(Tensor self) -> Tensor
inline at::Tensor fix(const at::Tensor & self) {
    return at::_ops::fix::call(self);
}

// aten::fix_(Tensor(a!) self) -> Tensor(a!)
inline at::Tensor & fix_(at::Tensor & self) {
    return at::_ops::fix_::call(self);
}

// aten::fix.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & fix_out(at::Tensor & out, const at::Tensor & self) {
    return at::_ops::fix_out::call(self, out);
}
// aten::fix.out(Tensor self, *, Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & fix_outf(const at::Tensor & self, at::Tensor & out) {
    return at::_ops::fix_out::call(self, out);
}

}

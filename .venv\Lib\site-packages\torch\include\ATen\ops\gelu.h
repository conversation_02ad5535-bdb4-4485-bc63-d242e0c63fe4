#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/gelu_ops.h>

namespace at {


// aten::gelu.out(Tensor self, *, str approximate='none', Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & gelu_out(at::Tensor & out, const at::Tensor & self, c10::string_view approximate="none") {
    return at::_ops::gelu_out::call(self, approximate, out);
}
// aten::gelu.out(Tensor self, *, str approximate='none', Tensor(a!) out) -> Tensor(a!)
inline at::Tensor & gelu_outf(const at::Tensor & self, c10::string_view approximate, at::Tensor & out) {
    return at::_ops::gelu_out::call(self, approximate, out);
}

// aten::gelu_(Tensor(a!) self, *, str approximate='none') -> Tensor(a!)
inline at::Tensor & gelu_(at::Tensor & self, c10::string_view approximate="none") {
    return at::_ops::gelu_::call(self, approximate);
}

// aten::gelu(Tensor self, *, str approximate='none') -> Tensor
inline at::Tensor gelu(const at::Tensor & self, c10::string_view approximate="none") {
    return at::_ops::gelu::call(self, approximate);
}

}

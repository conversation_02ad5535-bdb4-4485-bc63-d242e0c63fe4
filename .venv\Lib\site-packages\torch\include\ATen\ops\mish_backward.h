#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <c10/util/Optional.h>



#include <ATen/ops/mish_backward_ops.h>

namespace at {


// aten::mish_backward(Tensor grad_output, Tensor self) -> Tensor
inline at::Tensor mish_backward(const at::Tensor & grad_output, const at::Tensor & self) {
    return at::_ops::mish_backward::call(grad_output, self);
}

}

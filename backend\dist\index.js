"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
const winston_1 = __importDefault(require("winston"));
const auth_1 = __importDefault(require("./routes/auth"));
const cameras_1 = __importDefault(require("./routes/cameras"));
const events_1 = __importDefault(require("./routes/events"));
const hikvision_1 = require("./services/hikvision");
const Camera_1 = require("./models/Camera");
dotenv_1.default.config();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: [
            process.env.FRONTEND_URL || "http://localhost:3000",
            "http://localhost:3001",
            "http://*************:3000",
            "http://*************:3001"
        ],
        methods: ["GET", "POST"]
    }
});
let cameraManager;
const logger = winston_1.default.createLogger({
    level: 'info',
    format: winston_1.default.format.combine(winston_1.default.format.timestamp(), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json()),
    defaultMeta: { service: 'hikvision-ai-backend' },
    transports: [
        new winston_1.default.transports.File({ filename: 'logs/error.log', level: 'error' }),
        new winston_1.default.transports.File({ filename: 'logs/combined.log' }),
        new winston_1.default.transports.Console({
            format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple())
        })
    ]
});
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: [
        process.env.FRONTEND_URL || "http://localhost:3000",
        "http://localhost:3001",
        "http://*************:3000",
        "http://*************:3001"
    ],
    credentials: true
}));
app.use((0, morgan_1.default)('combined', {
    stream: { write: (message) => logger.info(message.trim()) }
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
    });
});
app.use('/api/auth', auth_1.default);
app.use('/api/cameras', (req, res, next) => {
    req.cameraManager = cameraManager;
    next();
}, cameras_1.default);
app.use('/api/events', events_1.default);
app.get('/api/status', (req, res) => {
    res.json({
        message: 'HikVision AI Monitoring System API',
        version: '1.0.0',
        status: 'running',
        features: {
            cameras: 'active',
            ai_processing: 'active',
            real_time_events: 'active',
            notifications: 'active'
        }
    });
});
io.on('connection', (socket) => {
    logger.info(`Client connected: ${socket.id}`);
    socket.emit('welcome', {
        message: 'Connected to HikVision AI Monitoring System',
        timestamp: new Date().toISOString()
    });
    socket.on('disconnect', () => {
        logger.info(`Client disconnected: ${socket.id}`);
    });
});
async function initializeCameraManager() {
    cameraManager = new hikvision_1.CameraManager(logger);
    cameraManager.on('cameraEvent', (cameraId, event) => {
        logger.info(`Camera event from ${cameraId}:`, event.type);
        io.emit('cameraEvent', {
            cameraId,
            event: {
                id: event._id,
                type: event.type,
                title: event.title,
                description: event.description,
                severity: event.severity,
                timestamp: event.timestamp,
                metadata: event.metadata
            }
        });
    });
    cameraManager.on('cameraAdded', (cameraId) => {
        logger.info(`Camera ${cameraId} added to manager`);
        io.emit('cameraStatusUpdate', { cameraId, status: 'online' });
    });
    cameraManager.on('cameraRemoved', (cameraId) => {
        logger.info(`Camera ${cameraId} removed from manager`);
        io.emit('cameraStatusUpdate', { cameraId, status: 'offline' });
    });
    cameraManager.on('cameraArmed', (cameraId) => {
        logger.info(`Camera ${cameraId} armed`);
        io.emit('cameraArmed', { cameraId });
    });
    cameraManager.on('cameraDisarmed', (cameraId) => {
        logger.info(`Camera ${cameraId} disarmed`);
        io.emit('cameraDisarmed', { cameraId });
    });
    try {
        const cameras = await Camera_1.Camera.find({ isActive: true });
        logger.info(`Loading ${cameras.length} active cameras into manager`);
        for (const camera of cameras) {
            const success = await cameraManager.addCamera(camera);
            if (success) {
                logger.info(`Successfully loaded camera ${camera._id} (${camera.name})`);
            }
            else {
                logger.error(`Failed to load camera ${camera._id} (${camera.name})`);
            }
        }
    }
    catch (error) {
        logger.error('Failed to load cameras into manager:', error);
    }
}
const connectDB = async () => {
    try {
        const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/hikvision-ai';
        await mongoose_1.default.connect(mongoUri);
        logger.info('Connected to MongoDB');
    }
    catch (error) {
        logger.error('MongoDB connection error:', error);
        process.exit(1);
    }
};
app.use((err, req, res, next) => {
    logger.error('Unhandled error:', err);
    res.status(500).json({
        error: 'Internal server error',
        message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});
app.use((req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.originalUrl} not found`
    });
});
const PORT = process.env.PORT || 5000;
const startServer = async () => {
    try {
        await connectDB();
        await initializeCameraManager();
        server.listen(PORT, () => {
            logger.info(`🚀 HikVision AI Monitoring System running on port ${PORT}`);
            logger.info(`📊 Dashboard: http://localhost:${PORT}`);
            logger.info(`🔌 Socket.IO: Enabled`);
            logger.info(`🗄️  Database: Connected`);
            logger.info(`🔐 Authentication: JWT`);
            logger.info(`📡 API Status: All endpoints operational`);
            logger.info(`📹 Camera Manager: Initialized`);
            logger.info(`🎯 ISAPI Integration: Active`);
        });
    }
    catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
};
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    if (cameraManager) {
        cameraManager.stop();
    }
    server.close(() => {
        logger.info('Process terminated');
        mongoose_1.default.connection.close();
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    if (cameraManager) {
        cameraManager.stop();
    }
    server.close(() => {
        logger.info('Process terminated');
        mongoose_1.default.connection.close();
        process.exit(0);
    });
});
startServer();
//# sourceMappingURL=index.js.map
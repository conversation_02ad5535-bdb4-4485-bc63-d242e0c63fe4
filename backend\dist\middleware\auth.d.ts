import { Request, Response, NextFunction } from 'express';
import { IUser } from '../models/User';
export interface AuthRequest extends Request {
    user?: IUser;
    token?: string;
}
export interface JWTPayload {
    userId: string;
    email: string;
    role: string;
    iat: number;
    exp: number;
}
export declare const authenticate: (req: AuthRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const authorize: (...roles: string[]) => (req: AuthRequest, res: Response, next: NextFunction) => void;
export declare const adminOnly: (req: AuthRequest, res: Response, next: NextFunction) => void;
export declare const operatorAccess: (req: AuthRequest, res: Response, next: NextFunction) => void;
export declare const viewerAccess: (req: AuthRequest, res: Response, next: NextFunction) => void;
export declare const generateToken: (user: IUser) => string;
export declare const generateRefreshToken: (user: IUser) => string;
export declare const verifyRefreshToken: (token: string) => {
    userId: string;
};
export declare const authRateLimit: (maxAttempts?: number, windowMs?: number) => import("express-rate-limit").RateLimitRequestHandler;
export declare const logAuthEvent: (event: string) => (req: Request, res: Response, next: NextFunction) => void;
export declare const checkAccountLock: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const validateJWTFormat: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=auth.d.ts.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateJWTFormat = exports.checkAccountLock = exports.logAuthEvent = exports.authRateLimit = exports.verifyRefreshToken = exports.generateRefreshToken = exports.generateToken = exports.viewerAccess = exports.operatorAccess = exports.adminOnly = exports.authorize = exports.authenticate = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const User_1 = require("../models/User");
const authenticate = async (req, res, next) => {
    try {
        const token = req.header('Authorization')?.replace('Bearer ', '');
        if (!token) {
            res.status(401).json({
                error: 'Access denied. No token provided.'
            });
            return;
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'fallback-secret');
        const user = await User_1.User.findById(decoded.userId).select('-password');
        if (!user) {
            res.status(401).json({
                error: 'Invalid token. User not found.'
            });
            return;
        }
        if (!user.isActive) {
            res.status(401).json({
                error: 'Account is deactivated.'
            });
            return;
        }
        req.user = user;
        req.token = token;
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            res.status(401).json({
                error: 'Invalid token.'
            });
            return;
        }
        if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
            res.status(401).json({
                error: 'Token expired.'
            });
            return;
        }
        console.error('Authentication error:', error);
        res.status(500).json({
            error: 'Internal server error during authentication.'
        });
    }
};
exports.authenticate = authenticate;
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!req.user) {
            res.status(401).json({
                error: 'Authentication required.'
            });
            return;
        }
        if (!roles.includes(req.user.role)) {
            res.status(403).json({
                error: 'Insufficient permissions.'
            });
            return;
        }
        next();
    };
};
exports.authorize = authorize;
exports.adminOnly = (0, exports.authorize)('admin');
exports.operatorAccess = (0, exports.authorize)('admin', 'operator');
exports.viewerAccess = (0, exports.authorize)('admin', 'operator', 'viewer');
const generateToken = (user) => {
    const payload = {
        userId: user._id.toString(),
        email: user.email,
        role: user.role
    };
    return jsonwebtoken_1.default.sign(payload, process.env.JWT_SECRET || 'fallback-secret', {
        expiresIn: '24h'
    });
};
exports.generateToken = generateToken;
const generateRefreshToken = (user) => {
    const payload = {
        userId: user._id.toString(),
        type: 'refresh'
    };
    return jsonwebtoken_1.default.sign(payload, process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret', {
        expiresIn: '7d'
    });
};
exports.generateRefreshToken = generateRefreshToken;
const verifyRefreshToken = (token) => {
    try {
        const secret = process.env.JWT_REFRESH_SECRET || 'fallback-refresh-secret';
        const decoded = jsonwebtoken_1.default.verify(token, secret);
        if (decoded.type !== 'refresh') {
            throw new Error('Invalid token type');
        }
        return { userId: decoded.userId };
    }
    catch (error) {
        throw new Error('Invalid refresh token');
    }
};
exports.verifyRefreshToken = verifyRefreshToken;
const authRateLimit = (maxAttempts = 5, windowMs = 15 * 60 * 1000) => {
    return (0, express_rate_limit_1.default)({
        windowMs,
        max: maxAttempts,
        message: {
            error: 'Too many authentication attempts, please try again later.'
        },
        standardHeaders: true,
        legacyHeaders: false
    });
};
exports.authRateLimit = authRateLimit;
const logAuthEvent = (event) => {
    return (req, res, next) => {
        console.log(`Auth Event: ${event} - IP: ${req.ip} - Time: ${new Date().toISOString()}`);
        next();
    };
};
exports.logAuthEvent = logAuthEvent;
const checkAccountLock = async (req, res, next) => {
    try {
        const { email } = req.body;
        if (!email) {
            next();
            return;
        }
        const user = await User_1.User.findOne({ email: email.toLowerCase() });
        if (user && user.lockUntil && user.lockUntil > new Date()) {
            res.status(423).json({
                error: 'Account is temporarily locked due to too many failed login attempts.'
            });
            return;
        }
        next();
    }
    catch (error) {
        console.error('Account lock check error:', error);
        next();
    }
};
exports.checkAccountLock = checkAccountLock;
const validateJWTFormat = (req, res, next) => {
    const authHeader = req.header('Authorization');
    if (authHeader && !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
            error: 'Invalid authorization header format. Use "Bearer <token>".'
        });
        return;
    }
    next();
};
exports.validateJWTFormat = validateJWTFormat;
//# sourceMappingURL=auth.js.map
{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+C;AAC/C,4EAA0C;AAC1C,yCAA4C;AAgBrC,MAAM,YAAY,GAAG,KAAK,EAAE,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACvG,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;QAEjE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,mCAAmC;aAC3C,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,iBAAiB,CAAe,CAAA;QAC5F,MAAM,IAAI,GAAG,MAAM,WAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QAEpE,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,gCAAgC;aACxC,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,yBAAyB;aACjC,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAA;QACf,GAAG,CAAC,KAAK,GAAG,KAAK,CAAA;QACjB,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,gBAAgB;aACxB,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,gBAAgB;aACxB,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;QAC7C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,8CAA8C;SACtD,CAAC,CAAA;IACJ,CAAC;AACH,CAAC,CAAA;AAnDY,QAAA,YAAY,gBAmDxB;AAGM,MAAM,SAAS,GAAG,CAAC,GAAG,KAAe,EAAE,EAAE;IAC9C,OAAO,CAAC,GAAgB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QACnE,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,0BAA0B;aAClC,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,2BAA2B;aACnC,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAA;AAlBY,QAAA,SAAS,aAkBrB;AAGY,QAAA,SAAS,GAAG,IAAA,iBAAS,EAAC,OAAO,CAAC,CAAA;AAG9B,QAAA,cAAc,GAAG,IAAA,iBAAS,EAAC,OAAO,EAAE,UAAU,CAAC,CAAA;AAG/C,QAAA,YAAY,GAAG,IAAA,iBAAS,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAA;AAG7D,MAAM,aAAa,GAAG,CAAC,IAAW,EAAU,EAAE;IACnD,MAAM,OAAO,GAAG;QACd,MAAM,EAAG,IAAI,CAAC,GAAW,CAAC,QAAQ,EAAE;QACpC,KAAK,EAAE,IAAI,CAAC,KAAK;QACjB,IAAI,EAAE,IAAI,CAAC,IAAI;KAChB,CAAA;IAED,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,iBAAiB,EAAE;QACpE,SAAS,EAAE,KAAK;KACjB,CAAC,CAAA;AACJ,CAAC,CAAA;AAVY,QAAA,aAAa,iBAUzB;AAGM,MAAM,oBAAoB,GAAG,CAAC,IAAW,EAAU,EAAE;IAC1D,MAAM,OAAO,GAAG;QACd,MAAM,EAAG,IAAI,CAAC,GAAW,CAAC,QAAQ,EAAE;QACpC,IAAI,EAAE,SAAS;KAChB,CAAA;IAED,OAAO,sBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,yBAAyB,EAAE;QACpF,SAAS,EAAE,IAAI;KAChB,CAAC,CAAA;AACJ,CAAC,CAAA;AATY,QAAA,oBAAoB,wBAShC;AAGM,MAAM,kBAAkB,GAAG,CAAC,KAAa,EAAsB,EAAE;IACtE,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,yBAAyB,CAAA;QAC1E,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAQ,CAAA;QAEhD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;QACvC,CAAC;QAED,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAA;IACnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;IAC1C,CAAC;AACH,CAAC,CAAA;AAbY,QAAA,kBAAkB,sBAa9B;AAGM,MAAM,aAAa,GAAG,CAAC,cAAsB,CAAC,EAAE,WAAmB,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,EAAE;IAC1F,OAAO,IAAA,4BAAS,EAAC;QACf,QAAQ;QACR,GAAG,EAAE,WAAW;QAChB,OAAO,EAAE;YACP,KAAK,EAAE,2DAA2D;SACnE;QACD,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,KAAK;KACrB,CAAC,CAAA;AACJ,CAAC,CAAA;AAVY,QAAA,aAAa,iBAUzB;AAGM,MAAM,YAAY,GAAG,CAAC,KAAa,EAAE,EAAE;IAC5C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;QAC/D,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,UAAU,GAAG,CAAC,EAAE,YAAY,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;QACvF,IAAI,EAAE,CAAA;IACR,CAAC,CAAA;AACH,CAAC,CAAA;AALY,QAAA,YAAY,gBAKxB;AAGM,MAAM,gBAAgB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IACvG,IAAI,CAAC;QACH,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,IAAI,CAAA;QAE1B,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,IAAI,EAAE,CAAA;YACN,OAAM;QACR,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,WAAI,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAA;QAE/D,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;YAC1D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,sEAAsE;aAC9E,CAAC,CAAA;YACF,OAAM;QACR,CAAC;QAED,IAAI,EAAE,CAAA;IACR,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;QACjD,IAAI,EAAE,CAAA;IACR,CAAC;AACH,CAAC,CAAA;AAvBY,QAAA,gBAAgB,oBAuB5B;AAGM,MAAM,iBAAiB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACzF,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,CAAA;IAE9C,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,4DAA4D;SACpE,CAAC,CAAA;QACF,OAAM;IACR,CAAC;IAED,IAAI,EAAE,CAAA;AACR,CAAC,CAAA;AAXY,QAAA,iBAAiB,qBAW7B"}
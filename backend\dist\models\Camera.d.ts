import mongoose, { Document } from 'mongoose';
export interface ICamera extends Document {
    name: string;
    description?: string;
    location: string;
    ipAddress: string;
    port: number;
    username: string;
    password: string;
    cameraModel: string;
    manufacturer: string;
    firmware?: string;
    resolution: string;
    fps: number;
    status: 'online' | 'offline' | 'maintenance' | 'error';
    isArmed: boolean;
    isRecording: boolean;
    lastSeen?: Date;
    lastHeartbeat?: Date;
    settings: {
        motionDetection: boolean;
        motionSensitivity: number;
        nightVision: boolean;
        audioRecording: boolean;
        recordingQuality: 'low' | 'medium' | 'high' | 'ultra';
        recordingMode: 'continuous' | 'motion' | 'schedule';
        retentionDays: number;
    };
    aiSettings: {
        enabled: boolean;
        personDetection: boolean;
        vehicleDetection: boolean;
        faceRecognition: boolean;
        objectDetection: boolean;
        confidenceThreshold: number;
    };
    position: {
        latitude?: number;
        longitude?: number;
        floor?: string;
        zone?: string;
    };
    network: {
        rtspUrl?: string;
        httpUrl?: string;
        onvifUrl?: string;
        streamUrl?: string;
    };
    statistics: {
        totalEvents: number;
        eventsToday: number;
        uptime: number;
        lastReboot?: Date;
        dataTransferred: number;
    };
    maintenance: {
        lastMaintenance?: Date;
        nextMaintenance?: Date;
        maintenanceNotes?: string;
    };
    createdAt: Date;
    updatedAt: Date;
    isOnline: boolean;
    updateHeartbeat(): Promise<void>;
    updateStatus(status: string): Promise<void>;
}
export declare const Camera: mongoose.Model<ICamera, {}, {}, {}, mongoose.Document<unknown, {}, ICamera, {}> & ICamera & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Camera.d.ts.map
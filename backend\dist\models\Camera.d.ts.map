{"version": 3, "file": "Camera.d.ts", "sourceRoot": "", "sources": ["../../src/models/Camera.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAU,MAAM,UAAU,CAAA;AAErD,MAAM,WAAW,OAAQ,SAAQ,QAAQ;IACvC,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,QAAQ,EAAE,MAAM,CAAA;IAChB,SAAS,EAAE,MAAM,CAAA;IACjB,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,MAAM,CAAA;IAChB,WAAW,EAAE,MAAM,CAAA;IACnB,YAAY,EAAE,MAAM,CAAA;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,MAAM,CAAA;IAClB,GAAG,EAAE,MAAM,CAAA;IACX,MAAM,EAAE,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,OAAO,CAAA;IACtD,OAAO,EAAE,OAAO,CAAA;IAChB,WAAW,EAAE,OAAO,CAAA;IACpB,QAAQ,CAAC,EAAE,IAAI,CAAA;IACf,aAAa,CAAC,EAAE,IAAI,CAAA;IACpB,QAAQ,EAAE;QACR,eAAe,EAAE,OAAO,CAAA;QACxB,iBAAiB,EAAE,MAAM,CAAA;QACzB,WAAW,EAAE,OAAO,CAAA;QACpB,cAAc,EAAE,OAAO,CAAA;QACvB,gBAAgB,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAA;QACrD,aAAa,EAAE,YAAY,GAAG,QAAQ,GAAG,UAAU,CAAA;QACnD,aAAa,EAAE,MAAM,CAAA;KACtB,CAAA;IACD,UAAU,EAAE;QACV,OAAO,EAAE,OAAO,CAAA;QAChB,eAAe,EAAE,OAAO,CAAA;QACxB,gBAAgB,EAAE,OAAO,CAAA;QACzB,eAAe,EAAE,OAAO,CAAA;QACxB,eAAe,EAAE,OAAO,CAAA;QACxB,mBAAmB,EAAE,MAAM,CAAA;KAC5B,CAAA;IACD,QAAQ,EAAE;QACR,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,IAAI,CAAC,EAAE,MAAM,CAAA;KACd,CAAA;IACD,OAAO,EAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAA;QAChB,OAAO,CAAC,EAAE,MAAM,CAAA;QAChB,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,SAAS,CAAC,EAAE,MAAM,CAAA;KACnB,CAAA;IACD,UAAU,EAAE;QACV,WAAW,EAAE,MAAM,CAAA;QACnB,WAAW,EAAE,MAAM,CAAA;QACnB,MAAM,EAAE,MAAM,CAAA;QACd,UAAU,CAAC,EAAE,IAAI,CAAA;QACjB,eAAe,EAAE,MAAM,CAAA;KACxB,CAAA;IACD,WAAW,EAAE;QACX,eAAe,CAAC,EAAE,IAAI,CAAA;QACtB,eAAe,CAAC,EAAE,IAAI,CAAA;QACtB,gBAAgB,CAAC,EAAE,MAAM,CAAA;KAC1B,CAAA;IACD,SAAS,EAAE,IAAI,CAAA;IACf,SAAS,EAAE,IAAI,CAAA;IACf,QAAQ,EAAE,OAAO,CAAA;IACjB,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IAChC,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;CAC5C;AAoRD,eAAO,MAAM,MAAM;;;;OAAkD,CAAA"}
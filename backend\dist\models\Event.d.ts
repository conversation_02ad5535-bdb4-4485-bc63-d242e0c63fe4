import mongoose, { Document } from 'mongoose';
export interface IEvent extends Document {
    eventId: string;
    type: 'motion' | 'person' | 'vehicle' | 'face' | 'intrusion' | 'object' | 'audio' | 'system';
    subType?: string;
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    status: 'active' | 'acknowledged' | 'resolved' | 'dismissed' | 'investigating';
    cameraId: mongoose.Types.ObjectId;
    cameraName: string;
    location: string;
    timestamp: Date;
    endTimestamp?: Date;
    duration?: number;
    confidence: number;
    metadata: {
        detectionBox?: {
            x: number;
            y: number;
            width: number;
            height: number;
        };
        objectClass?: string;
        objectCount?: number;
        motionArea?: number;
        audioLevel?: number;
        temperature?: number;
        customData?: Record<string, any>;
    };
    aiAnalysis?: {
        processed: boolean;
        processingTime?: number;
        results?: {
            objects: Array<{
                class: string;
                confidence: number;
                bbox: [number, number, number, number];
            }>;
            faces?: Array<{
                confidence: number;
                bbox: [number, number, number, number];
                identity?: string;
            }>;
            text?: Array<{
                text: string;
                confidence: number;
                bbox: [number, number, number, number];
            }>;
            summary?: string;
        };
        error?: string;
    };
    media: {
        thumbnail?: string;
        image?: string;
        video?: string;
        audio?: string;
        duration?: number;
        size?: number;
    };
    actions: Array<{
        type: 'email' | 'sms' | 'push' | 'webhook' | 'recording' | 'alarm';
        status: 'pending' | 'sent' | 'failed';
        timestamp: Date;
        details?: string;
        error?: string;
    }>;
    acknowledgment?: {
        acknowledgedBy: mongoose.Types.ObjectId;
        acknowledgedAt: Date;
        notes?: string;
    };
    resolution?: {
        resolvedBy: mongoose.Types.ObjectId;
        resolvedAt: Date;
        resolution: string;
        notes?: string;
    };
    tags: string[];
    priority: number;
    isArchived: boolean;
    createdAt: Date;
    updatedAt: Date;
    acknowledge(userId: mongoose.Types.ObjectId, notes?: string): Promise<void>;
    resolve(userId: mongoose.Types.ObjectId, resolution: string, notes?: string): Promise<void>;
    addAction(action: any): Promise<void>;
}
export declare const Event: mongoose.Model<IEvent, {}, {}, {}, mongoose.Document<unknown, {}, IEvent, {}> & IEvent & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
//# sourceMappingURL=Event.d.ts.map
{"version": 3, "file": "Event.d.ts", "sourceRoot": "", "sources": ["../../src/models/Event.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,EAAE,EAAE,QAAQ,EAAU,MAAM,UAAU,CAAA;AAErD,MAAM,WAAW,MAAO,SAAQ,QAAQ;IACtC,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,CAAA;IAC5F,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,KAAK,EAAE,MAAM,CAAA;IACb,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAA;IAChD,MAAM,EAAE,QAAQ,GAAG,cAAc,GAAG,UAAU,GAAG,WAAW,GAAG,eAAe,CAAA;IAC9E,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAA;IACjC,UAAU,EAAE,MAAM,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;IAChB,SAAS,EAAE,IAAI,CAAA;IACf,YAAY,CAAC,EAAE,IAAI,CAAA;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,MAAM,CAAA;IAClB,QAAQ,EAAE;QACR,YAAY,CAAC,EAAE;YACb,CAAC,EAAE,MAAM,CAAA;YACT,CAAC,EAAE,MAAM,CAAA;YACT,KAAK,EAAE,MAAM,CAAA;YACb,MAAM,EAAE,MAAM,CAAA;SACf,CAAA;QACD,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,WAAW,CAAC,EAAE,MAAM,CAAA;QACpB,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;KACjC,CAAA;IACD,UAAU,CAAC,EAAE;QACX,SAAS,EAAE,OAAO,CAAA;QAClB,cAAc,CAAC,EAAE,MAAM,CAAA;QACvB,OAAO,CAAC,EAAE;YACR,OAAO,EAAE,KAAK,CAAC;gBACb,KAAK,EAAE,MAAM,CAAA;gBACb,UAAU,EAAE,MAAM,CAAA;gBAClB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;aACvC,CAAC,CAAA;YACF,KAAK,CAAC,EAAE,KAAK,CAAC;gBACZ,UAAU,EAAE,MAAM,CAAA;gBAClB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;gBACtC,QAAQ,CAAC,EAAE,MAAM,CAAA;aAClB,CAAC,CAAA;YACF,IAAI,CAAC,EAAE,KAAK,CAAC;gBACX,IAAI,EAAE,MAAM,CAAA;gBACZ,UAAU,EAAE,MAAM,CAAA;gBAClB,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAA;aACvC,CAAC,CAAA;YACF,OAAO,CAAC,EAAE,MAAM,CAAA;SACjB,CAAA;QACD,KAAK,CAAC,EAAE,MAAM,CAAA;KACf,CAAA;IACD,KAAK,EAAE;QACL,SAAS,CAAC,EAAE,MAAM,CAAA;QAClB,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,KAAK,CAAC,EAAE,MAAM,CAAA;QACd,QAAQ,CAAC,EAAE,MAAM,CAAA;QACjB,IAAI,CAAC,EAAE,MAAM,CAAA;KACd,CAAA;IACD,OAAO,EAAE,KAAK,CAAC;QACb,IAAI,EAAE,OAAO,GAAG,KAAK,GAAG,MAAM,GAAG,SAAS,GAAG,WAAW,GAAG,OAAO,CAAA;QAClE,MAAM,EAAE,SAAS,GAAG,MAAM,GAAG,QAAQ,CAAA;QACrC,SAAS,EAAE,IAAI,CAAA;QACf,OAAO,CAAC,EAAE,MAAM,CAAA;QAChB,KAAK,CAAC,EAAE,MAAM,CAAA;KACf,CAAC,CAAA;IACF,cAAc,CAAC,EAAE;QACf,cAAc,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAA;QACvC,cAAc,EAAE,IAAI,CAAA;QACpB,KAAK,CAAC,EAAE,MAAM,CAAA;KACf,CAAA;IACD,UAAU,CAAC,EAAE;QACX,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAA;QACnC,UAAU,EAAE,IAAI,CAAA;QAChB,UAAU,EAAE,MAAM,CAAA;QAClB,KAAK,CAAC,EAAE,MAAM,CAAA;KACf,CAAA;IACD,IAAI,EAAE,MAAM,EAAE,CAAA;IACd,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,OAAO,CAAA;IACnB,SAAS,EAAE,IAAI,CAAA;IACf,SAAS,EAAE,IAAI,CAAA;IACf,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3E,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3F,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;CACtC;AAqUD,eAAO,MAAM,KAAK;;;;OAA+C,CAAA"}
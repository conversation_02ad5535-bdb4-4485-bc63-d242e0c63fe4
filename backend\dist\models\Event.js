"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Event = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const EventSchema = new mongoose_1.Schema({
    eventId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    type: {
        type: String,
        required: true,
        enum: ['motion', 'person', 'vehicle', 'face', 'intrusion', 'object', 'audio', 'system'],
        index: true
    },
    subType: {
        type: String,
        trim: true
    },
    title: {
        type: String,
        required: true,
        trim: true,
        maxlength: 200
    },
    description: {
        type: String,
        required: true,
        trim: true,
        maxlength: 1000
    },
    severity: {
        type: String,
        required: true,
        enum: ['low', 'medium', 'high', 'critical'],
        index: true
    },
    status: {
        type: String,
        required: true,
        enum: ['active', 'acknowledged', 'resolved', 'dismissed', 'investigating'],
        default: 'active',
        index: true
    },
    cameraId: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Camera',
        required: true,
        index: true
    },
    cameraName: {
        type: String,
        required: true,
        trim: true
    },
    location: {
        type: String,
        required: true,
        trim: true,
        index: true
    },
    timestamp: {
        type: Date,
        required: true,
        index: true
    },
    endTimestamp: {
        type: Date
    },
    duration: {
        type: Number,
        min: 0
    },
    confidence: {
        type: Number,
        required: true,
        min: 0,
        max: 100
    },
    metadata: {
        detectionBox: {
            x: { type: Number, min: 0 },
            y: { type: Number, min: 0 },
            width: { type: Number, min: 0 },
            height: { type: Number, min: 0 }
        },
        objectClass: {
            type: String,
            trim: true
        },
        objectCount: {
            type: Number,
            min: 0
        },
        motionArea: {
            type: Number,
            min: 0
        },
        audioLevel: {
            type: Number,
            min: 0
        },
        temperature: {
            type: Number
        },
        customData: {
            type: mongoose_1.Schema.Types.Mixed
        }
    },
    aiAnalysis: {
        processed: {
            type: Boolean,
            default: false
        },
        processingTime: {
            type: Number,
            min: 0
        },
        results: {
            objects: [{
                    class: { type: String, required: true },
                    confidence: { type: Number, required: true, min: 0, max: 100 },
                    bbox: [{ type: Number, required: true }]
                }],
            faces: [{
                    confidence: { type: Number, required: true, min: 0, max: 100 },
                    bbox: [{ type: Number, required: true }],
                    identity: { type: String }
                }],
            text: [{
                    text: { type: String, required: true },
                    confidence: { type: Number, required: true, min: 0, max: 100 },
                    bbox: [{ type: Number, required: true }]
                }],
            summary: {
                type: String,
                trim: true
            }
        },
        error: {
            type: String,
            trim: true
        }
    },
    media: {
        thumbnail: {
            type: String,
            trim: true
        },
        image: {
            type: String,
            trim: true
        },
        video: {
            type: String,
            trim: true
        },
        audio: {
            type: String,
            trim: true
        },
        duration: {
            type: Number,
            min: 0
        },
        size: {
            type: Number,
            min: 0
        }
    },
    actions: [{
            type: {
                type: String,
                required: true,
                enum: ['email', 'sms', 'push', 'webhook', 'recording', 'alarm']
            },
            status: {
                type: String,
                required: true,
                enum: ['pending', 'sent', 'failed'],
                default: 'pending'
            },
            timestamp: {
                type: Date,
                required: true,
                default: Date.now
            },
            details: {
                type: String,
                trim: true
            },
            error: {
                type: String,
                trim: true
            }
        }],
    acknowledgment: {
        acknowledgedBy: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'User'
        },
        acknowledgedAt: {
            type: Date
        },
        notes: {
            type: String,
            trim: true,
            maxlength: 500
        }
    },
    resolution: {
        resolvedBy: {
            type: mongoose_1.Schema.Types.ObjectId,
            ref: 'User'
        },
        resolvedAt: {
            type: Date
        },
        resolution: {
            type: String,
            trim: true,
            maxlength: 500
        },
        notes: {
            type: String,
            trim: true,
            maxlength: 1000
        }
    },
    tags: [{
            type: String,
            trim: true,
            lowercase: true
        }],
    priority: {
        type: Number,
        min: 1,
        max: 10,
        default: 5
    },
    isArchived: {
        type: Boolean,
        default: false,
        index: true
    }
}, {
    timestamps: true
});
EventSchema.index({ timestamp: -1, status: 1 });
EventSchema.index({ cameraId: 1, timestamp: -1 });
EventSchema.index({ type: 1, severity: 1, timestamp: -1 });
EventSchema.index({ status: 1, priority: -1, timestamp: -1 });
EventSchema.index({ location: 1, timestamp: -1 });
EventSchema.index({ tags: 1 });
EventSchema.pre('save', function (next) {
    if (!this.eventId) {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 5);
        this.eventId = `EVT-${timestamp}-${random}`.toUpperCase();
    }
    if (this.endTimestamp && this.timestamp) {
        this.duration = this.endTimestamp.getTime() - this.timestamp.getTime();
    }
    next();
});
EventSchema.methods.acknowledge = async function (userId, notes) {
    this.status = 'acknowledged';
    this.acknowledgment = {
        acknowledgedBy: userId,
        acknowledgedAt: new Date(),
        notes: notes
    };
    return this.save();
};
EventSchema.methods.resolve = async function (userId, resolution, notes) {
    this.status = 'resolved';
    this.resolution = {
        resolvedBy: userId,
        resolvedAt: new Date(),
        resolution: resolution,
        notes: notes
    };
    this.endTimestamp = new Date();
    return this.save();
};
EventSchema.methods.addAction = async function (action) {
    this.actions.push({
        ...action,
        timestamp: new Date()
    });
    return this.save();
};
EventSchema.statics.findByCamera = function (cameraId, limit = 50) {
    return this.find({ cameraId })
        .sort({ timestamp: -1 })
        .limit(limit)
        .populate('cameraId', 'name location');
};
EventSchema.statics.findActive = function (limit = 100) {
    return this.find({
        status: { $in: ['active', 'investigating'] },
        isArchived: false
    })
        .sort({ priority: -1, timestamp: -1 })
        .limit(limit)
        .populate('cameraId', 'name location');
};
exports.Event = mongoose_1.default.model('Event', EventSchema);
//# sourceMappingURL=Event.js.map
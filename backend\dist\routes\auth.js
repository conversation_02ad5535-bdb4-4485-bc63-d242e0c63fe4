"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const User_1 = require("../models/User");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
router.post('/register', async (req, res) => {
    try {
        const { username, email, password, firstName, lastName, role = 'viewer' } = req.body;
        const existingUser = await User_1.User.findOne({
            $or: [
                { email: email.toLowerCase() },
                { username: username.toLowerCase() }
            ]
        });
        if (existingUser) {
            res.status(409).json({
                error: 'User already exists with this email or username'
            });
            return;
        }
        const user = new User_1.User({
            username: username.toLowerCase(),
            email: email.toLowerCase(),
            password,
            firstName,
            lastName,
            role
        });
        await user.save();
        const token = (0, auth_1.generateToken)(user);
        const refreshToken = (0, auth_1.generateRefreshToken)(user);
        res.status(201).json({
            message: 'User registered successfully',
            user: user.toJSON(),
            token,
            refreshToken
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({
            error: 'Internal server error during registration'
        });
    }
});
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        const user = await User_1.User.findOne({ email: email.toLowerCase() });
        if (!user || !await user.comparePassword(password)) {
            res.status(401).json({
                error: 'Invalid email or password'
            });
            return;
        }
        const token = (0, auth_1.generateToken)(user);
        const refreshToken = (0, auth_1.generateRefreshToken)(user);
        res.json({
            message: 'Login successful',
            user: user.toJSON(),
            token,
            refreshToken
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            error: 'Internal server error during login'
        });
    }
});
router.post('/refresh', async (req, res) => {
    try {
        const { refreshToken } = req.body;
        if (!refreshToken) {
            res.status(400).json({
                error: 'Refresh token is required'
            });
            return;
        }
        const { userId } = (0, auth_1.verifyRefreshToken)(refreshToken);
        const user = await User_1.User.findById(userId);
        if (!user || !user.isActive) {
            res.status(401).json({
                error: 'Invalid refresh token'
            });
            return;
        }
        const newToken = (0, auth_1.generateToken)(user);
        const newRefreshToken = (0, auth_1.generateRefreshToken)(user);
        res.json({
            token: newToken,
            refreshToken: newRefreshToken
        });
    }
    catch (error) {
        console.error('Token refresh error:', error);
        res.status(401).json({
            error: 'Invalid refresh token'
        });
    }
});
router.post('/logout', auth_1.authenticate, async (req, res) => {
    try {
        res.json({
            message: 'Logout successful'
        });
    }
    catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            error: 'Internal server error during logout'
        });
    }
});
router.get('/me', auth_1.authenticate, async (req, res) => {
    try {
        res.json({
            user: req.user?.toJSON()
        });
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/me', auth_1.authenticate, async (req, res) => {
    try {
        const allowedUpdates = ['firstName', 'lastName', 'preferences'];
        const updates = Object.keys(req.body);
        const isValidOperation = updates.every(update => allowedUpdates.includes(update));
        if (!isValidOperation) {
            res.status(400).json({
                error: 'Invalid updates'
            });
            return;
        }
        const user = req.user;
        updates.forEach(update => {
            if (update === 'preferences') {
                user.preferences = { ...user.preferences, ...req.body.preferences };
            }
            else {
                user[update] = req.body[update];
            }
        });
        await user.save();
        res.json({
            message: 'Profile updated successfully',
            user: user.toJSON()
        });
    }
    catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/change-password', auth_1.authenticate, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const user = req.user;
        const isMatch = await user.comparePassword(currentPassword);
        if (!isMatch) {
            res.status(400).json({
                error: 'Current password is incorrect'
            });
            return;
        }
        user.password = newPassword;
        await user.save();
        res.json({
            message: 'Password changed successfully'
        });
    }
    catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=auth.js.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const Camera_1 = require("../models/Camera");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
const cameraValidation = [
    (0, express_validator_1.body)('name')
        .isLength({ min: 1, max: 100 })
        .withMessage('Camera name is required and must be less than 100 characters'),
    (0, express_validator_1.body)('location')
        .isLength({ min: 1, max: 200 })
        .withMessage('Location is required and must be less than 200 characters'),
    (0, express_validator_1.body)('ipAddress')
        .isIP(4)
        .withMessage('Please provide a valid IPv4 address'),
    (0, express_validator_1.body)('port')
        .isInt({ min: 1, max: 65535 })
        .withMessage('Port must be between 1 and 65535'),
    (0, express_validator_1.body)('username')
        .isLength({ min: 1 })
        .withMessage('Username is required'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 1 })
        .withMessage('Password is required'),
    (0, express_validator_1.body)('cameraModel')
        .isLength({ min: 1 })
        .withMessage('Camera model is required'),
    (0, express_validator_1.body)('resolution')
        .isIn(['720p', '1080p', '4K', '2MP', '4MP', '8MP'])
        .withMessage('Invalid resolution'),
    (0, express_validator_1.body)('fps')
        .isInt({ min: 1, max: 60 })
        .withMessage('FPS must be between 1 and 60')
];
router.get('/', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.query)('status').optional().isIn(['online', 'offline', 'maintenance', 'error']),
    (0, express_validator_1.query)('location').optional().isString(),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const { status, location, limit = 50, page = 1 } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const filter = {};
        if (status)
            filter.status = status;
        if (location)
            filter.location = new RegExp(location, 'i');
        const cameras = await Camera_1.Camera.find(filter)
            .select('-password')
            .sort({ name: 1 })
            .limit(Number(limit))
            .skip(skip);
        const total = await Camera_1.Camera.countDocuments(filter);
        res.json({
            cameras,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Get cameras error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/:id', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id).select('-password');
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        res.json({ camera });
    }
    catch (error) {
        console.error('Get camera error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.post('/', auth_1.authenticate, auth_1.operatorAccess, cameraValidation, async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const existingCamera = await Camera_1.Camera.findOne({ ipAddress: req.body.ipAddress });
        if (existingCamera) {
            res.status(409).json({
                error: 'Camera with this IP address already exists'
            });
            return;
        }
        const camera = new Camera_1.Camera(req.body);
        await camera.save();
        let managerStatus = 'not_available';
        if (req.cameraManager) {
            const success = await req.cameraManager.addCamera(camera);
            managerStatus = success ? 'connected' : 'failed';
        }
        res.status(201).json({
            message: 'Camera created successfully',
            camera: camera.toJSON(),
            isapiStatus: managerStatus,
            note: managerStatus === 'connected' ? 'Camera connected and ready for monitoring' :
                managerStatus === 'failed' ? 'Camera created but ISAPI connection failed' :
                    'Camera created but ISAPI manager not available'
        });
    }
    catch (error) {
        console.error('Create camera error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/:id', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID'),
    (0, express_validator_1.body)('name').optional().isLength({ min: 1, max: 100 }),
    (0, express_validator_1.body)('location').optional().isLength({ min: 1, max: 200 }),
    (0, express_validator_1.body)('ipAddress').optional().isIP(4),
    (0, express_validator_1.body)('port').optional().isInt({ min: 1, max: 65535 }),
    (0, express_validator_1.body)('resolution').optional().isIn(['720p', '1080p', '4K', '2MP', '4MP', '8MP']),
    (0, express_validator_1.body)('fps').optional().isInt({ min: 1, max: 60 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        if (req.body.ipAddress && req.body.ipAddress !== camera.ipAddress) {
            const existingCamera = await Camera_1.Camera.findOne({ ipAddress: req.body.ipAddress });
            if (existingCamera) {
                res.status(409).json({
                    error: 'Camera with this IP address already exists'
                });
                return;
            }
        }
        const allowedUpdates = [
            'name', 'description', 'location', 'ipAddress', 'port', 'username',
            'password', 'cameraModel', 'firmware', 'resolution', 'fps', 'settings',
            'aiSettings', 'position', 'maintenance'
        ];
        const updates = Object.keys(req.body);
        const isValidOperation = updates.every(update => allowedUpdates.includes(update));
        if (!isValidOperation) {
            res.status(400).json({
                error: 'Invalid updates'
            });
            return;
        }
        updates.forEach(update => {
            if (typeof req.body[update] === 'object' && req.body[update] !== null) {
                camera[update] = { ...camera[update], ...req.body[update] };
            }
            else {
                camera[update] = req.body[update];
            }
        });
        await camera.save();
        res.json({
            message: 'Camera updated successfully',
            camera: camera.toJSON()
        });
    }
    catch (error) {
        console.error('Update camera error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.delete('/:id', auth_1.authenticate, (0, auth_1.authorize)('admin'), [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        if (req.cameraManager && req.params.id) {
            req.cameraManager.removeCamera(req.params.id);
        }
        await Camera_1.Camera.findByIdAndDelete(req.params.id);
        res.json({
            message: 'Camera deleted successfully',
            note: 'Camera removed from database and ISAPI manager'
        });
    }
    catch (error) {
        console.error('Delete camera error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.post('/:id/arm', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        let isapiSuccess = false;
        if (req.cameraManager && req.params.id) {
            isapiSuccess = await req.cameraManager.armCamera(req.params.id);
        }
        camera.isArmed = true;
        await camera.save();
        res.json({
            message: 'Camera armed successfully',
            camera: camera.toJSON(),
            isapiStatus: isapiSuccess ? 'success' : 'failed',
            note: isapiSuccess ? 'Motion detection enabled on camera' : 'Database updated, but camera may not be physically armed'
        });
    }
    catch (error) {
        console.error('Arm camera error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.post('/:id/disarm', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        let isapiSuccess = false;
        if (req.cameraManager && req.params.id) {
            isapiSuccess = await req.cameraManager.disarmCamera(req.params.id);
        }
        camera.isArmed = false;
        await camera.save();
        res.json({
            message: 'Camera disarmed successfully',
            camera: camera.toJSON(),
            isapiStatus: isapiSuccess ? 'success' : 'failed',
            note: isapiSuccess ? 'Motion detection disabled on camera' : 'Database updated, but camera may not be physically disarmed'
        });
    }
    catch (error) {
        console.error('Disarm camera error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/:id/recording', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID'),
    (0, express_validator_1.body)('recording').isBoolean().withMessage('Recording status must be boolean')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        camera.isRecording = req.body.recording;
        await camera.save();
        res.json({
            message: `Recording ${req.body.recording ? 'started' : 'stopped'} successfully`,
            camera: camera.toJSON()
        });
    }
    catch (error) {
        console.error('Recording control error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/:id/status', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID'),
    (0, express_validator_1.body)('status').isIn(['online', 'offline', 'maintenance', 'error']).withMessage('Invalid status')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        await camera.updateStatus(req.body.status);
        res.json({
            message: 'Camera status updated successfully',
            camera: camera.toJSON()
        });
    }
    catch (error) {
        console.error('Update camera status error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.post('/:id/heartbeat', auth_1.authenticate, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        await camera.updateHeartbeat();
        res.json({
            message: 'Heartbeat updated successfully'
        });
    }
    catch (error) {
        console.error('Camera heartbeat error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/:id/status', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        let isapiStatus = null;
        if (req.cameraManager && req.params.id) {
            isapiStatus = await req.cameraManager.getCameraStatus(req.params.id);
        }
        const connectionStatus = req.cameraManager && req.params.id ?
            req.cameraManager.getConnectionStatus()[req.params.id] : null;
        res.json({
            camera: {
                id: camera._id,
                name: camera.name,
                status: camera.status,
                isArmed: camera.isArmed,
                lastSeen: camera.lastSeen,
                lastHeartbeat: camera.lastHeartbeat
            },
            realTimeStatus: isapiStatus,
            connectionStatus: connectionStatus
        });
    }
    catch (error) {
        console.error('Get camera status error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/:id/snapshot', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        if (!req.cameraManager) {
            res.status(503).json({
                error: 'Camera manager not available'
            });
            return;
        }
        const snapshot = req.params.id ? await req.cameraManager.captureSnapshot(req.params.id) : null;
        if (!snapshot) {
            res.status(500).json({
                error: 'Failed to capture snapshot'
            });
            return;
        }
        res.set({
            'Content-Type': 'image/jpeg',
            'Content-Length': snapshot.length.toString(),
            'Cache-Control': 'no-cache'
        });
        res.send(snapshot);
    }
    catch (error) {
        console.error('Capture snapshot error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/:id/stream', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID'),
    (0, express_validator_1.query)('channel').optional().isInt({ min: 101, max: 199 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        if (!req.cameraManager) {
            res.json({
                streamUrl: camera.network.rtspUrl || null,
                source: 'database'
            });
            return;
        }
        const channel = parseInt(req.query.channel) || 101;
        const streamUrl = req.params.id ? req.cameraManager.getRTSPStreamURL(req.params.id, channel) : null;
        res.json({
            streamUrl: streamUrl,
            channel: channel,
            source: 'isapi'
        });
    }
    catch (error) {
        console.error('Get stream URL error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/:id/stats', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid camera ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.params.id).select('statistics name location');
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        res.json({
            cameraId: camera._id,
            name: camera.name,
            location: camera.location,
            statistics: camera.statistics
        });
    }
    catch (error) {
        console.error('Get camera statistics error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=cameras.js.map
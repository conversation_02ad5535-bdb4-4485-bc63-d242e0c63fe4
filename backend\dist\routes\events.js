"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const Event_1 = require("../models/Event");
const Camera_1 = require("../models/Camera");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
router.get('/', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.query)('status').optional().isIn(['active', 'acknowledged', 'resolved', 'dismissed', 'investigating']),
    (0, express_validator_1.query)('type').optional().isIn(['motion', 'person', 'vehicle', 'face', 'intrusion', 'object', 'audio', 'system']),
    (0, express_validator_1.query)('severity').optional().isIn(['low', 'medium', 'high', 'critical']),
    (0, express_validator_1.query)('cameraId').optional().isMongoId(),
    (0, express_validator_1.query)('location').optional().isString(),
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601(),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 }),
    (0, express_validator_1.query)('sortBy').optional().isIn(['timestamp', 'severity', 'confidence', 'status']),
    (0, express_validator_1.query)('sortOrder').optional().isIn(['asc', 'desc'])
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const { status, type, severity, cameraId, location, startDate, endDate, limit = 50, page = 1, sortBy = 'timestamp', sortOrder = 'desc' } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const filter = { isArchived: false };
        if (status)
            filter.status = status;
        if (type)
            filter.type = type;
        if (severity)
            filter.severity = severity;
        if (cameraId)
            filter.cameraId = cameraId;
        if (location)
            filter.location = new RegExp(location, 'i');
        if (startDate || endDate) {
            filter.timestamp = {};
            if (startDate)
                filter.timestamp.$gte = new Date(startDate);
            if (endDate)
                filter.timestamp.$lte = new Date(endDate);
        }
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const events = await Event_1.Event.find(filter)
            .sort(sort)
            .limit(Number(limit))
            .skip(skip)
            .populate('cameraId', 'name location')
            .populate('acknowledgment.acknowledgedBy', 'firstName lastName')
            .populate('resolution.resolvedBy', 'firstName lastName');
        const total = await Event_1.Event.countDocuments(filter);
        res.json({
            events,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Get events error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/:id', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid event ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const event = await Event_1.Event.findById(req.params.id)
            .populate('cameraId', 'name location ipAddress')
            .populate('acknowledgment.acknowledgedBy', 'firstName lastName email')
            .populate('resolution.resolvedBy', 'firstName lastName email');
        if (!event) {
            res.status(404).json({
                error: 'Event not found'
            });
            return;
        }
        res.json({ event });
    }
    catch (error) {
        console.error('Get event error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.post('/', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.body)('type').isIn(['motion', 'person', 'vehicle', 'face', 'intrusion', 'object', 'audio', 'system']),
    (0, express_validator_1.body)('title').isLength({ min: 1, max: 200 }),
    (0, express_validator_1.body)('description').isLength({ min: 1, max: 1000 }),
    (0, express_validator_1.body)('severity').isIn(['low', 'medium', 'high', 'critical']),
    (0, express_validator_1.body)('cameraId').isMongoId(),
    (0, express_validator_1.body)('confidence').isFloat({ min: 0, max: 100 }),
    (0, express_validator_1.body)('timestamp').optional().isISO8601()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const camera = await Camera_1.Camera.findById(req.body.cameraId);
        if (!camera) {
            res.status(404).json({
                error: 'Camera not found'
            });
            return;
        }
        const eventData = {
            ...req.body,
            cameraName: camera.name,
            location: camera.location,
            timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date()
        };
        const event = new Event_1.Event(eventData);
        await event.save();
        camera.statistics.totalEvents += 1;
        camera.statistics.eventsToday += 1;
        await camera.save();
        res.status(201).json({
            message: 'Event created successfully',
            event
        });
    }
    catch (error) {
        console.error('Create event error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/:id/acknowledge', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid event ID'),
    (0, express_validator_1.body)('notes').optional().isLength({ max: 500 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const event = await Event_1.Event.findById(req.params.id);
        if (!event) {
            res.status(404).json({
                error: 'Event not found'
            });
            return;
        }
        if (event.status !== 'active') {
            res.status(400).json({
                error: 'Only active events can be acknowledged'
            });
            return;
        }
        await event.acknowledge(req.user._id, req.body.notes);
        res.json({
            message: 'Event acknowledged successfully',
            event
        });
    }
    catch (error) {
        console.error('Acknowledge event error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/:id/resolve', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid event ID'),
    (0, express_validator_1.body)('resolution').isLength({ min: 1, max: 500 }),
    (0, express_validator_1.body)('notes').optional().isLength({ max: 1000 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const event = await Event_1.Event.findById(req.params.id);
        if (!event) {
            res.status(404).json({
                error: 'Event not found'
            });
            return;
        }
        if (event.status === 'resolved') {
            res.status(400).json({
                error: 'Event is already resolved'
            });
            return;
        }
        await event.resolve(req.user._id, req.body.resolution, req.body.notes);
        res.json({
            message: 'Event resolved successfully',
            event
        });
    }
    catch (error) {
        console.error('Resolve event error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/:id/status', auth_1.authenticate, auth_1.operatorAccess, [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid event ID'),
    (0, express_validator_1.body)('status').isIn(['active', 'acknowledged', 'resolved', 'dismissed', 'investigating'])
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const event = await Event_1.Event.findById(req.params.id);
        if (!event) {
            res.status(404).json({
                error: 'Event not found'
            });
            return;
        }
        event.status = req.body.status;
        await event.save();
        res.json({
            message: 'Event status updated successfully',
            event
        });
    }
    catch (error) {
        console.error('Update event status error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.patch('/:id/archive', auth_1.authenticate, (0, auth_1.authorize)('admin'), [
    (0, express_validator_1.param)('id').isMongoId().withMessage('Invalid event ID')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const event = await Event_1.Event.findById(req.params.id);
        if (!event) {
            res.status(404).json({
                error: 'Event not found'
            });
            return;
        }
        event.isArchived = true;
        await event.save();
        res.json({
            message: 'Event archived successfully'
        });
    }
    catch (error) {
        console.error('Archive event error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/camera/:cameraId', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.param)('cameraId').isMongoId().withMessage('Invalid camera ID'),
    (0, express_validator_1.query)('limit').optional().isInt({ min: 1, max: 100 }),
    (0, express_validator_1.query)('page').optional().isInt({ min: 1 })
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const { limit = 50, page = 1 } = req.query;
        const skip = (Number(page) - 1) * Number(limit);
        const events = await Event_1.Event.find({
            cameraId: req.params.cameraId,
            isArchived: false
        })
            .sort({ timestamp: -1 })
            .limit(Number(limit))
            .skip(skip);
        const total = await Event_1.Event.countDocuments({
            cameraId: req.params.cameraId,
            isArchived: false
        });
        res.json({
            events,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total,
                pages: Math.ceil(total / Number(limit))
            }
        });
    }
    catch (error) {
        console.error('Get camera events error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
router.get('/stats/summary', auth_1.authenticate, auth_1.viewerAccess, [
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601()
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const { startDate, endDate } = req.query;
        const filter = { isArchived: false };
        if (startDate || endDate) {
            filter.timestamp = {};
            if (startDate)
                filter.timestamp.$gte = new Date(startDate);
            if (endDate)
                filter.timestamp.$lte = new Date(endDate);
        }
        const [totalEvents, activeEvents, eventsByType, eventsBySeverity, eventsByStatus] = await Promise.all([
            Event_1.Event.countDocuments(filter),
            Event_1.Event.countDocuments({ ...filter, status: 'active' }),
            Event_1.Event.aggregate([
                { $match: filter },
                { $group: { _id: '$type', count: { $sum: 1 } } }
            ]),
            Event_1.Event.aggregate([
                { $match: filter },
                { $group: { _id: '$severity', count: { $sum: 1 } } }
            ]),
            Event_1.Event.aggregate([
                { $match: filter },
                { $group: { _id: '$status', count: { $sum: 1 } } }
            ])
        ]);
        res.json({
            summary: {
                totalEvents,
                activeEvents,
                resolvedEvents: totalEvents - activeEvents
            },
            breakdown: {
                byType: eventsByType,
                bySeverity: eventsBySeverity,
                byStatus: eventsByStatus
            }
        });
    }
    catch (error) {
        console.error('Get event statistics error:', error);
        res.status(500).json({
            error: 'Internal server error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=events.js.map
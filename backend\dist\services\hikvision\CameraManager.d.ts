import { EventEmitter } from 'events';
import winston from 'winston';
import { ISAPIClient, CameraStatus } from './ISAPIClient';
import { EventListener } from './EventListener';
import { ICamera } from '../../models/Camera';
export interface CameraConnection {
    cameraId: string;
    isApiClient: ISAPIClient;
    eventListener: EventListener;
    isConnected: boolean;
    lastHeartbeat: Date;
    connectionAttempts: number;
}
export declare class CameraManager extends EventEmitter {
    private connections;
    private logger;
    private heartbeatInterval;
    private heartbeatIntervalMs;
    constructor(logger: winston.Logger);
    addCamera(camera: ICamera): Promise<boolean>;
    removeCamera(cameraId: string): boolean;
    armCamera(cameraId: string): Promise<boolean>;
    disarmCamera(cameraId: string): Promise<boolean>;
    getCameraStatus(cameraId: string): Promise<CameraStatus | null>;
    captureSnapshot(cameraId: string): Promise<Buffer | null>;
    getRTSPStreamURL(cameraId: string, channel?: number): string | null;
    getConnectedCameras(): string[];
    getConnectionStatus(): {
        [cameraId: string]: any;
    };
    private handleCameraEvent;
    private mapEventSeverity;
    private updateCameraStatus;
    private updateCameraCapabilities;
    private startHeartbeatMonitoring;
    stop(): void;
}
//# sourceMappingURL=CameraManager.d.ts.map
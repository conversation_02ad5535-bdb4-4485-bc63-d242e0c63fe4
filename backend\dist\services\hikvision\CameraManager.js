"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CameraManager = void 0;
const events_1 = require("events");
const ISAPIClient_1 = require("./ISAPIClient");
const EventListener_1 = require("./EventListener");
const Event_1 = require("../../models/Event");
const mongoose_1 = __importDefault(require("mongoose"));
class CameraManager extends events_1.EventEmitter {
    connections = new Map();
    logger;
    heartbeatInterval = null;
    heartbeatIntervalMs = 30000;
    constructor(logger) {
        super();
        this.logger = logger;
        this.startHeartbeatMonitoring();
    }
    async addCamera(camera) {
        const cameraId = camera._id?.toString();
        if (!cameraId) {
            this.logger.error('Camera ID is required');
            return false;
        }
        try {
            const credentials = {
                username: camera.username,
                password: camera.password,
                ipAddress: camera.ipAddress,
                port: camera.port
            };
            const isapiClient = new ISAPIClient_1.ISAPIClient(credentials, this.logger);
            const isConnected = await isapiClient.testConnection();
            if (!isConnected) {
                this.logger.error(`Failed to connect to camera ${camera._id} at ${camera.ipAddress}:${camera.port}`);
                return false;
            }
            const eventListener = new EventListener_1.EventListener(credentials, cameraId, this.logger);
            eventListener.on('event', (event) => {
                this.handleCameraEvent(cameraId, event);
            });
            eventListener.on('connected', () => {
                this.logger.info(`Event listener connected for camera ${cameraId}`);
                this.updateCameraStatus(cameraId, 'online');
            });
            eventListener.on('disconnected', (reason) => {
                this.logger.warn(`Event listener disconnected for camera ${cameraId}: ${reason}`);
                this.updateCameraStatus(cameraId, 'offline');
            });
            const connection = {
                cameraId: cameraId,
                isApiClient: isapiClient,
                eventListener: eventListener,
                isConnected: true,
                lastHeartbeat: new Date(),
                connectionAttempts: 0
            };
            this.connections.set(cameraId, connection);
            await eventListener.startListening();
            try {
                const capabilities = await isapiClient.getDeviceInfo();
                await this.updateCameraCapabilities(cameraId, capabilities);
            }
            catch (error) {
                this.logger.warn(`Failed to get capabilities for camera ${cameraId}:`, error);
            }
            this.logger.info(`Successfully added camera ${cameraId} (${camera.name}) to manager`);
            this.emit('cameraAdded', cameraId);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to add camera ${cameraId}:`, error);
            return false;
        }
    }
    removeCamera(cameraId) {
        const connection = this.connections.get(cameraId);
        if (!connection) {
            return false;
        }
        connection.eventListener.stopListening();
        this.connections.delete(cameraId);
        this.logger.info(`Removed camera ${cameraId} from manager`);
        this.emit('cameraRemoved', cameraId);
        return true;
    }
    async armCamera(cameraId) {
        const connection = this.connections.get(cameraId);
        if (!connection) {
            this.logger.error(`Camera ${cameraId} not found in manager`);
            return false;
        }
        try {
            const success = await connection.isApiClient.armCamera();
            if (success) {
                this.logger.info(`Successfully armed camera ${cameraId}`);
                this.emit('cameraArmed', cameraId);
            }
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to arm camera ${cameraId}:`, error);
            return false;
        }
    }
    async disarmCamera(cameraId) {
        const connection = this.connections.get(cameraId);
        if (!connection) {
            this.logger.error(`Camera ${cameraId} not found in manager`);
            return false;
        }
        try {
            const success = await connection.isApiClient.disarmCamera();
            if (success) {
                this.logger.info(`Successfully disarmed camera ${cameraId}`);
                this.emit('cameraDisarmed', cameraId);
            }
            return success;
        }
        catch (error) {
            this.logger.error(`Failed to disarm camera ${cameraId}:`, error);
            return false;
        }
    }
    async getCameraStatus(cameraId) {
        const connection = this.connections.get(cameraId);
        if (!connection) {
            return null;
        }
        try {
            return await connection.isApiClient.getCameraStatus();
        }
        catch (error) {
            this.logger.error(`Failed to get status for camera ${cameraId}:`, error);
            return null;
        }
    }
    async captureSnapshot(cameraId) {
        const connection = this.connections.get(cameraId);
        if (!connection) {
            return null;
        }
        try {
            return await connection.isApiClient.captureSnapshot();
        }
        catch (error) {
            this.logger.error(`Failed to capture snapshot for camera ${cameraId}:`, error);
            return null;
        }
    }
    getRTSPStreamURL(cameraId, channel = 101) {
        const connection = this.connections.get(cameraId);
        if (!connection) {
            return null;
        }
        return connection.isApiClient.getRTSPStreamURL(channel);
    }
    getConnectedCameras() {
        return Array.from(this.connections.keys()).filter(cameraId => {
            const connection = this.connections.get(cameraId);
            return connection?.isConnected;
        });
    }
    getConnectionStatus() {
        const status = {};
        for (const [cameraId, connection] of this.connections) {
            status[cameraId] = {
                isConnected: connection.isConnected,
                lastHeartbeat: connection.lastHeartbeat,
                connectionAttempts: connection.connectionAttempts,
                eventListenerStatus: connection.eventListener.getStatus()
            };
        }
        return status;
    }
    async handleCameraEvent(cameraId, event) {
        try {
            const newEvent = new Event_1.Event({
                eventId: `${cameraId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type: event.eventType,
                title: `${event.eventType.charAt(0).toUpperCase() + event.eventType.slice(1)} Detection`,
                description: event.description,
                severity: this.mapEventSeverity(event.eventType),
                status: 'active',
                cameraId: new mongoose_1.default.Types.ObjectId(cameraId),
                timestamp: event.timestamp,
                metadata: {
                    channelId: event.channelId,
                    region: event.region,
                    confidence: event.confidence,
                    ...event.metadata
                }
            });
            await newEvent.save();
            this.logger.info(`Created event ${newEvent.eventId} for camera ${cameraId}`);
            this.emit('cameraEvent', cameraId, newEvent);
        }
        catch (error) {
            this.logger.error(`Failed to handle camera event for ${cameraId}:`, error);
        }
    }
    mapEventSeverity(eventType) {
        const severityMap = {
            'motion': 'low',
            'intrusion': 'high',
            'lineDetection': 'medium',
            'faceDetection': 'medium',
            'vehicleDetection': 'medium',
            'tamperDetection': 'critical'
        };
        return severityMap[eventType] || 'medium';
    }
    async updateCameraStatus(cameraId, status) {
        try {
            const Camera = mongoose_1.default.model('Camera');
            await Camera.findByIdAndUpdate(cameraId, {
                status: status,
                lastSeen: new Date(),
                lastHeartbeat: new Date()
            });
        }
        catch (error) {
            this.logger.error(`Failed to update camera status for ${cameraId}:`, error);
        }
    }
    async updateCameraCapabilities(cameraId, capabilities) {
        try {
            const Camera = mongoose_1.default.model('Camera');
            await Camera.findByIdAndUpdate(cameraId, {
                firmware: capabilities.deviceInfo.firmwareVersion,
                'network.rtspUrl': `rtsp://username:password@ip:554/Streaming/Channels/101`,
                'statistics.lastReboot': new Date()
            });
        }
        catch (error) {
            this.logger.error(`Failed to update camera capabilities for ${cameraId}:`, error);
        }
    }
    startHeartbeatMonitoring() {
        this.heartbeatInterval = setInterval(async () => {
            for (const [cameraId, connection] of this.connections) {
                try {
                    const status = await connection.isApiClient.getCameraStatus();
                    if (status) {
                        connection.lastHeartbeat = new Date();
                        connection.isConnected = true;
                        await this.updateCameraStatus(cameraId, 'online');
                    }
                }
                catch (error) {
                    connection.isConnected = false;
                    await this.updateCameraStatus(cameraId, 'offline');
                    this.logger.warn(`Heartbeat failed for camera ${cameraId}:`, error);
                }
            }
        }, this.heartbeatIntervalMs);
    }
    stop() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        for (const connection of this.connections.values()) {
            connection.eventListener.stopListening();
        }
        this.connections.clear();
        this.logger.info('Camera manager stopped');
    }
}
exports.CameraManager = CameraManager;
//# sourceMappingURL=CameraManager.js.map
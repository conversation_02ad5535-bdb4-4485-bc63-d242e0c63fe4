import { EventEmitter } from 'events';
import winston from 'winston';
import { HikVisionCredentials } from './ISAPIClient';
export interface CameraEvent {
    cameraId: string;
    eventType: 'motion' | 'intrusion' | 'lineDetection' | 'faceDetection' | 'vehicleDetection' | 'tamperDetection';
    eventState: 'active' | 'inactive';
    channelId: number;
    timestamp: Date;
    description: string;
    confidence?: number;
    region?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
    metadata?: any;
}
export declare class EventListener extends EventEmitter {
    private credentials;
    private logger;
    private axiosInstance;
    private isListening;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    private eventStream;
    private cameraId;
    constructor(credentials: HikVisionCredentials, cameraId: string, logger: winston.Logger);
    startListening(): Promise<void>;
    stopListening(): void;
    private connectToEventStream;
    private processEventBuffer;
    private parseAndEmitEvent;
    private mapEventType;
    private handleReconnection;
    getStatus(): {
        isListening: boolean;
        reconnectAttempts: number;
        isConnected: boolean;
    };
}
//# sourceMappingURL=EventListener.d.ts.map
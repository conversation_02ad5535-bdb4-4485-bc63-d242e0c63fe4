"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventListener = void 0;
const axios_1 = __importDefault(require("axios"));
const events_1 = require("events");
const xml2js_1 = require("xml2js");
const util_1 = require("util");
const parseXML = (0, util_1.promisify)(xml2js_1.parseString);
class EventListener extends events_1.EventEmitter {
    credentials;
    logger;
    axiosInstance;
    isListening = false;
    reconnectAttempts = 0;
    maxReconnectAttempts = 5;
    reconnectDelay = 5000;
    eventStream = null;
    cameraId;
    constructor(credentials, cameraId, logger) {
        super();
        this.credentials = credentials;
        this.cameraId = cameraId;
        this.logger = logger;
        this.axiosInstance = axios_1.default.create({
            baseURL: `http://${credentials.ipAddress}:${credentials.port}`,
            timeout: 0,
            auth: {
                username: credentials.username,
                password: credentials.password
            }
        });
    }
    async startListening() {
        if (this.isListening) {
            this.logger.warn(`Event listener already running for camera ${this.cameraId}`);
            return;
        }
        this.isListening = true;
        this.logger.info(`Starting event listener for camera ${this.cameraId}`);
        try {
            await this.connectToEventStream();
        }
        catch (error) {
            this.logger.error(`Failed to start event listener for camera ${this.cameraId}:`, error);
            this.handleReconnection();
        }
    }
    stopListening() {
        this.isListening = false;
        this.reconnectAttempts = 0;
        if (this.eventStream) {
            this.eventStream.destroy();
            this.eventStream = null;
        }
        this.logger.info(`Stopped event listener for camera ${this.cameraId}`);
    }
    async connectToEventStream() {
        try {
            const response = await this.axiosInstance.get('/ISAPI/Event/notification/alertStream', {
                responseType: 'stream',
                headers: {
                    'Accept': 'multipart/x-mixed-replace'
                }
            });
            this.eventStream = response.data;
            this.reconnectAttempts = 0;
            let buffer = '';
            this.eventStream.on('data', (chunk) => {
                buffer += chunk.toString();
                this.processEventBuffer(buffer);
            });
            this.eventStream.on('end', () => {
                this.logger.warn(`Event stream ended for camera ${this.cameraId}`);
                if (this.isListening) {
                    this.handleReconnection();
                }
            });
            this.eventStream.on('error', (error) => {
                this.logger.error(`Event stream error for camera ${this.cameraId}:`, error);
                if (this.isListening) {
                    this.handleReconnection();
                }
            });
            this.logger.info(`Connected to event stream for camera ${this.cameraId}`);
            this.emit('connected');
        }
        catch (error) {
            this.logger.error(`Failed to connect to event stream for camera ${this.cameraId}:`, error);
            throw error;
        }
    }
    processEventBuffer(buffer) {
        const events = buffer.split('\r\n\r\n');
        for (const eventData of events) {
            if (eventData.trim() && eventData.includes('<EventNotificationAlert>')) {
                this.parseAndEmitEvent(eventData);
            }
        }
    }
    async parseAndEmitEvent(xmlData) {
        try {
            const parsedData = await parseXML(xmlData);
            const alert = parsedData?.EventNotificationAlert;
            if (!alert) {
                return;
            }
            const cameraEvent = {
                cameraId: this.cameraId,
                eventType: this.mapEventType(alert.eventType),
                eventState: alert.eventState === 'active' ? 'active' : 'inactive',
                channelId: parseInt(alert.channelID) || 1,
                timestamp: new Date(alert.dateTime),
                description: alert.eventDescription || 'Camera event detected',
                metadata: {
                    activePostCount: parseInt(alert.activePostCount) || 0,
                    eventId: alert.eventId,
                    rawEventType: alert.eventType
                }
            };
            if (alert.DetectionRegionList?.DetectionRegionEntry) {
                const region = alert.DetectionRegionList.DetectionRegionEntry;
                cameraEvent.region = {
                    x: parseInt(region.regionCoordinatesList?.regionCoordinates?.positionX) || 0,
                    y: parseInt(region.regionCoordinatesList?.regionCoordinates?.positionY) || 0,
                    width: parseInt(region.regionCoordinatesList?.regionCoordinates?.width) || 0,
                    height: parseInt(region.regionCoordinatesList?.regionCoordinates?.height) || 0
                };
            }
            this.logger.debug(`Received event for camera ${this.cameraId}:`, cameraEvent);
            this.emit('event', cameraEvent);
        }
        catch (error) {
            this.logger.error(`Failed to parse event data for camera ${this.cameraId}:`, error);
        }
    }
    mapEventType(hikVisionEventType) {
        const eventTypeMap = {
            'VMD': 'motion',
            'videomotion': 'motion',
            'motiondetection': 'motion',
            'fielddetection': 'intrusion',
            'regionEntrance': 'intrusion',
            'regionExiting': 'intrusion',
            'linedetection': 'lineDetection',
            'crossLineDetection': 'lineDetection',
            'facedetection': 'faceDetection',
            'vehicleDetection': 'vehicleDetection',
            'tamperdetection': 'tamperDetection',
            'videoloss': 'tamperDetection',
            'shelteralarm': 'tamperDetection'
        };
        return eventTypeMap[hikVisionEventType] || 'motion';
    }
    handleReconnection() {
        if (!this.isListening || this.reconnectAttempts >= this.maxReconnectAttempts) {
            this.logger.error(`Max reconnection attempts reached for camera ${this.cameraId}`);
            this.emit('disconnected', 'max_attempts_reached');
            return;
        }
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * this.reconnectAttempts;
        this.logger.info(`Attempting to reconnect to camera ${this.cameraId} (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
        setTimeout(async () => {
            if (this.isListening) {
                try {
                    await this.connectToEventStream();
                }
                catch (error) {
                    this.logger.error(`Reconnection attempt ${this.reconnectAttempts} failed for camera ${this.cameraId}:`, error);
                    this.handleReconnection();
                }
            }
        }, delay);
    }
    getStatus() {
        return {
            isListening: this.isListening,
            reconnectAttempts: this.reconnectAttempts,
            isConnected: this.eventStream !== null && !this.eventStream.destroyed
        };
    }
}
exports.EventListener = EventListener;
//# sourceMappingURL=EventListener.js.map
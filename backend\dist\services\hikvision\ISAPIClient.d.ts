import winston from 'winston';
export interface HikVisionCredentials {
    username: string;
    password: string;
    ipAddress: string;
    port: number;
}
export interface CameraCapabilities {
    deviceInfo: {
        deviceName: string;
        deviceID: string;
        model: string;
        serialNumber: string;
        macAddress: string;
        firmwareVersion: string;
        firmwareReleasedDate: string;
    };
    systemCapabilities: {
        isSupportLocalOutputTerminal: boolean;
        isSupportCentralizeManagement: boolean;
        isSupportAlarmServer: boolean;
        isSupportCentralizeConfig: boolean;
    };
    videoInputChannels: number;
    audioInputChannels: number;
    videoOutputChannels: number;
    audioOutputChannels: number;
}
export interface CameraStatus {
    deviceStatus: 'online' | 'offline' | 'error';
    cpuUsage: number;
    memoryUsage: number;
    temperature: number;
    uptime: number;
    recordingStatus: boolean;
    motionDetectionEnabled: boolean;
    isArmed: boolean;
}
export interface MotionDetectionRegion {
    id: number;
    enabled: boolean;
    regionType: string;
    coordinatesList: Array<{
        positionX: number;
        positionY: number;
    }>;
    sensitivityLevel: number;
}
export interface EventNotification {
    eventType: string;
    eventState: string;
    eventDescription: string;
    channelID: number;
    dateTime: string;
    activePostCount: number;
    eventId: string;
}
export declare class ISAPIClient {
    private axiosInstance;
    private credentials;
    private logger;
    private digestAuth;
    constructor(credentials: HikVisionCredentials, logger: winston.Logger);
    private handleUnauthorized;
    private parseDigestAuthHeader;
    private addDigestAuth;
    testConnection(): Promise<boolean>;
    getDeviceInfo(): Promise<CameraCapabilities>;
    getCameraStatus(): Promise<CameraStatus>;
    armCamera(): Promise<boolean>;
    disarmCamera(): Promise<boolean>;
    getMotionDetectionRegions(): Promise<MotionDetectionRegion[]>;
    captureSnapshot(): Promise<Buffer | null>;
    getRTSPStreamURL(channel?: number): string;
    private parseXMLResponse;
}
//# sourceMappingURL=ISAPIClient.d.ts.map
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ISAPIClient = void 0;
const axios_1 = __importDefault(require("axios"));
const crypto_1 = __importDefault(require("crypto"));
const xml2js_1 = require("xml2js");
const util_1 = require("util");
const parseXML = (0, util_1.promisify)(xml2js_1.parseString);
class ISAPIClient {
    axiosInstance;
    credentials;
    logger;
    digestAuth = {};
    constructor(credentials, logger) {
        this.credentials = credentials;
        this.logger = logger;
        this.axiosInstance = axios_1.default.create({
            baseURL: `http://${credentials.ipAddress}:${credentials.port}`,
            timeout: 10000,
            headers: {
                'Content-Type': 'application/xml',
                'Accept': 'application/xml, text/xml, */*'
            }
        });
        this.axiosInstance.interceptors.request.use((config) => {
            this.addDigestAuth(config);
            return config;
        }, (error) => Promise.reject(error));
        this.axiosInstance.interceptors.response.use((response) => response, async (error) => {
            if (error.response?.status === 401 && !error.config._retry) {
                error.config._retry = true;
                await this.handleUnauthorized(error.response);
                return this.axiosInstance.request(error.config);
            }
            return Promise.reject(error);
        });
    }
    async handleUnauthorized(response) {
        const authHeader = response.headers['www-authenticate'];
        if (authHeader && authHeader.startsWith('Digest ')) {
            this.parseDigestAuthHeader(authHeader);
        }
    }
    parseDigestAuthHeader(authHeader) {
        const digestParams = authHeader.substring(7);
        const params = digestParams.split(',').reduce((acc, param) => {
            const [key, value] = param.split('=').map(s => s.trim());
            if (key && value) {
                acc[key] = value.replace(/"/g, '');
            }
            return acc;
        }, {});
        this.digestAuth = {
            realm: params.realm,
            nonce: params.nonce,
            qop: params.qop,
            opaque: params.opaque,
            algorithm: params.algorithm || 'MD5'
        };
    }
    addDigestAuth(config) {
        if (!this.digestAuth.nonce) {
            return config;
        }
        const method = config.method?.toUpperCase() || 'GET';
        const uri = config.url || '';
        const nc = '00000001';
        const cnonce = crypto_1.default.randomBytes(16).toString('hex');
        const ha1 = crypto_1.default
            .createHash('md5')
            .update(`${this.credentials.username}:${this.digestAuth.realm}:${this.credentials.password}`)
            .digest('hex');
        const ha2 = crypto_1.default
            .createHash('md5')
            .update(`${method}:${uri}`)
            .digest('hex');
        let response;
        if (this.digestAuth.qop === 'auth') {
            response = crypto_1.default
                .createHash('md5')
                .update(`${ha1}:${this.digestAuth.nonce}:${nc}:${cnonce}:${this.digestAuth.qop}:${ha2}`)
                .digest('hex');
        }
        else {
            response = crypto_1.default
                .createHash('md5')
                .update(`${ha1}:${this.digestAuth.nonce}:${ha2}`)
                .digest('hex');
        }
        let authHeader = `Digest username="${this.credentials.username}", realm="${this.digestAuth.realm}", nonce="${this.digestAuth.nonce}", uri="${uri}", response="${response}"`;
        if (this.digestAuth.qop) {
            authHeader += `, qop=${this.digestAuth.qop}, nc=${nc}, cnonce="${cnonce}"`;
        }
        if (this.digestAuth.opaque) {
            authHeader += `, opaque="${this.digestAuth.opaque}"`;
        }
        config.headers = {
            ...config.headers,
            Authorization: authHeader
        };
        return config;
    }
    async testConnection() {
        try {
            const response = await this.axiosInstance.get('/ISAPI/System/deviceInfo');
            return response.status === 200;
        }
        catch (error) {
            this.logger.error('Connection test failed:', error);
            return false;
        }
    }
    async getDeviceInfo() {
        try {
            const response = await this.axiosInstance.get('/ISAPI/System/deviceInfo');
            const deviceInfo = await this.parseXMLResponse(response.data);
            const capResponse = await this.axiosInstance.get('/ISAPI/System/capabilities');
            const capabilities = await this.parseXMLResponse(capResponse.data);
            return {
                deviceInfo: {
                    deviceName: deviceInfo.DeviceInfo?.deviceName?.[0] || 'Unknown',
                    deviceID: deviceInfo.DeviceInfo?.deviceID?.[0] || 'Unknown',
                    model: deviceInfo.DeviceInfo?.model?.[0] || 'Unknown',
                    serialNumber: deviceInfo.DeviceInfo?.serialNumber?.[0] || 'Unknown',
                    macAddress: deviceInfo.DeviceInfo?.macAddress?.[0] || 'Unknown',
                    firmwareVersion: deviceInfo.DeviceInfo?.firmwareVersion?.[0] || 'Unknown',
                    firmwareReleasedDate: deviceInfo.DeviceInfo?.firmwareReleasedDate?.[0] || 'Unknown'
                },
                systemCapabilities: {
                    isSupportLocalOutputTerminal: capabilities.DeviceCap?.SysCap?.isSupportLocalOutputTerminal?.[0] === 'true',
                    isSupportCentralizeManagement: capabilities.DeviceCap?.SysCap?.isSupportCentralizeManagement?.[0] === 'true',
                    isSupportAlarmServer: capabilities.DeviceCap?.SysCap?.isSupportAlarmServer?.[0] === 'true',
                    isSupportCentralizeConfig: capabilities.DeviceCap?.SysCap?.isSupportCentralizeConfig?.[0] === 'true'
                },
                videoInputChannels: parseInt(capabilities.DeviceCap?.VideoCap?.videoInputPortNums?.[0] || '0'),
                audioInputChannels: parseInt(capabilities.DeviceCap?.AudioCap?.audioInputPortNums?.[0] || '0'),
                videoOutputChannels: parseInt(capabilities.DeviceCap?.VideoCap?.videoOutputPortNums?.[0] || '0'),
                audioOutputChannels: parseInt(capabilities.DeviceCap?.AudioCap?.audioOutputPortNums?.[0] || '0')
            };
        }
        catch (error) {
            this.logger.error('Failed to get device info:', error);
            throw new Error('Failed to retrieve device information');
        }
    }
    async getCameraStatus() {
        try {
            const statusResponse = await this.axiosInstance.get('/ISAPI/System/status');
            const statusData = await this.parseXMLResponse(statusResponse.data);
            const motionResponse = await this.axiosInstance.get('/ISAPI/System/Video/inputs/channels/1/motionDetection');
            const motionData = await this.parseXMLResponse(motionResponse.data);
            return {
                deviceStatus: 'online',
                cpuUsage: parseInt(statusData.DeviceStatus?.cpuUsage?.[0] || '0'),
                memoryUsage: parseInt(statusData.DeviceStatus?.memoryUsage?.[0] || '0'),
                temperature: parseInt(statusData.DeviceStatus?.temperature?.[0] || '0'),
                uptime: parseInt(statusData.DeviceStatus?.upTime?.[0] || '0'),
                recordingStatus: statusData.DeviceStatus?.recordingStatus?.[0] === 'true',
                motionDetectionEnabled: motionData.MotionDetection?.enabled?.[0] === 'true',
                isArmed: motionData.MotionDetection?.enabled?.[0] === 'true'
            };
        }
        catch (error) {
            this.logger.error('Failed to get camera status:', error);
            return {
                deviceStatus: 'error',
                cpuUsage: 0,
                memoryUsage: 0,
                temperature: 0,
                uptime: 0,
                recordingStatus: false,
                motionDetectionEnabled: false,
                isArmed: false
            };
        }
    }
    async armCamera() {
        try {
            const motionDetectionXML = `<?xml version="1.0" encoding="UTF-8"?>
<MotionDetection>
  <enabled>true</enabled>
  <enableHighlight>false</enableHighlight>
  <samplingInterval>2</samplingInterval>
  <startTriggerTime>500</startTriggerTime>
  <endTriggerTime>500</endTriggerTime>
</MotionDetection>`;
            const response = await this.axiosInstance.put('/ISAPI/System/Video/inputs/channels/1/motionDetection', motionDetectionXML, {
                headers: {
                    'Content-Type': 'application/xml'
                }
            });
            return response.status === 200;
        }
        catch (error) {
            this.logger.error('Failed to ARM camera:', error);
            return false;
        }
    }
    async disarmCamera() {
        try {
            const motionDetectionXML = `<?xml version="1.0" encoding="UTF-8"?>
<MotionDetection>
  <enabled>false</enabled>
  <enableHighlight>false</enableHighlight>
  <samplingInterval>2</samplingInterval>
  <startTriggerTime>500</startTriggerTime>
  <endTriggerTime>500</endTriggerTime>
</MotionDetection>`;
            const response = await this.axiosInstance.put('/ISAPI/System/Video/inputs/channels/1/motionDetection', motionDetectionXML, {
                headers: {
                    'Content-Type': 'application/xml'
                }
            });
            return response.status === 200;
        }
        catch (error) {
            this.logger.error('Failed to DISARM camera:', error);
            return false;
        }
    }
    async getMotionDetectionRegions() {
        try {
            const response = await this.axiosInstance.get('/ISAPI/System/Video/inputs/channels/1/motionDetection/regions');
            const regionsData = await this.parseXMLResponse(response.data);
            const regions = [];
            const regionList = regionsData.MotionDetectionRegionList?.MotionDetectionRegion || [];
            for (const region of regionList) {
                regions.push({
                    id: parseInt(region.id?.[0] || '0'),
                    enabled: region.enabled?.[0] === 'true',
                    regionType: region.regionType?.[0] || 'grid',
                    coordinatesList: region.coordinatesList?.[0]?.coordinates?.map((coord) => ({
                        positionX: parseInt(coord.positionX?.[0] || '0'),
                        positionY: parseInt(coord.positionY?.[0] || '0')
                    })) || [],
                    sensitivityLevel: parseInt(region.sensitivityLevel?.[0] || '50')
                });
            }
            return regions;
        }
        catch (error) {
            this.logger.error('Failed to get motion detection regions:', error);
            return [];
        }
    }
    async captureSnapshot() {
        try {
            const response = await this.axiosInstance.get('/ISAPI/Streaming/channels/101/picture', {
                responseType: 'arraybuffer'
            });
            return Buffer.from(response.data);
        }
        catch (error) {
            this.logger.error('Failed to capture snapshot:', error);
            return null;
        }
    }
    getRTSPStreamURL(channel = 101) {
        return `rtsp://${this.credentials.username}:${this.credentials.password}@${this.credentials.ipAddress}:554/Streaming/Channels/${channel}`;
    }
    async parseXMLResponse(xmlData) {
        try {
            return await parseXML(xmlData);
        }
        catch (error) {
            this.logger.error('Failed to parse XML response:', error);
            throw new Error('Invalid XML response from camera');
        }
    }
}
exports.ISAPIClient = ISAPIClient;
//# sourceMappingURL=ISAPIClient.js.map
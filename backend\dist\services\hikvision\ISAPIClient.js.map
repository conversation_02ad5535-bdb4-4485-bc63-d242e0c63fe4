{"version": 3, "file": "ISAPIClient.js", "sourceRoot": "", "sources": ["../../../src/services/hikvision/ISAPIClient.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA+E;AAC/E,oDAA2B;AAC3B,mCAAoC;AACpC,+BAAgC;AAGhC,MAAM,QAAQ,GAAG,IAAA,gBAAS,EAAC,oBAAW,CAAC,CAAA;AA4DvC,MAAa,WAAW;IACd,aAAa,CAAe;IAC5B,WAAW,CAAsB;IACjC,MAAM,CAAgB;IACtB,UAAU,GAMd,EAAE,CAAA;IAEN,YAAY,WAAiC,EAAE,MAAsB;QACnE,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QAEpB,IAAI,CAAC,aAAa,GAAG,eAAK,CAAC,MAAM,CAAC;YAChC,OAAO,EAAE,UAAU,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,IAAI,EAAE;YAC9D,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,iBAAiB;gBACjC,QAAQ,EAAE,gCAAgC;aAC3C;SACF,CAAC,CAAA;QAGF,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACzC,CAAC,MAAM,EAAE,EAAE;YACT,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAC1B,OAAO,MAAM,CAAA;QACf,CAAC,EACD,CAAC,KAAK,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CACjC,CAAA;QAGD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC1C,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,EACtB,KAAK,EAAE,KAAK,EAAE,EAAE;YACd,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBAC3D,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAA;gBAC1B,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;YACjD,CAAC;YACD,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAC9B,CAAC,CACF,CAAA;IACH,CAAC;IAKO,KAAK,CAAC,kBAAkB,CAAC,QAAuB;QACtD,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAA;QACvD,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAKO,qBAAqB,CAAC,UAAkB;QAC9C,MAAM,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;YACxD,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;gBACjB,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;YACpC,CAAC;YACD,OAAO,GAAG,CAAA;QACZ,CAAC,EAAE,EAAS,CAAC,CAAA;QAEb,IAAI,CAAC,UAAU,GAAG;YAChB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,KAAK,EAAE,MAAM,CAAC,KAAK;YACnB,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,KAAK;SACrC,CAAA;IACH,CAAC;IAKO,aAAa,CAAC,MAA0B;QAC9C,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;YAC3B,OAAO,MAAM,CAAA;QACf,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,KAAK,CAAA;QACpD,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,EAAE,CAAA;QAC5B,MAAM,EAAE,GAAG,UAAU,CAAA;QACrB,MAAM,MAAM,GAAG,gBAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAGrD,MAAM,GAAG,GAAG,gBAAM;aACf,UAAU,CAAC,KAAK,CAAC;aACjB,MAAM,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;aAC5F,MAAM,CAAC,KAAK,CAAC,CAAA;QAGhB,MAAM,GAAG,GAAG,gBAAM;aACf,UAAU,CAAC,KAAK,CAAC;aACjB,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC;aAC1B,MAAM,CAAC,KAAK,CAAC,CAAA;QAGhB,IAAI,QAAgB,CAAA;QACpB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,MAAM,EAAE,CAAC;YACnC,QAAQ,GAAG,gBAAM;iBACd,UAAU,CAAC,KAAK,CAAC;iBACjB,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE,IAAI,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;iBACvF,MAAM,CAAC,KAAK,CAAC,CAAA;QAClB,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,gBAAM;iBACd,UAAU,CAAC,KAAK,CAAC;iBACjB,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC;iBAChD,MAAM,CAAC,KAAK,CAAC,CAAA;QAClB,CAAC;QAGD,IAAI,UAAU,GAAG,oBAAoB,IAAI,CAAC,WAAW,CAAC,QAAQ,aAAa,IAAI,CAAC,UAAU,CAAC,KAAK,aAAa,IAAI,CAAC,UAAU,CAAC,KAAK,WAAW,GAAG,gBAAgB,QAAQ,GAAG,CAAA;QAE3K,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;YACxB,UAAU,IAAI,SAAS,IAAI,CAAC,UAAU,CAAC,GAAG,QAAQ,EAAE,aAAa,MAAM,GAAG,CAAA;QAC5E,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YAC3B,UAAU,IAAI,aAAa,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAA;QACtD,CAAC;QAED,MAAM,CAAC,OAAO,GAAG;YACf,GAAG,MAAM,CAAC,OAAO;YACjB,aAAa,EAAE,UAAU;SAC1B,CAAA;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;YACzE,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAA;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;YACnD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAA;YACzE,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YAE7D,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAA;YAC9E,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;YAElE,OAAO;gBACL,UAAU,EAAE;oBACV,UAAU,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;oBAC/D,QAAQ,EAAE,UAAU,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;oBAC3D,KAAK,EAAE,UAAU,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;oBACrD,YAAY,EAAE,UAAU,CAAC,UAAU,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;oBACnE,UAAU,EAAE,UAAU,CAAC,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;oBAC/D,eAAe,EAAE,UAAU,CAAC,UAAU,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;oBACzE,oBAAoB,EAAE,UAAU,CAAC,UAAU,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;iBACpF;gBACD,kBAAkB,EAAE;oBAClB,4BAA4B,EAAE,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,4BAA4B,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC1G,6BAA6B,EAAE,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,6BAA6B,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC5G,oBAAoB,EAAE,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;oBAC1F,yBAAyB,EAAE,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,yBAAyB,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;iBACrG;gBACD,kBAAkB,EAAE,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBAC9F,kBAAkB,EAAE,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBAC9F,mBAAmB,EAAE,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBAChG,mBAAmB,EAAE,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;aACjG,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAA;YACtD,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;QAC1D,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAA;YAC3E,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YAEnE,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAA;YAC5G,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;YAEnE,OAAO;gBACL,YAAY,EAAE,QAAQ;gBACtB,QAAQ,EAAE,QAAQ,CAAC,UAAU,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBACjE,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBACvE,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,YAAY,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBACvE,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;gBAC7D,eAAe,EAAE,UAAU,CAAC,YAAY,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;gBACzE,sBAAsB,EAAE,UAAU,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC3E,OAAO,EAAE,UAAU,CAAC,eAAe,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;aAC7D,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;YACxD,OAAO;gBACL,YAAY,EAAE,OAAO;gBACrB,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,CAAC;gBACd,WAAW,EAAE,CAAC;gBACd,MAAM,EAAE,CAAC;gBACT,eAAe,EAAE,KAAK;gBACtB,sBAAsB,EAAE,KAAK;gBAC7B,OAAO,EAAE,KAAK;aACf,CAAA;QACH,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS;QACb,IAAI,CAAC;YACH,MAAM,kBAAkB,GAAG;;;;;;;mBAOd,CAAA;YAEb,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAC3C,uDAAuD,EACvD,kBAAkB,EAClB;gBACE,OAAO,EAAE;oBACP,cAAc,EAAE,iBAAiB;iBAClC;aACF,CACF,CAAA;YAED,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAA;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAA;YACjD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC;YACH,MAAM,kBAAkB,GAAG;;;;;;;mBAOd,CAAA;YAEb,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAC3C,uDAAuD,EACvD,kBAAkB,EAClB;gBACE,OAAO,EAAE;oBACP,cAAc,EAAE,iBAAiB;iBAClC;aACF,CACF,CAAA;YAED,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,CAAA;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;YACpD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,yBAAyB;QAC7B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAA;YAC9G,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;YAE9D,MAAM,OAAO,GAA4B,EAAE,CAAA;YAC3C,MAAM,UAAU,GAAG,WAAW,CAAC,yBAAyB,EAAE,qBAAqB,IAAI,EAAE,CAAA;YAErF,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;gBAChC,OAAO,CAAC,IAAI,CAAC;oBACX,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;oBACnC,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,KAAK,MAAM;oBACvC,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,IAAI,MAAM;oBAC5C,eAAe,EAAE,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;wBAC9E,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;wBAChD,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;qBACjD,CAAC,CAAC,IAAI,EAAE;oBACT,gBAAgB,EAAE,QAAQ,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;iBACjE,CAAC,CAAA;YACJ,CAAC;YAED,OAAO,OAAO,CAAA;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;YACnE,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,uCAAuC,EAAE;gBACrF,YAAY,EAAE,aAAa;aAC5B,CAAC,CAAA;YAEF,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAA;YACvD,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAKD,gBAAgB,CAAC,UAAkB,GAAG;QACpC,OAAO,UAAU,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,2BAA2B,OAAO,EAAE,CAAA;IAC3I,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,OAAe;QAC5C,IAAI,CAAC;YACH,OAAO,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;YACzD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAA;QACrD,CAAC;IACH,CAAC;CACF;AAhWD,kCAgWC"}
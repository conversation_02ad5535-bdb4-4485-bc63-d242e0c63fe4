{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.105Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.105Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.108Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.109Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.110Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.110Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:35.079Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.663Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.667Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.667Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.668Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.668Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:42:01 +0000] \"GET /health HTTP/1.1\" 200 107 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:01.981Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.193Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.196Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.197Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.197Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.198Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.266Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.593Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.597Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.597Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.598Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.598Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.712Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:42:48 +0000] \"GET /api/status HTTP/1.1\" 200 195 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:48.199Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:43:37 +0000] \"POST /api/auth/register HTTP/1.1\" 201 895 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:43:37.187Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:44:19 +0000] \"POST /api/auth/login HTTP/1.1\" 200 883 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:44:19.182Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:44:31 +0000] \"GET /api/auth/me HTTP/1.1\" 200 393 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:44:31.317Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.905Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.909Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.910Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.910Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.911Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.925Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.913Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.916Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.917Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.917Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.918Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.980Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.936Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.940Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.940Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.940Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.941Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.971Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.022Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.027Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.028Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.028Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.029Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.098Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.837Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.842Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.843Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.843Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.844Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.936Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.739Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.743Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.744Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.745Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.745Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.816Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.935Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.938Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.939Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.939Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.940Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.967Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.922Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.926Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.927Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.927Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.927Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.966Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.643Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.646Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.647Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.647Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.648Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.698Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.737Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.741Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.742Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.742Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.743Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.830Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.876Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.880Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.881Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.881Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.881Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.919Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.825Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.830Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.831Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.831Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.832Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.872Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:58.707Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:07:22.952Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.783Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.787Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.787Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.788Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.788Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:14:10 +0000] \"GET /health HTTP/1.1\" 200 106 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:14:10.375Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:14:15 +0000] \"GET /api/status HTTP/1.1\" 200 195 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:14:15.976Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:19:20 +0000] \"POST /api/auth/register HTTP/1.1\" 500 53 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:19:20.315Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:19:58 +0000] \"POST /api/auth/register HTTP/1.1\" 201 890 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:19:58.780Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:20:36 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:20:36.763Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:09.805Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:15.653Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.876Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.880Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.880Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.881Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.881Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.211Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.215Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.215Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.216Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.216Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:53:47 +0000] \"GET /health HTTP/1.1\" 200 107 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:47.225Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:53:53 +0000] \"GET /api/cameras HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:53.719Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:54:01 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:54:01.283Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:54:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:54:47.193Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:01 +0000] \"POST /api/cameras HTTP/1.1\" 400 208 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:01.061Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:16 +0000] \"POST /api/cameras HTTP/1.1\" 201 1077 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:16.007Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:30 +0000] \"POST /api/cameras HTTP/1.1\" 201 1074 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:30.388Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:42 +0000] \"GET /api/cameras HTTP/1.1\" 200 2119 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:42.588Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:04:12 +0000] \"POST /api/events HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:04:12.064Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:04:40 +0000] \"POST /api/events HTTP/1.1\" 201 706 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:04:40.481Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:04:57 +0000] \"POST /api/events HTTP/1.1\" 201 691 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:04:57.617Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:05:09 +0000] \"GET /api/events HTTP/1.1\" 200 1482 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:05:09.384Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:05:15 +0000] \"GET /api/events HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:05:15.353Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:37:04 +0000] \"POST /api/auth/login HTTP/1.1\" 401 37 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:37:04.947Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:37:17 +0000] \"GET /api/auth/profile HTTP/1.1\" 404 67 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:37:17.651Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:37:59 +0000] \"GET /api/auth/me HTTP/1.1\" 200 392 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:37:59.718Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:40:07 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:40:07.923Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:40:57 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:40:57.184Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:26:43 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:26:43.782Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:35:32 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:35:32.431Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:35:41 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:35:41.124Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:36:18 +0000] \"POST /api/auth/login HTTP/1.1\" 401 37 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:36:18.094Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:36:47 +0000] \"POST /api/auth/register HTTP/1.1\" 201 911 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:36:47.328Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:36:59 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:36:59.814Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:49:42 +0000] \"GET /api/health HTTP/1.1\" 404 61 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:49:42.925Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:49:49 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:49:49.502Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:50:02 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:50:02.556Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:50:17 +0000] \"GET /api/cameras HTTP/1.1\" 200 2119 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:50:17.659Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:50:30 +0000] \"GET /api/events HTTP/1.1\" 200 1482 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:50:30.748Z"}

{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.105Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.105Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.108Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.109Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.110Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:29.110Z"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:35.079Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.663Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.667Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.667Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.668Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:41:44.668Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:42:01 +0000] \"GET /health HTTP/1.1\" 200 107 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:01.981Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.193Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.196Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.197Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.197Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.198Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:12.266Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.593Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.597Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.597Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.598Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.598Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:24.712Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:42:48 +0000] \"GET /api/status HTTP/1.1\" 200 195 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:42:48.199Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:43:37 +0000] \"POST /api/auth/register HTTP/1.1\" 201 895 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:43:37.187Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:44:19 +0000] \"POST /api/auth/login HTTP/1.1\" 200 883 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:44:19.182Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:06:44:31 +0000] \"GET /api/auth/me HTTP/1.1\" 200 393 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:44:31.317Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.905Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.909Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.910Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.910Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.911Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T06:56:13.925Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.913Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.916Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.917Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.917Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.918Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:28.980Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.936Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.940Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.940Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.940Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.941Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:01:49.971Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.022Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.027Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.028Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.028Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.029Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:09.098Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.837Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.842Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.843Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.843Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.844Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:29.936Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.739Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.743Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.744Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.745Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.745Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:02:50.816Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.935Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.938Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.939Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.939Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.940Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:10.967Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.922Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.926Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.927Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.927Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.927Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:30.966Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.643Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.646Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.647Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.647Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.648Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:03:43.698Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.737Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.741Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.742Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.742Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.743Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:01.830Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.876Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.880Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.881Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.881Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.881Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:04:20.919Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.825Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.830Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.831Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.831Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.832Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:38.872Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:06:58.707Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T07:07:22.952Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.783Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.787Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.787Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.788Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:13:47.788Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:14:10 +0000] \"GET /health HTTP/1.1\" 200 106 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:14:10.375Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:14:15 +0000] \"GET /api/status HTTP/1.1\" 200 195 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:14:15.976Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:19:20 +0000] \"POST /api/auth/register HTTP/1.1\" 500 53 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:19:20.315Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:19:58 +0000] \"POST /api/auth/register HTTP/1.1\" 201 890 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:19:58.780Z"}
{"level":"info","message":"::1 - - [29/Jun/2025:09:20:36 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-06-29T09:20:36.763Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:09.805Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:15.653Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.876Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.880Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.880Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.881Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-06-30T16:12:19.881Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.211Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.215Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.215Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.216Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:03.216Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:53:47 +0000] \"GET /health HTTP/1.1\" 200 107 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:47.225Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:53:53 +0000] \"GET /api/cameras HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:53:53.719Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:54:01 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:54:01.283Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:54:47 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:54:47.193Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:01 +0000] \"POST /api/cameras HTTP/1.1\" 400 208 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:01.061Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:16 +0000] \"POST /api/cameras HTTP/1.1\" 201 1077 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:16.007Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:30 +0000] \"POST /api/cameras HTTP/1.1\" 201 1074 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:30.388Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:00:55:42 +0000] \"GET /api/cameras HTTP/1.1\" 200 2119 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T00:55:42.588Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:04:12 +0000] \"POST /api/events HTTP/1.1\" 500 33 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:04:12.064Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:04:40 +0000] \"POST /api/events HTTP/1.1\" 201 706 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:04:40.481Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:04:57 +0000] \"POST /api/events HTTP/1.1\" 201 691 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:04:57.617Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:05:09 +0000] \"GET /api/events HTTP/1.1\" 200 1482 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:05:09.384Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:01:05:15 +0000] \"GET /api/events HTTP/1.1\" 401 45 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T01:05:15.353Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:37:04 +0000] \"POST /api/auth/login HTTP/1.1\" 401 37 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:37:04.947Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:37:17 +0000] \"GET /api/auth/profile HTTP/1.1\" 404 67 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:37:17.651Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:37:59 +0000] \"GET /api/auth/me HTTP/1.1\" 200 392 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:37:59.718Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:40:07 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:40:07.923Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:07:40:57 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T07:40:57.184Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:26:43 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:26:43.782Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:35:32 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:35:32.431Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:35:41 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:35:41.124Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:36:18 +0000] \"POST /api/auth/login HTTP/1.1\" 401 37 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:36:18.094Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:36:47 +0000] \"POST /api/auth/register HTTP/1.1\" 201 911 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:36:47.328Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:36:59 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:36:59.814Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:49:42 +0000] \"GET /api/health HTTP/1.1\" 404 61 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:49:42.925Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:49:49 +0000] \"GET /api/auth/me HTTP/1.1\" 401 45 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:49:49.502Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:50:02 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:50:02.556Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:50:17 +0000] \"GET /api/cameras HTTP/1.1\" 200 2119 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:50:17.659Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:08:50:30 +0000] \"GET /api/events HTTP/1.1\" 200 1482 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T08:50:30.748Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:00.465Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:00.469Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:00.470Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:00.471Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:00.472Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:10.788Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:10.792Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:10.792Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:10.792Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:10.793Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:25.916Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:25.919Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:25.920Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:25.921Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:25.921Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:36.090Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:36.095Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:36.095Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:36.096Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:36.096Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:22:57:53 +0000] \"POST /api/auth/register HTTP/1.1\" 201 893 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:57:53.390Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:22:58:11 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:58:11.418Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:22:58:21 +0000] \"POST /api/auth/login HTTP/1.1\" 200 899 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:58:21.929Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:22:58:30 +0000] \"POST /api/auth/login HTTP/1.1\" 200 899 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:58:30.026Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:22:58:50 +0000] \"POST /api/auth/register HTTP/1.1\" 201 902 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:58:50.594Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:22:59:48 +0000] \"POST /api/auth/login HTTP/1.1\" 200 899 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T22:59:48.018Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:12 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:12.768Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:24 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:24.802Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:33 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:33.710Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:39 +0000] \"POST /api/auth/register HTTP/1.1\" 201 916 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:39.194Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:48 +0000] \"POST /api/auth/register HTTP/1.1\" 201 902 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:48.185Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:48 +0000] \"POST /api/auth/login HTTP/1.1\" 401 37 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:48.587Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:50 +0000] \"POST /api/auth/login HTTP/1.1\" 200 904 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:50.722Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:00:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 904 \"http://localhost:3001/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:00:54.903Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:01:24.008Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:01:24.012Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:01:24.012Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:01:24.013Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:01:24.013Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:04:50.389Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:04:50.395Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:04:50.396Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:04:50.397Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:04:50.398Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:02.009Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:02.014Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:02.015Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:02.015Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:02.015Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:14.043Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:14.047Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:14.047Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:14.048Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:07:14.048Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:00.217Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:16.525Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:16.529Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:16.530Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:16.530Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:16.531Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:56.750Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:56.754Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:56.754Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:56.755Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:08:56.755Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:09:04 +0000] \"GET /health HTTP/1.1\" 200 107 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:09:04.843Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:09:05 +0000] \"GET /favicon.ico HTTP/1.1\" 404 62 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:09:05.130Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:09:43 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:09:43.191Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:09:45 +0000] \"POST /api/auth/login HTTP/1.1\" 200 899 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:09:45.163Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:10:14 +0000] \"POST /api/auth/login HTTP/1.1\" 401 37 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:10:14.090Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:10:44 +0000] \"POST /api/auth/login HTTP/1.1\" 200 899 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:10:44.980Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:11:06 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:11:06.514Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:11:12 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:11:12.325Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:11:15 +0000] \"POST /api/auth/register HTTP/1.1\" 201 910 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:11:15.347Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:11:30 +0000] \"POST /api/auth/login HTTP/1.1\" 200 899 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:11:30.269Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:12:19 +0000] \"POST /api/auth/login HTTP/1.1\" 200 899 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:12:19.915Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:12:54.845Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:12:54.851Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:12:54.853Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:12:54.853Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:12:54.854Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:14:03.385Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:14:03.389Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:14:03.389Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:14:03.390Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:14:03.390Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:14:19 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:14:19.388Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:15:39 +0000] \"POST /api/auth/register HTTP/1.1\" 409 59 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:15:39.898Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:15:46 +0000] \"POST /api/auth/register HTTP/1.1\" 201 899 \"-\" \"curl/8.12.1\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:15:46.435Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:20:27.757Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:20:27.765Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:20:27.766Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:20:27.766Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:20:27.767Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:20:53 +0000] \"POST /api/auth/login HTTP/1.1\" 401 37 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:20:53.880Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:21:01 +0000] \"POST /api/auth/login HTTP/1.1\" 200 878 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:21:01.448Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:21:11 +0000] \"GET /api/auth/me HTTP/1.1\" 200 392 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:21:11.910Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:21:12 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:21:12.301Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:21:15 +0000] \"GET /api/events HTTP/1.1\" 200 1482 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:21:15.081Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:21:15 +0000] \"GET /api/events HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:21:15.100Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:05.960Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:05.965Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:05.966Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:05.966Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:05.966Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:23:20 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:20.275Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:23:32 +0000] \"GET /api/events HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:32.602Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:23:32 +0000] \"GET /api/events HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:32.621Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:23:37 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:37.701Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:23:42 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:42.829Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:23:44 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:44.799Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:23:57 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:23:57.331Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:24:00 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:24:00.326Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:24:04 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:24:04.226Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:24:06 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:24:06.254Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:24:30 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:24:30.198Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:24:34 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:24:34.414Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:24:49.504Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:25:11.258Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:25:38.870Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:25:38.879Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:25:38.881Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:25:38.882Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:25:38.882Z"}
{"level":"info","message":"::1 - - [01/Jul/2025:23:26:03 +0000] \"GET /api/auth/me HTTP/1.1\" 304 - \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:26:03.117Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:51:43.766Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:51:43.773Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:51:43.774Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:51:43.774Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:51:43.775Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:21.912Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:21.924Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:21.924Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:21.925Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:21.925Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:49.200Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:49.206Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:49.207Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:49.207Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:52:49.207Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:53:29.796Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:53:29.800Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:53:29.800Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:53:29.801Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:53:29.801Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:24.614Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:24.645Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:24.684Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:24.691Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:24.743Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:33.387Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:33.392Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:33.393Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:33.393Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:33.393Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:43.236Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:43.241Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:43.241Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:43.242Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:43.242Z"}
{"level":"info","message":"Connected to MongoDB","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:56.562Z"}
{"level":"info","message":"🚀 Server running on port 5000","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:56.567Z"}
{"level":"info","message":"📊 Health check: http://localhost:5000/health","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:56.569Z"}
{"level":"info","message":"🔌 Socket.IO ready for connections","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:56.569Z"}
{"level":"info","message":"🎯 Environment: development","service":"hikvision-ai-backend","timestamp":"2025-07-01T23:55:56.570Z"}

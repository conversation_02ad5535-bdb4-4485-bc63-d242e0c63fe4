{"name": "hikvision-ai-backend", "version": "1.0.0", "description": "AI-enhanced Hikvision monitoring system backend", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["hikvision", "ai", "monitoring", "surveillance"], "author": "AI Development Team", "license": "MIT", "dependencies": {"@types/crypto-js": "^4.2.2", "@types/xml2js": "^0.4.14", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "bullmq": "^5.56.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^17.0.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "ffmpeg-static": "^5.2.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "multer": "^2.0.1", "sharp": "^0.34.2", "socket.io": "^4.8.1", "winston": "^3.17.0", "xml2js": "^0.6.2", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.7", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.30.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
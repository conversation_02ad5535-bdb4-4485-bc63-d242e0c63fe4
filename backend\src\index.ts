import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import { createServer } from 'http'
import { Server } from 'socket.io'
import mongoose from 'mongoose'
import dotenv from 'dotenv'
import winston from 'winston'

// Import routes
import authRoutes from './routes/auth'
import cameraRoutes from './routes/cameras'
import eventRoutes from './routes/events'

// Import HikVision services
import { CameraManager } from './services/hikvision'
import { Camera } from './models/Camera'

// Load environment variables
dotenv.config()

// Create Express app
const app = express()
const server = createServer(app)
const io = new Server(server, {
  cors: {
    origin: [
      process.env.FRONTEND_URL || "http://localhost:3000",
      "http://localhost:3001",
      "http://*************:3000",
      "http://*************:3001"
    ],
    methods: ["GET", "POST"]
  }
})

// Initialize HikVision Camera Manager
let cameraManager: CameraManager

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'hikvision-ai-backend' },
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
})

// Middleware
app.use(helmet())
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || "http://localhost:3000",
    "http://localhost:3001",
    "http://*************:3000",
    "http://*************:3001"
  ],
  credentials: true
}))
app.use(morgan('combined', {
  stream: { write: (message) => logger.info(message.trim()) }
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  })
})

// API routes - Pass camera manager to routes
app.use('/api/auth', authRoutes)
app.use('/api/cameras', (req, res, next) => {
  req.cameraManager = cameraManager
  next()
}, cameraRoutes)
app.use('/api/events', eventRoutes)

app.get('/api/status', (req, res) => {
  res.json({
    message: 'HikVision AI Monitoring System API',
    version: '1.0.0',
    status: 'running',
    features: {
      cameras: 'active',
      ai_processing: 'active',
      real_time_events: 'active',
      notifications: 'active'
    }
  })
})

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`)

  socket.emit('welcome', {
    message: 'Connected to HikVision AI Monitoring System',
    timestamp: new Date().toISOString()
  })

  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`)
  })
})

// Initialize HikVision Camera Manager and set up event handlers
async function initializeCameraManager() {
  cameraManager = new CameraManager(logger)

  // Set up camera manager event handlers
  cameraManager.on('cameraEvent', (cameraId: string, event: any) => {
    logger.info(`Camera event from ${cameraId}:`, event.type)
    // Broadcast event to all connected clients
    io.emit('cameraEvent', {
      cameraId,
      event: {
        id: event._id,
        type: event.type,
        title: event.title,
        description: event.description,
        severity: event.severity,
        timestamp: event.timestamp,
        metadata: event.metadata
      }
    })
  })

  cameraManager.on('cameraAdded', (cameraId: string) => {
    logger.info(`Camera ${cameraId} added to manager`)
    io.emit('cameraStatusUpdate', { cameraId, status: 'online' })
  })

  cameraManager.on('cameraRemoved', (cameraId: string) => {
    logger.info(`Camera ${cameraId} removed from manager`)
    io.emit('cameraStatusUpdate', { cameraId, status: 'offline' })
  })

  cameraManager.on('cameraArmed', (cameraId: string) => {
    logger.info(`Camera ${cameraId} armed`)
    io.emit('cameraArmed', { cameraId })
  })

  cameraManager.on('cameraDisarmed', (cameraId: string) => {
    logger.info(`Camera ${cameraId} disarmed`)
    io.emit('cameraDisarmed', { cameraId })
  })

  // Load existing cameras and add them to the manager
  try {
    const cameras = await Camera.find({ isActive: true })
    logger.info(`Loading ${cameras.length} active cameras into manager`)

    for (const camera of cameras) {
      const success = await cameraManager.addCamera(camera)
      if (success) {
        logger.info(`Successfully loaded camera ${camera._id} (${camera.name})`)
      } else {
        logger.error(`Failed to load camera ${camera._id} (${camera.name})`)
      }
    }
  } catch (error) {
    logger.error('Failed to load cameras into manager:', error)
  }
}

// Database connection
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/hikvision-ai'
    await mongoose.connect(mongoUri)
    logger.info('Connected to MongoDB')
  } catch (error) {
    logger.error('MongoDB connection error:', error)
    process.exit(1)
  }
}

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', err)
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  })
})

// Start server
const PORT = process.env.PORT || 5000

const startServer = async () => {
  try {
    await connectDB()

    // Initialize camera manager after database connection
    await initializeCameraManager()

    server.listen(PORT, () => {
      logger.info(`🚀 HikVision AI Monitoring System running on port ${PORT}`)
      logger.info(`📊 Dashboard: http://localhost:${PORT}`)
      logger.info(`🔌 Socket.IO: Enabled`)
      logger.info(`🗄️  Database: Connected`)
      logger.info(`🔐 Authentication: JWT`)
      logger.info(`📡 API Status: All endpoints operational`)
      logger.info(`📹 Camera Manager: Initialized`)
      logger.info(`🎯 ISAPI Integration: Active`)
    })
  } catch (error) {
    logger.error('Failed to start server:', error)
    process.exit(1)
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully')
  if (cameraManager) {
    cameraManager.stop()
  }
  server.close(() => {
    logger.info('Process terminated')
    mongoose.connection.close()
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully')
  if (cameraManager) {
    cameraManager.stop()
  }
  server.close(() => {
    logger.info('Process terminated')
    mongoose.connection.close()
    process.exit(0)
  })
})

startServer()

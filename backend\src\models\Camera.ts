import mongoose, { Document, Schema } from 'mongoose'

export interface ICamera extends Document {
  name: string
  description?: string
  location: string
  ipAddress: string
  port: number
  username: string
  password: string
  cameraModel: string
  manufacturer: string
  firmware?: string
  resolution: string
  fps: number
  status: 'online' | 'offline' | 'maintenance' | 'error'
  isArmed: boolean
  isRecording: boolean
  lastSeen?: Date
  lastHeartbeat?: Date
  settings: {
    motionDetection: boolean
    motionSensitivity: number
    nightVision: boolean
    audioRecording: boolean
    recordingQuality: 'low' | 'medium' | 'high' | 'ultra'
    recordingMode: 'continuous' | 'motion' | 'schedule'
    retentionDays: number
  }
  aiSettings: {
    enabled: boolean
    personDetection: boolean
    vehicleDetection: boolean
    faceRecognition: boolean
    objectDetection: boolean
    confidenceThreshold: number
  }
  position: {
    latitude?: number
    longitude?: number
    floor?: string
    zone?: string
  }
  network: {
    rtspUrl?: string
    httpUrl?: string
    onvifUrl?: string
    streamUrl?: string
  }
  statistics: {
    totalEvents: number
    eventsToday: number
    uptime: number
    lastReboot?: Date
    dataTransferred: number
  }
  maintenance: {
    lastMaintenance?: Date
    nextMaintenance?: Date
    maintenanceNotes?: string
  }
  createdAt: Date
  updatedAt: Date
  isOnline: boolean
  updateHeartbeat(): Promise<void>
  updateStatus(status: string): Promise<void>
}

const CameraSchema = new Schema<ICamera>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  description: {
    type: String,
    trim: true,
    maxlength: 500
  },
  location: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  ipAddress: {
    type: String,
    required: true,
    match: [/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/, 'Please enter a valid IP address']
  },
  port: {
    type: Number,
    required: true,
    min: 1,
    max: 65535,
    default: 80
  },
  username: {
    type: String,
    required: true,
    trim: true
  },
  password: {
    type: String,
    required: true
  },
  cameraModel: {
    type: String,
    required: true,
    trim: true
  },
  manufacturer: {
    type: String,
    required: true,
    default: 'HikVision'
  },
  firmware: {
    type: String,
    trim: true
  },
  resolution: {
    type: String,
    required: true,
    enum: ['720p', '1080p', '4K', '2MP', '4MP', '8MP']
  },
  fps: {
    type: Number,
    required: true,
    min: 1,
    max: 60,
    default: 25
  },
  status: {
    type: String,
    enum: ['online', 'offline', 'maintenance', 'error'],
    default: 'offline'
  },
  isArmed: {
    type: Boolean,
    default: false
  },
  isRecording: {
    type: Boolean,
    default: false
  },
  lastSeen: {
    type: Date
  },
  lastHeartbeat: {
    type: Date
  },
  settings: {
    motionDetection: {
      type: Boolean,
      default: true
    },
    motionSensitivity: {
      type: Number,
      min: 1,
      max: 100,
      default: 50
    },
    nightVision: {
      type: Boolean,
      default: true
    },
    audioRecording: {
      type: Boolean,
      default: false
    },
    recordingQuality: {
      type: String,
      enum: ['low', 'medium', 'high', 'ultra'],
      default: 'high'
    },
    recordingMode: {
      type: String,
      enum: ['continuous', 'motion', 'schedule'],
      default: 'motion'
    },
    retentionDays: {
      type: Number,
      min: 1,
      max: 365,
      default: 30
    }
  },
  aiSettings: {
    enabled: {
      type: Boolean,
      default: true
    },
    personDetection: {
      type: Boolean,
      default: true
    },
    vehicleDetection: {
      type: Boolean,
      default: true
    },
    faceRecognition: {
      type: Boolean,
      default: false
    },
    objectDetection: {
      type: Boolean,
      default: true
    },
    confidenceThreshold: {
      type: Number,
      min: 50,
      max: 95,
      default: 80
    }
  },
  position: {
    latitude: {
      type: Number,
      min: -90,
      max: 90
    },
    longitude: {
      type: Number,
      min: -180,
      max: 180
    },
    floor: {
      type: String,
      trim: true
    },
    zone: {
      type: String,
      trim: true
    }
  },
  network: {
    rtspUrl: {
      type: String,
      trim: true
    },
    httpUrl: {
      type: String,
      trim: true
    },
    onvifUrl: {
      type: String,
      trim: true
    },
    streamUrl: {
      type: String,
      trim: true
    }
  },
  statistics: {
    totalEvents: {
      type: Number,
      default: 0
    },
    eventsToday: {
      type: Number,
      default: 0
    },
    uptime: {
      type: Number,
      default: 0
    },
    lastReboot: {
      type: Date
    },
    dataTransferred: {
      type: Number,
      default: 0
    }
  },
  maintenance: {
    lastMaintenance: {
      type: Date
    },
    nextMaintenance: {
      type: Date
    },
    maintenanceNotes: {
      type: String,
      trim: true
    }
  }
}, {
  timestamps: true
})

// Indexes
CameraSchema.index({ ipAddress: 1 }, { unique: true })
CameraSchema.index({ status: 1 })
CameraSchema.index({ location: 1 })
CameraSchema.index({ isArmed: 1 })
CameraSchema.index({ 'position.zone': 1 })
CameraSchema.index({ createdAt: -1 })

// Virtual for online status
CameraSchema.virtual('isOnline').get(function(this: ICamera) {
  if (!this.lastHeartbeat) return false
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
  return this.lastHeartbeat > fiveMinutesAgo && this.status === 'online'
})

// Method to update heartbeat
CameraSchema.methods.updateHeartbeat = async function(): Promise<void> {
  this.lastHeartbeat = new Date()
  this.lastSeen = new Date()
  if (this.status === 'offline') {
    this.status = 'online'
  }
  return this.save()
}

// Method to update status
CameraSchema.methods.updateStatus = async function(status: string): Promise<void> {
  this.status = status
  this.lastSeen = new Date()
  return this.save()
}

// Pre-save middleware to generate URLs
CameraSchema.pre('save', function(next) {
  if (this.isModified('ipAddress') || this.isModified('port') || this.isModified('username')) {
    const baseUrl = `http://${this.ipAddress}:${this.port}`
    this.network.httpUrl = baseUrl
    this.network.rtspUrl = `rtsp://${this.username}:${this.password}@${this.ipAddress}:554/Streaming/Channels/101`
    this.network.onvifUrl = `${baseUrl}/onvif/device_service`
    this.network.streamUrl = `${baseUrl}/ISAPI/Streaming/channels/101/picture`
  }
  next()
})

// Transform output to hide sensitive data
CameraSchema.methods.toJSON = function() {
  const camera = this.toObject()
  delete camera.password
  return camera
}

export const Camera = mongoose.model<ICamera>('Camera', CameraSchema)

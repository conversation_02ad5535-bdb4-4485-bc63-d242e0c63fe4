import express, { Request, Response, NextFunction } from 'express'
import { body, validationResult } from 'express-validator'
import { User, IUser } from '../models/User'
import {
  generateToken,
  generateRefreshToken,
  verifyRefreshToken,
  authenticate,
  AuthRequest
} from '../middleware/auth'

const router = express.Router()

// Register new user
router.post('/register', async (req: Request, res: Response): Promise<void> => {
    try {
      const { username, email, password, firstName, lastName, role = 'viewer' } = req.body

      // Check if user already exists
      const existingUser = await User.findOne({
        $or: [
          { email: email.toLowerCase() },
          { username: username.toLowerCase() }
        ]
      })

      if (existingUser) {
        res.status(409).json({
          error: 'User already exists with this email or username'
        })
        return
      }

      // Create new user
      const user = new User({
        username: username.toLowerCase(),
        email: email.toLowerCase(),
        password,
        firstName,
        lastName,
        role
      })

      await user.save()

      // Generate tokens
      const token = generateToken(user)
      const refreshToken = generateRefreshToken(user)

      res.status(201).json({
        message: 'User registered successfully',
        user: user.toJSON(),
        token,
        refreshToken
      })
    } catch (error) {
      console.error('Registration error:', error)
      res.status(500).json({
        error: 'Internal server error during registration'
      })
    }
  }
)

// Login user
router.post('/login', async (req: Request, res: Response): Promise<void> => {
    try {
      const { email, password } = req.body

      // Find user
      const user = await User.findOne({ email: email.toLowerCase() })

      if (!user || !await user.comparePassword(password)) {
        res.status(401).json({
          error: 'Invalid email or password'
        })
        return
      }

      // Generate tokens
      const token = generateToken(user)
      const refreshToken = generateRefreshToken(user)

      res.json({
        message: 'Login successful',
        user: user.toJSON(),
        token,
        refreshToken
      })
    } catch (error) {
      console.error('Login error:', error)
      res.status(500).json({
        error: 'Internal server error during login'
      })
    }
  }
)

// Refresh token
router.post('/refresh', async (req: Request, res: Response): Promise<void> => {
    try {
      const { refreshToken } = req.body

      if (!refreshToken) {
        res.status(400).json({
          error: 'Refresh token is required'
        })
        return
      }

      const { userId } = verifyRefreshToken(refreshToken)
      const user = await User.findById(userId)

      if (!user || !user.isActive) {
        res.status(401).json({
          error: 'Invalid refresh token'
        })
        return
      }

      // Generate new tokens
      const newToken = generateToken(user)
      const newRefreshToken = generateRefreshToken(user)

      res.json({
        token: newToken,
        refreshToken: newRefreshToken
      })
    } catch (error) {
      console.error('Token refresh error:', error)
      res.status(401).json({
        error: 'Invalid refresh token'
      })
    }
  }
)

// Logout (invalidate token - in a real app, you'd maintain a blacklist)
router.post('/logout', authenticate, async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      // In a production app, you would add the token to a blacklist
      // For now, we'll just return success
      res.json({
        message: 'Logout successful'
      })
    } catch (error) {
      console.error('Logout error:', error)
      res.status(500).json({
        error: 'Internal server error during logout'
      })
    }
  }
)

// Get current user profile
router.get('/me', authenticate, async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      res.json({
        user: req.user?.toJSON()
      })
    } catch (error) {
      console.error('Get profile error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Update user profile
router.patch('/me', authenticate, async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const allowedUpdates = ['firstName', 'lastName', 'preferences']
      const updates = Object.keys(req.body)
      const isValidOperation = updates.every(update => allowedUpdates.includes(update))

      if (!isValidOperation) {
        res.status(400).json({
          error: 'Invalid updates'
        })
        return
      }

      const user = req.user!
      updates.forEach(update => {
        if (update === 'preferences') {
          user.preferences = { ...user.preferences, ...req.body.preferences }
        } else {
          (user as any)[update] = req.body[update]
        }
      })

      await user.save()

      res.json({
        message: 'Profile updated successfully',
        user: user.toJSON()
      })
    } catch (error) {
      console.error('Update profile error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Change password
router.patch('/change-password', authenticate, async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const { currentPassword, newPassword } = req.body
      const user = req.user!

      // Verify current password
      const isMatch = await user.comparePassword(currentPassword)
      if (!isMatch) {
        res.status(400).json({
          error: 'Current password is incorrect'
        })
        return
      }

      // Update password
      user.password = newPassword
      await user.save()

      res.json({
        message: 'Password changed successfully'
      })
    } catch (error) {
      console.error('Change password error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

export default router

import express, { Request, Response } from 'express'
import { body, query, param, validationResult } from 'express-validator'
import { Camera, ICamera } from '../models/Camera'
import { authenticate, authorize, operatorAccess, viewerAccess, AuthRequest } from '../middleware/auth'
import { CameraManager } from '../services/hikvision'
import mongoose from 'mongoose'

// Extend Express Request interface
declare module 'express-serve-static-core' {
  interface Request {
    cameraManager?: CameraManager
  }
}

const router = express.Router()

// Validation rules
const cameraValidation = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('Camera name is required and must be less than 100 characters'),
  body('location')
    .isLength({ min: 1, max: 200 })
    .withMessage('Location is required and must be less than 200 characters'),
  body('ipAddress')
    .isIP(4)
    .withMessage('Please provide a valid IPv4 address'),
  body('port')
    .isInt({ min: 1, max: 65535 })
    .withMessage('Port must be between 1 and 65535'),
  body('username')
    .isLength({ min: 1 })
    .withMessage('Username is required'),
  body('password')
    .isLength({ min: 1 })
    .withMessage('Password is required'),
  body('cameraModel')
    .isLength({ min: 1 })
    .withMessage('Camera model is required'),
  body('resolution')
    .isIn(['720p', '1080p', '4K', '2MP', '4MP', '8MP'])
    .withMessage('Invalid resolution'),
  body('fps')
    .isInt({ min: 1, max: 60 })
    .withMessage('FPS must be between 1 and 60')
]

// Get all cameras
router.get('/',
  authenticate,
  viewerAccess,
  [
    query('status').optional().isIn(['online', 'offline', 'maintenance', 'error']),
    query('location').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('page').optional().isInt({ min: 1 })
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const { status, location, limit = 50, page = 1 } = req.query
      const skip = (Number(page) - 1) * Number(limit)

      // Build filter
      const filter: any = {}
      if (status) filter.status = status
      if (location) filter.location = new RegExp(location as string, 'i')

      const cameras = await Camera.find(filter)
        .select('-password') // Exclude password from response
        .sort({ name: 1 })
        .limit(Number(limit))
        .skip(skip)

      const total = await Camera.countDocuments(filter)

      res.json({
        cameras,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      })
    } catch (error) {
      console.error('Get cameras error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get camera by ID
router.get('/:id',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id).select('-password')

      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      res.json({ camera })
    } catch (error) {
      console.error('Get camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Create new camera
router.post('/',
  authenticate,
  operatorAccess,
  cameraValidation,
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      // Check if camera with same IP already exists
      const existingCamera = await Camera.findOne({ ipAddress: req.body.ipAddress })
      if (existingCamera) {
        res.status(409).json({
          error: 'Camera with this IP address already exists'
        })
        return
      }

      const camera = new Camera(req.body)
      await camera.save()

      // Add camera to manager for ISAPI integration
      let managerStatus = 'not_available'
      if (req.cameraManager) {
        const success = await req.cameraManager.addCamera(camera)
        managerStatus = success ? 'connected' : 'failed'
      }

      res.status(201).json({
        message: 'Camera created successfully',
        camera: camera.toJSON(),
        isapiStatus: managerStatus,
        note: managerStatus === 'connected' ? 'Camera connected and ready for monitoring' :
              managerStatus === 'failed' ? 'Camera created but ISAPI connection failed' :
              'Camera created but ISAPI manager not available'
      })
    } catch (error) {
      console.error('Create camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Update camera
router.patch('/:id',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    body('name').optional().isLength({ min: 1, max: 100 }),
    body('location').optional().isLength({ min: 1, max: 200 }),
    body('ipAddress').optional().isIP(4),
    body('port').optional().isInt({ min: 1, max: 65535 }),
    body('resolution').optional().isIn(['720p', '1080p', '4K', '2MP', '4MP', '8MP']),
    body('fps').optional().isInt({ min: 1, max: 60 })
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      // Check IP address uniqueness if being updated
      if (req.body.ipAddress && req.body.ipAddress !== camera.ipAddress) {
        const existingCamera = await Camera.findOne({ ipAddress: req.body.ipAddress })
        if (existingCamera) {
          res.status(409).json({
            error: 'Camera with this IP address already exists'
          })
          return
        }
      }

      const allowedUpdates = [
        'name', 'description', 'location', 'ipAddress', 'port', 'username',
        'password', 'cameraModel', 'firmware', 'resolution', 'fps', 'settings',
        'aiSettings', 'position', 'maintenance'
      ]

      const updates = Object.keys(req.body)
      const isValidOperation = updates.every(update => allowedUpdates.includes(update))

      if (!isValidOperation) {
        res.status(400).json({
          error: 'Invalid updates'
        })
        return
      }

      updates.forEach(update => {
        if (typeof req.body[update] === 'object' && req.body[update] !== null) {
          (camera as any)[update] = { ...(camera as any)[update], ...req.body[update] }
        } else {
          (camera as any)[update] = req.body[update]
        }
      })

      await camera.save()

      res.json({
        message: 'Camera updated successfully',
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Update camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Delete camera
router.delete('/:id',
  authenticate,
  authorize('admin'),
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      // Remove camera from manager first
      if (req.cameraManager && req.params.id) {
        req.cameraManager.removeCamera(req.params.id)
      }

      await Camera.findByIdAndDelete(req.params.id)

      res.json({
        message: 'Camera deleted successfully',
        note: 'Camera removed from database and ISAPI manager'
      })
    } catch (error) {
      console.error('Delete camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Arm camera (enable motion detection)
router.post('/:id/arm',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      // Use ISAPI to arm the camera if camera manager is available
      let isapiSuccess = false
      if (req.cameraManager && req.params.id) {
        isapiSuccess = await req.cameraManager.armCamera(req.params.id)
      }

      // Update database regardless of ISAPI result
      camera.isArmed = true
      await camera.save()

      res.json({
        message: 'Camera armed successfully',
        camera: camera.toJSON(),
        isapiStatus: isapiSuccess ? 'success' : 'failed',
        note: isapiSuccess ? 'Motion detection enabled on camera' : 'Database updated, but camera may not be physically armed'
      })
    } catch (error) {
      console.error('Arm camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Disarm camera (disable motion detection)
router.post('/:id/disarm',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      // Use ISAPI to disarm the camera if camera manager is available
      let isapiSuccess = false
      if (req.cameraManager && req.params.id) {
        isapiSuccess = await req.cameraManager.disarmCamera(req.params.id)
      }

      // Update database regardless of ISAPI result
      camera.isArmed = false
      await camera.save()

      res.json({
        message: 'Camera disarmed successfully',
        camera: camera.toJSON(),
        isapiStatus: isapiSuccess ? 'success' : 'failed',
        note: isapiSuccess ? 'Motion detection disabled on camera' : 'Database updated, but camera may not be physically disarmed'
      })
    } catch (error) {
      console.error('Disarm camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Start/Stop recording
router.patch('/:id/recording',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    body('recording').isBoolean().withMessage('Recording status must be boolean')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      camera.isRecording = req.body.recording
      await camera.save()

      res.json({
        message: `Recording ${req.body.recording ? 'started' : 'stopped'} successfully`,
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Recording control error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Update camera status
router.patch('/:id/status',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    body('status').isIn(['online', 'offline', 'maintenance', 'error']).withMessage('Invalid status')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      await camera.updateStatus(req.body.status)

      res.json({
        message: 'Camera status updated successfully',
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Update camera status error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Camera heartbeat
router.post('/:id/heartbeat',
  authenticate,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      await camera.updateHeartbeat()

      res.json({
        message: 'Heartbeat updated successfully'
      })
    } catch (error) {
      console.error('Camera heartbeat error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get camera real-time status via ISAPI
router.get('/:id/status',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      // Get real-time status via ISAPI if available
      let isapiStatus = null
      if (req.cameraManager && req.params.id) {
        isapiStatus = await req.cameraManager.getCameraStatus(req.params.id)
      }

      const connectionStatus = req.cameraManager && req.params.id ?
        req.cameraManager.getConnectionStatus()[req.params.id] : null

      res.json({
        camera: {
          id: camera._id,
          name: camera.name,
          status: camera.status,
          isArmed: camera.isArmed,
          lastSeen: camera.lastSeen,
          lastHeartbeat: camera.lastHeartbeat
        },
        realTimeStatus: isapiStatus,
        connectionStatus: connectionStatus
      })
    } catch (error) {
      console.error('Get camera status error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Capture snapshot from camera
router.get('/:id/snapshot',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      // Capture snapshot via ISAPI if available
      if (!req.cameraManager) {
        res.status(503).json({
          error: 'Camera manager not available'
        })
        return
      }

      const snapshot = req.params.id ? await req.cameraManager.captureSnapshot(req.params.id) : null
      if (!snapshot) {
        res.status(500).json({
          error: 'Failed to capture snapshot'
        })
        return
      }

      res.set({
        'Content-Type': 'image/jpeg',
        'Content-Length': snapshot.length.toString(),
        'Cache-Control': 'no-cache'
      })
      res.send(snapshot)
    } catch (error) {
      console.error('Capture snapshot error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get RTSP stream URL
router.get('/:id/stream',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    query('channel').optional().isInt({ min: 101, max: 199 })
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      // Get RTSP stream URL via ISAPI if available
      if (!req.cameraManager) {
        // Fallback to database stored URL
        res.json({
          streamUrl: camera.network.rtspUrl || null,
          source: 'database'
        })
        return
      }

      const channel = parseInt(req.query.channel as string) || 101
      const streamUrl = req.params.id ? req.cameraManager.getRTSPStreamURL(req.params.id, channel) : null

      res.json({
        streamUrl: streamUrl,
        channel: channel,
        source: 'isapi'
      })
    } catch (error) {
      console.error('Get stream URL error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get camera statistics
router.get('/:id/stats',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const camera = await Camera.findById(req.params.id).select('statistics name location')
      if (!camera) {
        res.status(404).json({
          error: 'Camera not found'
        })
        return
      }

      res.json({
        cameraId: camera._id,
        name: camera.name,
        location: camera.location,
        statistics: camera.statistics
      })
    } catch (error) {
      console.error('Get camera statistics error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

export default router

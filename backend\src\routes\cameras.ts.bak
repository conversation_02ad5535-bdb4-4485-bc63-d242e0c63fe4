import express, { Request, Response } from 'express'
import { body, query, param, validationResult } from 'express-validator'
import { Camera, ICamera } from '../models/Camera'
import { authenticate, authorize, operatorAccess, viewerAccess, AuthRequest } from '../middleware/auth'
import mongoose from 'mongoose'

const router = express.Router()

// Validation rules
const cameraValidation = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .withMessage('Camera name is required and must be less than 100 characters'),
  body('location')
    .isLength({ min: 1, max: 200 })
    .withMessage('Location is required and must be less than 200 characters'),
  body('ipAddress')
    .isIP(4)
    .withMessage('Please provide a valid IPv4 address'),
  body('port')
    .isInt({ min: 1, max: 65535 })
    .withMessage('Port must be between 1 and 65535'),
  body('username')
    .isLength({ min: 1 })
    .withMessage('Username is required'),
  body('password')
    .isLength({ min: 1 })
    .withMessage('Password is required'),
  body('model')
    .isLength({ min: 1 })
    .withMessage('Camera model is required'),
  body('resolution')
    .isIn(['720p', '1080p', '4K', '2MP', '4MP', '8MP'])
    .withMessage('Invalid resolution'),
  body('fps')
    .isInt({ min: 1, max: 60 })
    .withMessage('FPS must be between 1 and 60')
]

// Get all cameras
router.get('/',
  authenticate,
  viewerAccess,
  [
    query('status').optional().isIn(['online', 'offline', 'maintenance', 'error']),
    query('location').optional().isString(),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('page').optional().isInt({ min: 1 })
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { status, location, limit = 50, page = 1 } = req.query
      const skip = (Number(page) - 1) * Number(limit)

      // Build filter
      const filter: any = {}
      if (status) filter.status = status
      if (location) filter.location = new RegExp(location as string, 'i')

      const cameras = await Camera.find(filter)
        .select('-password') // Exclude password from response
        .sort({ name: 1 })
        .limit(Number(limit))
        .skip(skip)

      const total = await Camera.countDocuments(filter)

      res.json({
        cameras,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      })
    } catch (error) {
      console.error('Get cameras error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get camera by ID
router.get('/:id',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id).select('-password')
      
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      res.json({ camera })
    } catch (error) {
      console.error('Get camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Create new camera
router.post('/',
  authenticate,
  operatorAccess,
  cameraValidation,
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      // Check if camera with same IP already exists
      const existingCamera = await Camera.findOne({ ipAddress: req.body.ipAddress })
      if (existingCamera) {
        return res.status(409).json({
          error: 'Camera with this IP address already exists'
        })
      }

      const camera = new Camera(req.body)
      await camera.save()

      res.status(201).json({
        message: 'Camera created successfully',
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Create camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Update camera
router.patch('/:id',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    body('name').optional().isLength({ min: 1, max: 100 }),
    body('location').optional().isLength({ min: 1, max: 200 }),
    body('ipAddress').optional().isIP(4),
    body('port').optional().isInt({ min: 1, max: 65535 }),
    body('resolution').optional().isIn(['720p', '1080p', '4K', '2MP', '4MP', '8MP']),
    body('fps').optional().isInt({ min: 1, max: 60 })
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      // Check IP address uniqueness if being updated
      if (req.body.ipAddress && req.body.ipAddress !== camera.ipAddress) {
        const existingCamera = await Camera.findOne({ ipAddress: req.body.ipAddress })
        if (existingCamera) {
          return res.status(409).json({
            error: 'Camera with this IP address already exists'
          })
        }
      }

      const allowedUpdates = [
        'name', 'description', 'location', 'ipAddress', 'port', 'username', 
        'password', 'model', 'firmware', 'resolution', 'fps', 'settings', 
        'aiSettings', 'position', 'maintenance'
      ]
      
      const updates = Object.keys(req.body)
      const isValidOperation = updates.every(update => allowedUpdates.includes(update))

      if (!isValidOperation) {
        return res.status(400).json({
          error: 'Invalid updates'
        })
      }

      updates.forEach(update => {
        if (typeof req.body[update] === 'object' && req.body[update] !== null) {
          (camera as any)[update] = { ...(camera as any)[update], ...req.body[update] }
        } else {
          (camera as any)[update] = req.body[update]
        }
      })

      await camera.save()

      res.json({
        message: 'Camera updated successfully',
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Update camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Delete camera
router.delete('/:id',
  authenticate,
  authorize('admin'),
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      await Camera.findByIdAndDelete(req.params.id)

      res.json({
        message: 'Camera deleted successfully'
      })
    } catch (error) {
      console.error('Delete camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Arm/Disarm camera
router.patch('/:id/arm',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    body('armed').isBoolean().withMessage('Armed status must be boolean')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      camera.isArmed = req.body.armed
      await camera.save()

      res.json({
        message: `Camera ${req.body.armed ? 'armed' : 'disarmed'} successfully`,
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Arm/disarm camera error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Start/Stop recording
router.patch('/:id/recording',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    body('recording').isBoolean().withMessage('Recording status must be boolean')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      camera.isRecording = req.body.recording
      await camera.save()

      res.json({
        message: `Recording ${req.body.recording ? 'started' : 'stopped'} successfully`,
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Recording control error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Update camera status
router.patch('/:id/status',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID'),
    body('status').isIn(['online', 'offline', 'maintenance', 'error']).withMessage('Invalid status')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      await camera.updateStatus(req.body.status)

      res.json({
        message: 'Camera status updated successfully',
        camera: camera.toJSON()
      })
    } catch (error) {
      console.error('Update camera status error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Camera heartbeat
router.post('/:id/heartbeat',
  authenticate,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id)
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      await camera.updateHeartbeat()

      res.json({
        message: 'Heartbeat updated successfully'
      })
    } catch (error) {
      console.error('Camera heartbeat error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get camera statistics
router.get('/:id/stats',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid camera ID')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const camera = await Camera.findById(req.params.id).select('statistics name location')
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      res.json({
        cameraId: camera._id,
        name: camera.name,
        location: camera.location,
        statistics: camera.statistics
      })
    } catch (error) {
      console.error('Get camera statistics error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

export default router

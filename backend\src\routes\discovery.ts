import express, { Request, Response } from 'express'
import { body, query, validationResult } from 'express-validator'
import { authenticate, operatorAccess, AuthRequest } from '../middleware/auth'
import { NetworkDiscovery, DiscoveredCamera, NetworkScanOptions } from '../services/hikvision'
import winston from 'winston'

// Extend Express Request interface
declare module 'express-serve-static-core' {
  interface Request {
    networkDiscovery?: NetworkDiscovery
  }
}

const router = express.Router()

// Create logger for discovery service
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'network-discovery' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
})

// Initialize NetworkDiscovery instance
const networkDiscovery = new NetworkDiscovery(logger)

// Middleware to attach networkDiscovery to request
router.use((req: Request, res: Response, next) => {
  req.networkDiscovery = networkDiscovery
  next()
})

/**
 * @route   POST /api/discovery/scan
 * @desc    Start network scan for HikVision cameras
 * @access  Private (Operator+)
 */
router.post('/scan',
  authenticate,
  operatorAccess,
  [
    body('networkRange')
      .notEmpty()
      .withMessage('Network range is required')
      .matches(/^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/)
      .withMessage('Invalid network range format (use CIDR notation like 192.168.1.0/24)'),
    body('portRange')
      .isArray({ min: 1 })
      .withMessage('Port range must be an array with at least one port'),
    body('portRange.*')
      .isInt({ min: 1, max: 65535 })
      .withMessage('Each port must be between 1 and 65535'),
    body('timeout')
      .optional()
      .isInt({ min: 1000, max: 30000 })
      .withMessage('Timeout must be between 1000 and 30000 milliseconds'),
    body('maxConcurrent')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Max concurrent must be between 1 and 100'),
    body('credentials')
      .optional()
      .isArray()
      .withMessage('Credentials must be an array'),
    body('credentials.*.username')
      .if(body('credentials').exists())
      .notEmpty()
      .withMessage('Username is required for each credential'),
    body('credentials.*.password')
      .if(body('credentials').exists())
      .notEmpty()
      .withMessage('Password is required for each credential')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const {
        networkRange,
        portRange,
        timeout = 5000,
        maxConcurrent = 20,
        credentials = []
      } = req.body

      const scanOptions: NetworkScanOptions = {
        networkRange,
        portRange,
        timeout,
        maxConcurrent,
        credentials
      }

      // Start the scan (non-blocking)
      const scanPromise = req.networkDiscovery!.scanNetwork(scanOptions)
      
      // Return immediately with scan started status
      res.status(202).json({
        message: 'Network scan started',
        scanId: Date.now().toString(),
        options: scanOptions
      })

      // Handle scan completion in background
      try {
        const discoveredCameras = await scanPromise
        logger.info(`Scan completed: ${discoveredCameras.length} cameras found`)
      } catch (error) {
        logger.error('Scan failed:', error)
      }

    } catch (error) {
      logger.error('Failed to start network scan:', error)
      res.status(500).json({
        error: 'Failed to start network scan',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
)

/**
 * @route   GET /api/discovery/status
 * @desc    Get current scan status
 * @access  Private (Operator+)
 */
router.get('/status',
  authenticate,
  operatorAccess,
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const status = req.networkDiscovery!.getScanStatus()
      res.json(status)
    } catch (error) {
      logger.error('Failed to get scan status:', error)
      res.status(500).json({
        error: 'Failed to get scan status',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
)

/**
 * @route   POST /api/discovery/stop
 * @desc    Stop current network scan
 * @access  Private (Operator+)
 */
router.post('/stop',
  authenticate,
  operatorAccess,
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      req.networkDiscovery!.stopScan()
      res.json({ message: 'Scan stopped successfully' })
    } catch (error) {
      logger.error('Failed to stop scan:', error)
      res.status(500).json({
        error: 'Failed to stop scan',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
)

/**
 * @route   POST /api/discovery/ping
 * @desc    Ping a specific host
 * @access  Private (Operator+)
 */
router.post('/ping',
  authenticate,
  operatorAccess,
  [
    body('ipAddress')
      .isIP()
      .withMessage('Valid IP address is required'),
    body('port')
      .optional()
      .isInt({ min: 1, max: 65535 })
      .withMessage('Port must be between 1 and 65535'),
    body('timeout')
      .optional()
      .isInt({ min: 1000, max: 10000 })
      .withMessage('Timeout must be between 1000 and 10000 milliseconds')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const { ipAddress, port = 80, timeout = 3000 } = req.body
      const startTime = Date.now()
      
      const isReachable = await req.networkDiscovery!.pingHost(ipAddress, port, timeout)
      const responseTime = Date.now() - startTime

      res.json({
        ipAddress,
        port,
        isReachable,
        responseTime
      })

    } catch (error) {
      logger.error('Failed to ping host:', error)
      res.status(500).json({
        error: 'Failed to ping host',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
)

/**
 * @route   POST /api/discovery/test-camera
 * @desc    Test connection to a specific camera
 * @access  Private (Operator+)
 */
router.post('/test-camera',
  authenticate,
  operatorAccess,
  [
    body('ipAddress')
      .isIP()
      .withMessage('Valid IP address is required'),
    body('port')
      .isInt({ min: 1, max: 65535 })
      .withMessage('Port must be between 1 and 65535'),
    body('username')
      .notEmpty()
      .withMessage('Username is required'),
    body('password')
      .notEmpty()
      .withMessage('Password is required'),
    body('useHttps')
      .optional()
      .isBoolean()
      .withMessage('useHttps must be a boolean')
  ],
  async (req: AuthRequest, res: Response): Promise<void> => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
        return
      }

      const { ipAddress, port, username, password, useHttps = false } = req.body
      
      const credentials = {
        ipAddress,
        port,
        username,
        password,
        useHttps
      }

      const startTime = Date.now()
      const isConnected = await req.networkDiscovery!.testCameraConnection(credentials)
      const responseTime = Date.now() - startTime

      res.json({
        ipAddress,
        port,
        isConnected,
        responseTime,
        credentials: {
          username,
          useHttps
        }
      })

    } catch (error) {
      logger.error('Failed to test camera connection:', error)
      res.status(500).json({
        error: 'Failed to test camera connection',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
)

// Set up real-time events for scan progress
networkDiscovery.on('scanStarted', (data) => {
  logger.info(`Network scan started: ${data.totalHosts} hosts to scan`)
})

networkDiscovery.on('scanProgress', (data) => {
  logger.info(`Scan progress: ${data.percent}% (${data.progress}/${data.total})`)
})

networkDiscovery.on('cameraFound', (camera: DiscoveredCamera) => {
  logger.info(`Camera discovered: ${camera.ipAddress}:${camera.port} (${camera.responseTime}ms)`)
})

networkDiscovery.on('scanCompleted', (data) => {
  logger.info(`Network scan completed: ${data.total} cameras found`)
})

networkDiscovery.on('scanError', (error) => {
  logger.error('Network scan error:', error)
})

networkDiscovery.on('scanStopped', () => {
  logger.info('Network scan stopped')
})

export default router

import express, { Request, Response } from 'express'
import { body, query, param, validationResult } from 'express-validator'
import { Event, IEvent } from '../models/Event'
import { Camera } from '../models/Camera'
import { authenticate, authorize, operatorAccess, viewerAccess, AuthRequest } from '../middleware/auth'
import mongoose from 'mongoose'

const router = express.Router()

// Get all events with filtering and pagination
router.get('/',
  authenticate,
  viewerAccess,
  [
    query('status').optional().isIn(['active', 'acknowledged', 'resolved', 'dismissed', 'investigating']),
    query('type').optional().isIn(['motion', 'person', 'vehicle', 'face', 'intrusion', 'object', 'audio', 'system']),
    query('severity').optional().isIn(['low', 'medium', 'high', 'critical']),
    query('cameraId').optional().isMongoId(),
    query('location').optional().isString(),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601(),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('page').optional().isInt({ min: 1 }),
    query('sortBy').optional().isIn(['timestamp', 'severity', 'confidence', 'status']),
    query('sortOrder').optional().isIn(['asc', 'desc'])
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const {
        status,
        type,
        severity,
        cameraId,
        location,
        startDate,
        endDate,
        limit = 50,
        page = 1,
        sortBy = 'timestamp',
        sortOrder = 'desc'
      } = req.query

      const skip = (Number(page) - 1) * Number(limit)

      // Build filter
      const filter: any = { isArchived: false }
      
      if (status) filter.status = status
      if (type) filter.type = type
      if (severity) filter.severity = severity
      if (cameraId) filter.cameraId = cameraId
      if (location) filter.location = new RegExp(location as string, 'i')
      
      if (startDate || endDate) {
        filter.timestamp = {}
        if (startDate) filter.timestamp.$gte = new Date(startDate as string)
        if (endDate) filter.timestamp.$lte = new Date(endDate as string)
      }

      // Build sort
      const sort: any = {}
      sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1

      const events = await Event.find(filter)
        .sort(sort)
        .limit(Number(limit))
        .skip(skip)
        .populate('cameraId', 'name location')
        .populate('acknowledgment.acknowledgedBy', 'firstName lastName')
        .populate('resolution.resolvedBy', 'firstName lastName')

      const total = await Event.countDocuments(filter)

      res.json({
        events,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      })
    } catch (error) {
      console.error('Get events error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get event by ID
router.get('/:id',
  authenticate,
  viewerAccess,
  [
    param('id').isMongoId().withMessage('Invalid event ID')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const event = await Event.findById(req.params.id)
        .populate('cameraId', 'name location ipAddress')
        .populate('acknowledgment.acknowledgedBy', 'firstName lastName email')
        .populate('resolution.resolvedBy', 'firstName lastName email')
      
      if (!event) {
        return res.status(404).json({
          error: 'Event not found'
        })
      }

      res.json({ event })
    } catch (error) {
      console.error('Get event error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Create new event
router.post('/',
  authenticate,
  operatorAccess,
  [
    body('type').isIn(['motion', 'person', 'vehicle', 'face', 'intrusion', 'object', 'audio', 'system']),
    body('title').isLength({ min: 1, max: 200 }),
    body('description').isLength({ min: 1, max: 1000 }),
    body('severity').isIn(['low', 'medium', 'high', 'critical']),
    body('cameraId').isMongoId(),
    body('confidence').isFloat({ min: 0, max: 100 }),
    body('timestamp').optional().isISO8601()
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      // Verify camera exists
      const camera = await Camera.findById(req.body.cameraId)
      if (!camera) {
        return res.status(404).json({
          error: 'Camera not found'
        })
      }

      const eventData = {
        ...req.body,
        cameraName: camera.name,
        location: camera.location,
        timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date()
      }

      const event = new Event(eventData)
      await event.save()

      // Update camera statistics
      camera.statistics.totalEvents += 1
      camera.statistics.eventsToday += 1
      await camera.save()

      res.status(201).json({
        message: 'Event created successfully',
        event
      })
    } catch (error) {
      console.error('Create event error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Acknowledge event
router.patch('/:id/acknowledge',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid event ID'),
    body('notes').optional().isLength({ max: 500 })
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const event = await Event.findById(req.params.id)
      if (!event) {
        return res.status(404).json({
          error: 'Event not found'
        })
      }

      if (event.status !== 'active') {
        return res.status(400).json({
          error: 'Only active events can be acknowledged'
        })
      }

      await event.acknowledge(req.user!._id, req.body.notes)

      res.json({
        message: 'Event acknowledged successfully',
        event
      })
    } catch (error) {
      console.error('Acknowledge event error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Resolve event
router.patch('/:id/resolve',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid event ID'),
    body('resolution').isLength({ min: 1, max: 500 }),
    body('notes').optional().isLength({ max: 1000 })
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const event = await Event.findById(req.params.id)
      if (!event) {
        return res.status(404).json({
          error: 'Event not found'
        })
      }

      if (event.status === 'resolved') {
        return res.status(400).json({
          error: 'Event is already resolved'
        })
      }

      await event.resolve(req.user!._id, req.body.resolution, req.body.notes)

      res.json({
        message: 'Event resolved successfully',
        event
      })
    } catch (error) {
      console.error('Resolve event error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Update event status
router.patch('/:id/status',
  authenticate,
  operatorAccess,
  [
    param('id').isMongoId().withMessage('Invalid event ID'),
    body('status').isIn(['active', 'acknowledged', 'resolved', 'dismissed', 'investigating'])
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const event = await Event.findById(req.params.id)
      if (!event) {
        return res.status(404).json({
          error: 'Event not found'
        })
      }

      event.status = req.body.status
      await event.save()

      res.json({
        message: 'Event status updated successfully',
        event
      })
    } catch (error) {
      console.error('Update event status error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Archive event
router.patch('/:id/archive',
  authenticate,
  authorize('admin'),
  [
    param('id').isMongoId().withMessage('Invalid event ID')
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const event = await Event.findById(req.params.id)
      if (!event) {
        return res.status(404).json({
          error: 'Event not found'
        })
      }

      event.isArchived = true
      await event.save()

      res.json({
        message: 'Event archived successfully'
      })
    } catch (error) {
      console.error('Archive event error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get events by camera
router.get('/camera/:cameraId',
  authenticate,
  viewerAccess,
  [
    param('cameraId').isMongoId().withMessage('Invalid camera ID'),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('page').optional().isInt({ min: 1 })
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { limit = 50, page = 1 } = req.query
      const skip = (Number(page) - 1) * Number(limit)

      const events = await Event.find({ 
        cameraId: req.params.cameraId,
        isArchived: false 
      })
        .sort({ timestamp: -1 })
        .limit(Number(limit))
        .skip(skip)

      const total = await Event.countDocuments({ 
        cameraId: req.params.cameraId,
        isArchived: false 
      })

      res.json({
        events,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      })
    } catch (error) {
      console.error('Get camera events error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

// Get event statistics
router.get('/stats/summary',
  authenticate,
  viewerAccess,
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  async (req: AuthRequest, res: Response) => {
    try {
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          error: 'Validation failed',
          details: errors.array()
        })
      }

      const { startDate, endDate } = req.query
      const filter: any = { isArchived: false }

      if (startDate || endDate) {
        filter.timestamp = {}
        if (startDate) filter.timestamp.$gte = new Date(startDate as string)
        if (endDate) filter.timestamp.$lte = new Date(endDate as string)
      }

      const [
        totalEvents,
        activeEvents,
        eventsByType,
        eventsBySeverity,
        eventsByStatus
      ] = await Promise.all([
        Event.countDocuments(filter),
        Event.countDocuments({ ...filter, status: 'active' }),
        Event.aggregate([
          { $match: filter },
          { $group: { _id: '$type', count: { $sum: 1 } } }
        ]),
        Event.aggregate([
          { $match: filter },
          { $group: { _id: '$severity', count: { $sum: 1 } } }
        ]),
        Event.aggregate([
          { $match: filter },
          { $group: { _id: '$status', count: { $sum: 1 } } }
        ])
      ])

      res.json({
        summary: {
          totalEvents,
          activeEvents,
          resolvedEvents: totalEvents - activeEvents
        },
        breakdown: {
          byType: eventsByType,
          bySeverity: eventsBySeverity,
          byStatus: eventsByStatus
        }
      })
    } catch (error) {
      console.error('Get event statistics error:', error)
      res.status(500).json({
        error: 'Internal server error'
      })
    }
  }
)

export default router

import { EventEmitter } from 'events'
import winston from 'winston'
import { ISAPIClient, HikVisionCredentials, CameraCapabilities, CameraStatus } from './ISAPIClient'
import { EventListener, CameraEvent } from './EventListener'
import { ICamera } from '../../models/Camera'
import { Event } from '../../models/Event'
import mongoose from 'mongoose'

export interface CameraConnection {
  cameraId: string
  isApiClient: ISAPIClient
  eventListener: EventListener
  isConnected: boolean
  lastHeartbeat: Date
  connectionAttempts: number
}

export class CameraManager extends EventEmitter {
  private connections: Map<string, CameraConnection> = new Map()
  private logger: winston.Logger
  private heartbeatInterval: NodeJS.Timeout | null = null
  private heartbeatIntervalMs: number = 30000 // 30 seconds

  constructor(logger: winston.Logger) {
    super()
    this.logger = logger
    this.startHeartbeatMonitoring()
  }

  /**
   * Add a camera to the manager
   */
  async addCamera(camera: ICamera): Promise<boolean> {
    const cameraId = camera._id?.toString()
    if (!cameraId) {
      this.logger.error('Camera ID is required')
      return false
    }
    try {
      const credentials: HikVisionCredentials = {
        username: camera.username,
        password: camera.password,
        ipAddress: camera.ipAddress,
        port: camera.port
      }

      // Create ISAPI client
      const isapiClient = new ISAPIClient(credentials, this.logger)
      
      // Test connection first
      const isConnected = await isapiClient.testConnection()
      if (!isConnected) {
        this.logger.error(`Failed to connect to camera ${camera._id} at ${camera.ipAddress}:${camera.port}`)
        return false
      }

      // Create event listener
      const eventListener = new EventListener(credentials, cameraId, this.logger)

      // Set up event handlers
      eventListener.on('event', (event: CameraEvent) => {
        this.handleCameraEvent(cameraId, event)
      })

      eventListener.on('connected', () => {
        this.logger.info(`Event listener connected for camera ${cameraId}`)
        this.updateCameraStatus(cameraId, 'online')
      })

      eventListener.on('disconnected', (reason: string) => {
        this.logger.warn(`Event listener disconnected for camera ${cameraId}: ${reason}`)
        this.updateCameraStatus(cameraId, 'offline')
      })

      // Store connection
      const connection: CameraConnection = {
        cameraId: cameraId,
        isApiClient: isapiClient,
        eventListener: eventListener,
        isConnected: true,
        lastHeartbeat: new Date(),
        connectionAttempts: 0
      }

      this.connections.set(cameraId, connection)

      // Start event listening
      await eventListener.startListening()

      // Get and update camera capabilities
      try {
        const capabilities = await isapiClient.getDeviceInfo()
        await this.updateCameraCapabilities(cameraId, capabilities)
      } catch (error) {
        this.logger.warn(`Failed to get capabilities for camera ${cameraId}:`, error)
      }

      this.logger.info(`Successfully added camera ${cameraId} (${camera.name}) to manager`)
      this.emit('cameraAdded', cameraId)
      
      return true
    } catch (error) {
      this.logger.error(`Failed to add camera ${cameraId}:`, error)
      return false
    }
  }

  /**
   * Remove a camera from the manager
   */
  removeCamera(cameraId: string): boolean {
    const connection = this.connections.get(cameraId)
    if (!connection) {
      return false
    }

    // Stop event listener
    connection.eventListener.stopListening()

    // Remove from connections
    this.connections.delete(cameraId)

    this.logger.info(`Removed camera ${cameraId} from manager`)
    this.emit('cameraRemoved', cameraId)
    
    return true
  }

  /**
   * ARM a camera (enable motion detection)
   */
  async armCamera(cameraId: string): Promise<boolean> {
    const connection = this.connections.get(cameraId)
    if (!connection) {
      this.logger.error(`Camera ${cameraId} not found in manager`)
      return false
    }

    try {
      const success = await connection.isApiClient.armCamera()
      if (success) {
        this.logger.info(`Successfully armed camera ${cameraId}`)
        this.emit('cameraArmed', cameraId)
      }
      return success
    } catch (error) {
      this.logger.error(`Failed to arm camera ${cameraId}:`, error)
      return false
    }
  }

  /**
   * DISARM a camera (disable motion detection)
   */
  async disarmCamera(cameraId: string): Promise<boolean> {
    const connection = this.connections.get(cameraId)
    if (!connection) {
      this.logger.error(`Camera ${cameraId} not found in manager`)
      return false
    }

    try {
      const success = await connection.isApiClient.disarmCamera()
      if (success) {
        this.logger.info(`Successfully disarmed camera ${cameraId}`)
        this.emit('cameraDisarmed', cameraId)
      }
      return success
    } catch (error) {
      this.logger.error(`Failed to disarm camera ${cameraId}:`, error)
      return false
    }
  }

  /**
   * Get camera status
   */
  async getCameraStatus(cameraId: string): Promise<CameraStatus | null> {
    const connection = this.connections.get(cameraId)
    if (!connection) {
      return null
    }

    try {
      return await connection.isApiClient.getCameraStatus()
    } catch (error) {
      this.logger.error(`Failed to get status for camera ${cameraId}:`, error)
      return null
    }
  }

  /**
   * Capture snapshot from camera
   */
  async captureSnapshot(cameraId: string): Promise<Buffer | null> {
    const connection = this.connections.get(cameraId)
    if (!connection) {
      return null
    }

    try {
      return await connection.isApiClient.captureSnapshot()
    } catch (error) {
      this.logger.error(`Failed to capture snapshot for camera ${cameraId}:`, error)
      return null
    }
  }

  /**
   * Get RTSP stream URL for camera
   */
  getRTSPStreamURL(cameraId: string, channel: number = 101): string | null {
    const connection = this.connections.get(cameraId)
    if (!connection) {
      return null
    }

    return connection.isApiClient.getRTSPStreamURL(channel)
  }

  /**
   * Get all connected cameras
   */
  getConnectedCameras(): string[] {
    return Array.from(this.connections.keys()).filter(cameraId => {
      const connection = this.connections.get(cameraId)
      return connection?.isConnected
    })
  }

  /**
   * Get connection status for all cameras
   */
  getConnectionStatus(): { [cameraId: string]: any } {
    const status: { [cameraId: string]: any } = {}
    
    for (const [cameraId, connection] of this.connections) {
      status[cameraId] = {
        isConnected: connection.isConnected,
        lastHeartbeat: connection.lastHeartbeat,
        connectionAttempts: connection.connectionAttempts,
        eventListenerStatus: connection.eventListener.getStatus()
      }
    }
    
    return status
  }

  /**
   * Handle camera events
   */
  private async handleCameraEvent(cameraId: string, event: CameraEvent): Promise<void> {
    try {
      // Create event in database
      const newEvent = new Event({
        eventId: `${cameraId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: event.eventType,
        title: `${event.eventType.charAt(0).toUpperCase() + event.eventType.slice(1)} Detection`,
        description: event.description,
        severity: this.mapEventSeverity(event.eventType),
        status: 'active',
        cameraId: new mongoose.Types.ObjectId(cameraId),
        timestamp: event.timestamp,
        metadata: {
          channelId: event.channelId,
          region: event.region,
          confidence: event.confidence,
          ...event.metadata
        }
      })

      await newEvent.save()

      this.logger.info(`Created event ${newEvent.eventId} for camera ${cameraId}`)
      this.emit('cameraEvent', cameraId, newEvent)

    } catch (error) {
      this.logger.error(`Failed to handle camera event for ${cameraId}:`, error)
    }
  }

  /**
   * Map event type to severity
   */
  private mapEventSeverity(eventType: string): 'low' | 'medium' | 'high' | 'critical' {
    const severityMap: { [key: string]: 'low' | 'medium' | 'high' | 'critical' } = {
      'motion': 'low',
      'intrusion': 'high',
      'lineDetection': 'medium',
      'faceDetection': 'medium',
      'vehicleDetection': 'medium',
      'tamperDetection': 'critical'
    }

    return severityMap[eventType] || 'medium'
  }

  /**
   * Update camera status in database
   */
  private async updateCameraStatus(cameraId: string, status: 'online' | 'offline' | 'error'): Promise<void> {
    try {
      const Camera = mongoose.model('Camera')
      await Camera.findByIdAndUpdate(cameraId, {
        status: status,
        lastSeen: new Date(),
        lastHeartbeat: new Date()
      })
    } catch (error) {
      this.logger.error(`Failed to update camera status for ${cameraId}:`, error)
    }
  }

  /**
   * Update camera capabilities in database
   */
  private async updateCameraCapabilities(cameraId: string, capabilities: CameraCapabilities): Promise<void> {
    try {
      const Camera = mongoose.model('Camera')
      await Camera.findByIdAndUpdate(cameraId, {
        firmware: capabilities.deviceInfo.firmwareVersion,
        'network.rtspUrl': `rtsp://username:password@ip:554/Streaming/Channels/101`,
        'statistics.lastReboot': new Date()
      })
    } catch (error) {
      this.logger.error(`Failed to update camera capabilities for ${cameraId}:`, error)
    }
  }

  /**
   * Start heartbeat monitoring
   */
  private startHeartbeatMonitoring(): void {
    this.heartbeatInterval = setInterval(async () => {
      for (const [cameraId, connection] of this.connections) {
        try {
          const status = await connection.isApiClient.getCameraStatus()
          if (status) {
            connection.lastHeartbeat = new Date()
            connection.isConnected = true
            await this.updateCameraStatus(cameraId, 'online')
          }
        } catch (error) {
          connection.isConnected = false
          await this.updateCameraStatus(cameraId, 'offline')
          this.logger.warn(`Heartbeat failed for camera ${cameraId}:`, error)
        }
      }
    }, this.heartbeatIntervalMs)
  }

  /**
   * Stop the camera manager
   */
  stop(): void {
    // Stop heartbeat monitoring
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    // Stop all event listeners
    for (const connection of this.connections.values()) {
      connection.eventListener.stopListening()
    }

    // Clear connections
    this.connections.clear()

    this.logger.info('Camera manager stopped')
  }
}

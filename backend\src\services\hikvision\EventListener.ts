import axios, { AxiosInstance } from 'axios'
import { EventEmitter } from 'events'
import winston from 'winston'
import { parseString } from 'xml2js'
import { promisify } from 'util'
import { HikVisionCredentials, EventNotification } from './ISAPIClient'

const parseXML = promisify(parseString)

export interface CameraEvent {
  cameraId: string
  eventType: 'motion' | 'intrusion' | 'lineDetection' | 'faceDetection' | 'vehicleDetection' | 'tamperDetection'
  eventState: 'active' | 'inactive'
  channelId: number
  timestamp: Date
  description: string
  confidence?: number
  region?: {
    x: number
    y: number
    width: number
    height: number
  }
  metadata?: any
}

export class EventListener extends EventEmitter {
  private credentials: HikVisionCredentials
  private logger: winston.Logger
  private axiosInstance: AxiosInstance
  private isListening: boolean = false
  private reconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectDelay: number = 5000
  private eventStream: any = null
  private cameraId: string

  constructor(credentials: HikVisionCredentials, cameraId: string, logger: winston.Logger) {
    super()
    this.credentials = credentials
    this.cameraId = cameraId
    this.logger = logger

    this.axiosInstance = axios.create({
      baseURL: `http://${credentials.ipAddress}:${credentials.port}`,
      timeout: 0, // No timeout for event stream
      auth: {
        username: credentials.username,
        password: credentials.password
      }
    })
  }

  /**
   * Start listening for camera events
   */
  async startListening(): Promise<void> {
    if (this.isListening) {
      this.logger.warn(`Event listener already running for camera ${this.cameraId}`)
      return
    }

    this.isListening = true
    this.logger.info(`Starting event listener for camera ${this.cameraId}`)

    try {
      await this.connectToEventStream()
    } catch (error) {
      this.logger.error(`Failed to start event listener for camera ${this.cameraId}:`, error)
      this.handleReconnection()
    }
  }

  /**
   * Stop listening for camera events
   */
  stopListening(): void {
    this.isListening = false
    this.reconnectAttempts = 0

    if (this.eventStream) {
      this.eventStream.destroy()
      this.eventStream = null
    }

    this.logger.info(`Stopped event listener for camera ${this.cameraId}`)
  }

  /**
   * Connect to HikVision event stream
   */
  private async connectToEventStream(): Promise<void> {
    try {
      const response = await this.axiosInstance.get('/ISAPI/Event/notification/alertStream', {
        responseType: 'stream',
        headers: {
          'Accept': 'multipart/x-mixed-replace'
        }
      })

      this.eventStream = response.data
      this.reconnectAttempts = 0

      let buffer = ''

      this.eventStream.on('data', (chunk: Buffer) => {
        buffer += chunk.toString()
        this.processEventBuffer(buffer)
      })

      this.eventStream.on('end', () => {
        this.logger.warn(`Event stream ended for camera ${this.cameraId}`)
        if (this.isListening) {
          this.handleReconnection()
        }
      })

      this.eventStream.on('error', (error: Error) => {
        this.logger.error(`Event stream error for camera ${this.cameraId}:`, error)
        if (this.isListening) {
          this.handleReconnection()
        }
      })

      this.logger.info(`Connected to event stream for camera ${this.cameraId}`)
      this.emit('connected')

    } catch (error) {
      this.logger.error(`Failed to connect to event stream for camera ${this.cameraId}:`, error)
      throw error
    }
  }

  /**
   * Process incoming event data buffer
   */
  private processEventBuffer(buffer: string): void {
    const events = buffer.split('\r\n\r\n')
    
    for (const eventData of events) {
      if (eventData.trim() && eventData.includes('<EventNotificationAlert>')) {
        this.parseAndEmitEvent(eventData)
      }
    }
  }

  /**
   * Parse XML event data and emit structured event
   */
  private async parseAndEmitEvent(xmlData: string): Promise<void> {
    try {
      const parsedData = await parseXML(xmlData) as any
      const alert = parsedData?.EventNotificationAlert

      if (!alert) {
        return
      }

      const cameraEvent: CameraEvent = {
        cameraId: this.cameraId,
        eventType: this.mapEventType(alert.eventType),
        eventState: alert.eventState === 'active' ? 'active' : 'inactive',
        channelId: parseInt(alert.channelID) || 1,
        timestamp: new Date(alert.dateTime),
        description: alert.eventDescription || 'Camera event detected',
        metadata: {
          activePostCount: parseInt(alert.activePostCount) || 0,
          eventId: alert.eventId,
          rawEventType: alert.eventType
        }
      }

      // Extract region information if available
      if (alert.DetectionRegionList?.DetectionRegionEntry) {
        const region = alert.DetectionRegionList.DetectionRegionEntry
        cameraEvent.region = {
          x: parseInt(region.regionCoordinatesList?.regionCoordinates?.positionX) || 0,
          y: parseInt(region.regionCoordinatesList?.regionCoordinates?.positionY) || 0,
          width: parseInt(region.regionCoordinatesList?.regionCoordinates?.width) || 0,
          height: parseInt(region.regionCoordinatesList?.regionCoordinates?.height) || 0
        }
      }

      this.logger.debug(`Received event for camera ${this.cameraId}:`, cameraEvent)
      this.emit('event', cameraEvent)

    } catch (error) {
      this.logger.error(`Failed to parse event data for camera ${this.cameraId}:`, error)
    }
  }

  /**
   * Map HikVision event types to our standardized types
   */
  private mapEventType(hikVisionEventType: string): CameraEvent['eventType'] {
    const eventTypeMap: { [key: string]: CameraEvent['eventType'] } = {
      'VMD': 'motion',
      'videomotion': 'motion',
      'motiondetection': 'motion',
      'fielddetection': 'intrusion',
      'regionEntrance': 'intrusion',
      'regionExiting': 'intrusion',
      'linedetection': 'lineDetection',
      'crossLineDetection': 'lineDetection',
      'facedetection': 'faceDetection',
      'vehicleDetection': 'vehicleDetection',
      'tamperdetection': 'tamperDetection',
      'videoloss': 'tamperDetection',
      'shelteralarm': 'tamperDetection'
    }

    return eventTypeMap[hikVisionEventType] || 'motion'
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnection(): void {
    if (!this.isListening || this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error(`Max reconnection attempts reached for camera ${this.cameraId}`)
      this.emit('disconnected', 'max_attempts_reached')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * this.reconnectAttempts

    this.logger.info(`Attempting to reconnect to camera ${this.cameraId} (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`)

    setTimeout(async () => {
      if (this.isListening) {
        try {
          await this.connectToEventStream()
        } catch (error) {
          this.logger.error(`Reconnection attempt ${this.reconnectAttempts} failed for camera ${this.cameraId}:`, error)
          this.handleReconnection()
        }
      }
    }, delay)
  }

  /**
   * Get connection status
   */
  getStatus(): {
    isListening: boolean
    reconnectAttempts: number
    isConnected: boolean
  } {
    return {
      isListening: this.isListening,
      reconnectAttempts: this.reconnectAttempts,
      isConnected: this.eventStream !== null && !this.eventStream.destroyed
    }
  }
}

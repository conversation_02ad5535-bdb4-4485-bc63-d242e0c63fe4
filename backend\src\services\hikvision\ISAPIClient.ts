import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import crypto from 'crypto'
import { parseString } from 'xml2js'
import { promisify } from 'util'
import winston from 'winston'

const parseXML = promisify(parseString)

export interface HikVisionCredentials {
  username: string
  password: string
  ipAddress: string
  port: number
}

export interface CameraCapabilities {
  deviceInfo: {
    deviceName: string
    deviceID: string
    model: string
    serialNumber: string
    macAddress: string
    firmwareVersion: string
    firmwareReleasedDate: string
  }
  systemCapabilities: {
    isSupportLocalOutputTerminal: boolean
    isSupportCentralizeManagement: boolean
    isSupportAlarmServer: boolean
    isSupportCentralizeConfig: boolean
  }
  videoInputChannels: number
  audioInputChannels: number
  videoOutputChannels: number
  audioOutputChannels: number
}

export interface CameraStatus {
  deviceStatus: 'online' | 'offline' | 'error'
  cpuUsage: number
  memoryUsage: number
  temperature: number
  uptime: number
  recordingStatus: boolean
  motionDetectionEnabled: boolean
  isArmed: boolean
}

export interface MotionDetectionRegion {
  id: number
  enabled: boolean
  regionType: string
  coordinatesList: Array<{ positionX: number; positionY: number }>
  sensitivityLevel: number
}

export interface EventNotification {
  eventType: string
  eventState: string
  eventDescription: string
  channelID: number
  dateTime: string
  activePostCount: number
  eventId: string
}

export class ISAPIClient {
  private axiosInstance: AxiosInstance
  private credentials: HikVisionCredentials
  private logger: winston.Logger
  private digestAuth: {
    realm?: string
    nonce?: string
    qop?: string
    opaque?: string
    algorithm?: string
  } = {}

  constructor(credentials: HikVisionCredentials, logger: winston.Logger) {
    this.credentials = credentials
    this.logger = logger

    this.axiosInstance = axios.create({
      baseURL: `http://${credentials.ipAddress}:${credentials.port}`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/xml',
        'Accept': 'application/xml, text/xml, */*'
      }
    })

    // Add request interceptor for digest authentication
    this.axiosInstance.interceptors.request.use(
      (config) => {
        this.addDigestAuth(config)
        return config
      },
      (error) => Promise.reject(error)
    )

    // Add response interceptor for handling 401 responses
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && !error.config._retry) {
          error.config._retry = true
          await this.handleUnauthorized(error.response)
          return this.axiosInstance.request(error.config)
        }
        return Promise.reject(error)
      }
    )
  }

  /**
   * Handle 401 Unauthorized responses and extract digest auth parameters
   */
  private async handleUnauthorized(response: AxiosResponse): Promise<void> {
    const authHeader = response.headers['www-authenticate']
    if (authHeader && authHeader.startsWith('Digest ')) {
      this.parseDigestAuthHeader(authHeader)
    }
  }

  /**
   * Parse WWW-Authenticate header to extract digest parameters
   */
  private parseDigestAuthHeader(authHeader: string): void {
    const digestParams = authHeader.substring(7) // Remove 'Digest '
    const params = digestParams.split(',').reduce((acc, param) => {
      const [key, value] = param.split('=').map(s => s.trim())
      if (key && value) {
        acc[key] = value.replace(/"/g, '')
      }
      return acc
    }, {} as any)

    this.digestAuth = {
      realm: params.realm,
      nonce: params.nonce,
      qop: params.qop,
      opaque: params.opaque,
      algorithm: params.algorithm || 'MD5'
    }
  }

  /**
   * Add digest authentication to request
   */
  private addDigestAuth(config: AxiosRequestConfig): AxiosRequestConfig {
    if (!this.digestAuth.nonce) {
      return config
    }

    const method = config.method?.toUpperCase() || 'GET'
    const uri = config.url || ''
    const nc = '00000001'
    const cnonce = crypto.randomBytes(16).toString('hex')

    // Calculate HA1
    const ha1 = crypto
      .createHash('md5')
      .update(`${this.credentials.username}:${this.digestAuth.realm}:${this.credentials.password}`)
      .digest('hex')

    // Calculate HA2
    const ha2 = crypto
      .createHash('md5')
      .update(`${method}:${uri}`)
      .digest('hex')

    // Calculate response
    let response: string
    if (this.digestAuth.qop === 'auth') {
      response = crypto
        .createHash('md5')
        .update(`${ha1}:${this.digestAuth.nonce}:${nc}:${cnonce}:${this.digestAuth.qop}:${ha2}`)
        .digest('hex')
    } else {
      response = crypto
        .createHash('md5')
        .update(`${ha1}:${this.digestAuth.nonce}:${ha2}`)
        .digest('hex')
    }

    // Build authorization header
    let authHeader = `Digest username="${this.credentials.username}", realm="${this.digestAuth.realm}", nonce="${this.digestAuth.nonce}", uri="${uri}", response="${response}"`
    
    if (this.digestAuth.qop) {
      authHeader += `, qop=${this.digestAuth.qop}, nc=${nc}, cnonce="${cnonce}"`
    }
    
    if (this.digestAuth.opaque) {
      authHeader += `, opaque="${this.digestAuth.opaque}"`
    }

    config.headers = {
      ...config.headers,
      Authorization: authHeader
    }

    return config
  }

  /**
   * Test connection to camera
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.axiosInstance.get('/ISAPI/System/deviceInfo')
      return response.status === 200
    } catch (error) {
      this.logger.error('Connection test failed:', error)
      return false
    }
  }

  /**
   * Get device information and capabilities
   */
  async getDeviceInfo(): Promise<CameraCapabilities> {
    try {
      const response = await this.axiosInstance.get('/ISAPI/System/deviceInfo')
      const deviceInfo = await this.parseXMLResponse(response.data)

      const capResponse = await this.axiosInstance.get('/ISAPI/System/capabilities')
      const capabilities = await this.parseXMLResponse(capResponse.data)

      return {
        deviceInfo: {
          deviceName: deviceInfo.DeviceInfo?.deviceName?.[0] || 'Unknown',
          deviceID: deviceInfo.DeviceInfo?.deviceID?.[0] || 'Unknown',
          model: deviceInfo.DeviceInfo?.model?.[0] || 'Unknown',
          serialNumber: deviceInfo.DeviceInfo?.serialNumber?.[0] || 'Unknown',
          macAddress: deviceInfo.DeviceInfo?.macAddress?.[0] || 'Unknown',
          firmwareVersion: deviceInfo.DeviceInfo?.firmwareVersion?.[0] || 'Unknown',
          firmwareReleasedDate: deviceInfo.DeviceInfo?.firmwareReleasedDate?.[0] || 'Unknown'
        },
        systemCapabilities: {
          isSupportLocalOutputTerminal: capabilities.DeviceCap?.SysCap?.isSupportLocalOutputTerminal?.[0] === 'true',
          isSupportCentralizeManagement: capabilities.DeviceCap?.SysCap?.isSupportCentralizeManagement?.[0] === 'true',
          isSupportAlarmServer: capabilities.DeviceCap?.SysCap?.isSupportAlarmServer?.[0] === 'true',
          isSupportCentralizeConfig: capabilities.DeviceCap?.SysCap?.isSupportCentralizeConfig?.[0] === 'true'
        },
        videoInputChannels: parseInt(capabilities.DeviceCap?.VideoCap?.videoInputPortNums?.[0] || '0'),
        audioInputChannels: parseInt(capabilities.DeviceCap?.AudioCap?.audioInputPortNums?.[0] || '0'),
        videoOutputChannels: parseInt(capabilities.DeviceCap?.VideoCap?.videoOutputPortNums?.[0] || '0'),
        audioOutputChannels: parseInt(capabilities.DeviceCap?.AudioCap?.audioOutputPortNums?.[0] || '0')
      }
    } catch (error) {
      this.logger.error('Failed to get device info:', error)
      throw new Error('Failed to retrieve device information')
    }
  }

  /**
   * Get camera status
   */
  async getCameraStatus(): Promise<CameraStatus> {
    try {
      const statusResponse = await this.axiosInstance.get('/ISAPI/System/status')
      const statusData = await this.parseXMLResponse(statusResponse.data)

      const motionResponse = await this.axiosInstance.get('/ISAPI/System/Video/inputs/channels/1/motionDetection')
      const motionData = await this.parseXMLResponse(motionResponse.data)

      return {
        deviceStatus: 'online',
        cpuUsage: parseInt(statusData.DeviceStatus?.cpuUsage?.[0] || '0'),
        memoryUsage: parseInt(statusData.DeviceStatus?.memoryUsage?.[0] || '0'),
        temperature: parseInt(statusData.DeviceStatus?.temperature?.[0] || '0'),
        uptime: parseInt(statusData.DeviceStatus?.upTime?.[0] || '0'),
        recordingStatus: statusData.DeviceStatus?.recordingStatus?.[0] === 'true',
        motionDetectionEnabled: motionData.MotionDetection?.enabled?.[0] === 'true',
        isArmed: motionData.MotionDetection?.enabled?.[0] === 'true'
      }
    } catch (error) {
      this.logger.error('Failed to get camera status:', error)
      return {
        deviceStatus: 'error',
        cpuUsage: 0,
        memoryUsage: 0,
        temperature: 0,
        uptime: 0,
        recordingStatus: false,
        motionDetectionEnabled: false,
        isArmed: false
      }
    }
  }

  /**
   * ARM camera (enable motion detection)
   */
  async armCamera(): Promise<boolean> {
    try {
      const motionDetectionXML = `<?xml version="1.0" encoding="UTF-8"?>
<MotionDetection>
  <enabled>true</enabled>
  <enableHighlight>false</enableHighlight>
  <samplingInterval>2</samplingInterval>
  <startTriggerTime>500</startTriggerTime>
  <endTriggerTime>500</endTriggerTime>
</MotionDetection>`

      const response = await this.axiosInstance.put(
        '/ISAPI/System/Video/inputs/channels/1/motionDetection',
        motionDetectionXML,
        {
          headers: {
            'Content-Type': 'application/xml'
          }
        }
      )

      return response.status === 200
    } catch (error) {
      this.logger.error('Failed to ARM camera:', error)
      return false
    }
  }

  /**
   * DISARM camera (disable motion detection)
   */
  async disarmCamera(): Promise<boolean> {
    try {
      const motionDetectionXML = `<?xml version="1.0" encoding="UTF-8"?>
<MotionDetection>
  <enabled>false</enabled>
  <enableHighlight>false</enableHighlight>
  <samplingInterval>2</samplingInterval>
  <startTriggerTime>500</startTriggerTime>
  <endTriggerTime>500</endTriggerTime>
</MotionDetection>`

      const response = await this.axiosInstance.put(
        '/ISAPI/System/Video/inputs/channels/1/motionDetection',
        motionDetectionXML,
        {
          headers: {
            'Content-Type': 'application/xml'
          }
        }
      )

      return response.status === 200
    } catch (error) {
      this.logger.error('Failed to DISARM camera:', error)
      return false
    }
  }

  /**
   * Get motion detection regions
   */
  async getMotionDetectionRegions(): Promise<MotionDetectionRegion[]> {
    try {
      const response = await this.axiosInstance.get('/ISAPI/System/Video/inputs/channels/1/motionDetection/regions')
      const regionsData = await this.parseXMLResponse(response.data)

      const regions: MotionDetectionRegion[] = []
      const regionList = regionsData.MotionDetectionRegionList?.MotionDetectionRegion || []

      for (const region of regionList) {
        regions.push({
          id: parseInt(region.id?.[0] || '0'),
          enabled: region.enabled?.[0] === 'true',
          regionType: region.regionType?.[0] || 'grid',
          coordinatesList: region.coordinatesList?.[0]?.coordinates?.map((coord: any) => ({
            positionX: parseInt(coord.positionX?.[0] || '0'),
            positionY: parseInt(coord.positionY?.[0] || '0')
          })) || [],
          sensitivityLevel: parseInt(region.sensitivityLevel?.[0] || '50')
        })
      }

      return regions
    } catch (error) {
      this.logger.error('Failed to get motion detection regions:', error)
      return []
    }
  }

  /**
   * Capture snapshot from camera
   */
  async captureSnapshot(): Promise<Buffer | null> {
    try {
      const response = await this.axiosInstance.get('/ISAPI/Streaming/channels/101/picture', {
        responseType: 'arraybuffer'
      })

      return Buffer.from(response.data)
    } catch (error) {
      this.logger.error('Failed to capture snapshot:', error)
      return null
    }
  }

  /**
   * Get RTSP stream URL
   */
  getRTSPStreamURL(channel: number = 101): string {
    return `rtsp://${this.credentials.username}:${this.credentials.password}@${this.credentials.ipAddress}:554/Streaming/Channels/${channel}`
  }

  /**
   * Parse XML response to JavaScript object
   */
  private async parseXMLResponse(xmlData: string): Promise<any> {
    try {
      return await parseXML(xmlData)
    } catch (error) {
      this.logger.error('Failed to parse XML response:', error)
      throw new Error('Invalid XML response from camera')
    }
  }
}

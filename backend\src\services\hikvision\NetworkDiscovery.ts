import { EventEmitter } from 'events'
import winston from 'winston'
import axios, { AxiosResponse } from 'axios'
import { ISAPIClient, HikVisionCredentials } from './ISAPIClient'

export interface DiscoveredCamera {
  ipAddress: string
  port: number
  deviceInfo?: any
  capabilities?: any
  isReachable: boolean
  responseTime: number
  lastSeen: Date
}

export interface NetworkScanOptions {
  networkRange: string // e.g., "***********/24"
  portRange: number[] // e.g., [80, 8000, 8080]
  timeout: number // milliseconds
  maxConcurrent: number // max concurrent scans
  credentials?: HikVisionCredentials[]
}

export class NetworkDiscovery extends EventEmitter {
  private logger: winston.Logger
  private isScanning: boolean = false
  private scanProgress: number = 0
  private totalHosts: number = 0

  constructor(logger: winston.Logger) {
    super()
    this.logger = logger
  }

  /**
   * Scan network for HikVision cameras
   */
  async scanNetwork(options: NetworkScanOptions): Promise<DiscoveredCamera[]> {
    if (this.isScanning) {
      throw new Error('Network scan already in progress')
    }

    this.isScanning = true
    this.scanProgress = 0
    const discoveredCameras: DiscoveredCamera[] = []

    try {
      const ipAddresses = this.generateIPRange(options.networkRange)
      this.totalHosts = ipAddresses.length * options.portRange.length
      
      this.logger.info(`Starting network scan for ${this.totalHosts} host:port combinations`)
      this.emit('scanStarted', { totalHosts: this.totalHosts })

      // Create batches for concurrent scanning
      const batchSize = options.maxConcurrent
      const allCombinations: Array<{ip: string, port: number}> = []
      
      for (const ip of ipAddresses) {
        for (const port of options.portRange) {
          allCombinations.push({ ip, port })
        }
      }

      // Process in batches
      for (let i = 0; i < allCombinations.length; i += batchSize) {
        const batch = allCombinations.slice(i, i + batchSize)
        const batchPromises = batch.map(({ ip, port }) => 
          this.scanHost(ip, port, options.timeout, options.credentials)
        )

        const batchResults = await Promise.allSettled(batchPromises)
        
        for (const result of batchResults) {
          this.scanProgress++
          const progressPercent = Math.round((this.scanProgress / this.totalHosts) * 100)
          
          if (result.status === 'fulfilled' && result.value) {
            discoveredCameras.push(result.value)
            this.logger.info(`Found HikVision camera at ${result.value.ipAddress}:${result.value.port}`)
            this.emit('cameraFound', result.value)
          }
          
          this.emit('scanProgress', { 
            progress: this.scanProgress, 
            total: this.totalHosts, 
            percent: progressPercent 
          })
        }

        // Small delay between batches to avoid overwhelming the network
        if (i + batchSize < allCombinations.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      this.logger.info(`Network scan completed. Found ${discoveredCameras.length} cameras`)
      this.emit('scanCompleted', { cameras: discoveredCameras, total: discoveredCameras.length })
      
      return discoveredCameras

    } catch (error) {
      this.logger.error('Network scan failed:', error)
      this.emit('scanError', error)
      throw error
    } finally {
      this.isScanning = false
    }
  }

  /**
   * Scan a specific host and port for HikVision camera
   */
  private async scanHost(
    ipAddress: string, 
    port: number, 
    timeout: number,
    credentials?: HikVisionCredentials[]
  ): Promise<DiscoveredCamera | null> {
    const startTime = Date.now()
    
    try {
      // First, check if the host is reachable
      const isReachable = await this.checkHostReachability(ipAddress, port, timeout)
      if (!isReachable) {
        return null
      }

      // Try to identify if it's a HikVision camera
      const isHikVision = await this.identifyHikVisionCamera(ipAddress, port, timeout)
      if (!isHikVision) {
        return null
      }

      const responseTime = Date.now() - startTime
      const discoveredCamera: DiscoveredCamera = {
        ipAddress,
        port,
        isReachable: true,
        responseTime,
        lastSeen: new Date()
      }

      // Try to get device information with provided credentials
      if (credentials && credentials.length > 0) {
        for (const cred of credentials) {
          try {
            const testCredentials: HikVisionCredentials = {
              ...cred,
              ipAddress,
              port
            }
            
            const isapiClient = new ISAPIClient(testCredentials, this.logger)
            const deviceInfo = await isapiClient.getDeviceInfo()
            
            if (deviceInfo) {
              discoveredCamera.deviceInfo = deviceInfo
              discoveredCamera.capabilities = deviceInfo
              break
            }
          } catch (error) {
            // Try next credential set
            continue
          }
        }
      }

      return discoveredCamera

    } catch (error) {
      return null
    }
  }

  /**
   * Check if host:port is reachable
   */
  private async checkHostReachability(ipAddress: string, port: number, timeout: number): Promise<boolean> {
    try {
      const response = await axios.get(`http://${ipAddress}:${port}`, {
        timeout,
        validateStatus: () => true, // Accept any status code
        maxRedirects: 0
      })
      return true
    } catch (error: any) {
      // Even connection refused means the host is reachable
      if (error.code === 'ECONNREFUSED' || error.code === 'ECONNRESET') {
        return true
      }
      return false
    }
  }

  /**
   * Try to identify if the device is a HikVision camera
   */
  private async identifyHikVisionCamera(ipAddress: string, port: number, timeout: number): Promise<boolean> {
    try {
      // Try common HikVision endpoints
      const hikVisionEndpoints = [
        '/ISAPI/System/deviceInfo',
        '/ISAPI/System/status',
        '/doc/page/login.asp',
        '/PSIA/System/deviceInfo'
      ]

      for (const endpoint of hikVisionEndpoints) {
        try {
          const response = await axios.get(`http://${ipAddress}:${port}${endpoint}`, {
            timeout: timeout / hikVisionEndpoints.length,
            validateStatus: () => true,
            maxRedirects: 0
          })

          // Check for HikVision-specific headers or content
          const serverHeader = response.headers['server']?.toLowerCase()
          const contentType = response.headers['content-type']?.toLowerCase()
          const responseData = typeof response.data === 'string' ? response.data.toLowerCase() : ''

          if (
            serverHeader?.includes('hikvision') ||
            serverHeader?.includes('webs') ||
            contentType?.includes('xml') ||
            responseData.includes('hikvision') ||
            responseData.includes('isapi') ||
            responseData.includes('psia') ||
            response.status === 401 // Unauthorized often indicates camera
          ) {
            return true
          }
        } catch (error) {
          continue
        }
      }

      return false
    } catch (error) {
      return false
    }
  }

  /**
   * Generate IP addresses from CIDR notation
   */
  private generateIPRange(networkRange: string): string[] {
    const [network, prefixLength] = networkRange.split('/')

    if (!network || !prefixLength) {
      throw new Error('Invalid network range format. Expected format: ***********/24')
    }

    const prefix = parseInt(prefixLength, 10)

    if (prefix < 24 || prefix > 30) {
      throw new Error('Only /24 to /30 networks are supported for scanning')
    }

    const networkParts = network.split('.').map(Number)
    if (networkParts.length !== 4) {
      throw new Error('Invalid IP address format')
    }

    const [a, b, c, d] = networkParts

    if (a === undefined || b === undefined || c === undefined || d === undefined) {
      throw new Error('Invalid IP address format - missing octets')
    }

    const ips: string[] = []

    if (prefix === 24) {
      // /24 network: scan *********** to *************
      for (let i = 1; i <= 254; i++) {
        ips.push(`${a}.${b}.${c}.${i}`)
      }
    } else if (prefix === 25) {
      // /25 network: scan half of the /24 range
      const start = d < 128 ? 1 : 129
      const end = d < 128 ? 126 : 254
      for (let i = start; i <= end; i++) {
        ips.push(`${a}.${b}.${c}.${i}`)
      }
    } else if (prefix === 26) {
      // /26 network: scan quarter of the /24 range
      const start = Math.floor(d / 64) * 64 + 1
      const end = Math.floor(d / 64) * 64 + 62
      for (let i = start; i <= end; i++) {
        ips.push(`${a}.${b}.${c}.${i}`)
      }
    } else if (prefix === 27) {
      // /27 network
      const start = Math.floor(d / 32) * 32 + 1
      const end = Math.floor(d / 32) * 32 + 30
      for (let i = start; i <= end; i++) {
        ips.push(`${a}.${b}.${c}.${i}`)
      }
    } else if (prefix === 28) {
      // /28 network
      const start = Math.floor(d / 16) * 16 + 1
      const end = Math.floor(d / 16) * 16 + 14
      for (let i = start; i <= end; i++) {
        ips.push(`${a}.${b}.${c}.${i}`)
      }
    } else if (prefix === 29) {
      // /29 network
      const start = Math.floor(d / 8) * 8 + 1
      const end = Math.floor(d / 8) * 8 + 6
      for (let i = start; i <= end; i++) {
        ips.push(`${a}.${b}.${c}.${i}`)
      }
    } else if (prefix === 30) {
      // /30 network
      const start = Math.floor(d / 4) * 4 + 1
      const end = Math.floor(d / 4) * 4 + 2
      for (let i = start; i <= end; i++) {
        ips.push(`${a}.${b}.${c}.${i}`)
      }
    }

    return ips
  }

  /**
   * Quick ping test for a single IP
   */
  async pingHost(ipAddress: string, port: number = 80, timeout: number = 3000): Promise<boolean> {
    return this.checkHostReachability(ipAddress, port, timeout)
  }

  /**
   * Test connection to a specific camera
   */
  async testCameraConnection(credentials: HikVisionCredentials): Promise<boolean> {
    try {
      const isapiClient = new ISAPIClient(credentials, this.logger)
      return await isapiClient.testConnection()
    } catch (error) {
      return false
    }
  }

  /**
   * Get scan status
   */
  getScanStatus(): { isScanning: boolean, progress: number, total: number } {
    return {
      isScanning: this.isScanning,
      progress: this.scanProgress,
      total: this.totalHosts
    }
  }

  /**
   * Stop current scan
   */
  stopScan(): void {
    if (this.isScanning) {
      this.isScanning = false
      this.emit('scanStopped')
      this.logger.info('Network scan stopped by user')
    }
  }
}

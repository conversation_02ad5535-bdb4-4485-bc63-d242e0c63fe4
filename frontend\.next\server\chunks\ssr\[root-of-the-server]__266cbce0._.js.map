{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n  }).format(new Date(date))\n}\n\nexport function formatFileSize(bytes: number): string {\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\"]\n  if (bytes === 0) return \"0 Bytes\"\n  const i = Math.floor(Math.log(bytes) / Math.log(1024))\n  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + \" \" + sizes[i]\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\",\n        secondary:\n          \"border-transparent bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300\",\n        destructive:\n          \"border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n        warning:\n          \"border-transparent bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300\",\n        outline: \n          \"text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600\",\n        purple:\n          \"border-transparent bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300\",\n        pink:\n          \"border-transparent bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,QACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-blue-600 text-white shadow-lg hover:bg-blue-700 hover:shadow-xl\",\n        destructive:\n          \"bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl\",\n        outline:\n          \"border border-gray-300 bg-transparent hover:bg-gray-50 hover:border-gray-400 dark:border-gray-600 dark:hover:bg-gray-800 dark:hover:border-gray-500\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700\",\n        ghost: \n          \"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100\",\n        link: \n          \"text-blue-600 underline-offset-4 hover:underline dark:text-blue-400\",\n        success:\n          \"bg-green-600 text-white shadow-lg hover:bg-green-700 hover:shadow-xl\",\n        warning:\n          \"bg-amber-600 text-white shadow-lg hover:bg-amber-700 hover:shadow-xl\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-12 rounded-lg px-8 text-base\",\n        xl: \"h-14 rounded-xl px-10 text-lg\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,iSACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MACE;YACF,SACE;YACF,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: 'admin' | 'operator' | 'viewer'\n}\n\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  requiredRole = 'viewer' \n}) => {\n  const { user, loading, isAuthenticated } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [loading, isAuthenticated, router])\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin text-blue-600\" />\n          <span className=\"text-gray-600 dark:text-gray-400\">Loading...</span>\n        </div>\n      </div>\n    )\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return null\n  }\n\n  // Check role permissions\n  if (user && requiredRole) {\n    const roleHierarchy = {\n      viewer: 1,\n      operator: 2,\n      admin: 3\n    }\n\n    const userRoleLevel = roleHierarchy[user.role]\n    const requiredRoleLevel = roleHierarchy[requiredRole]\n\n    if (userRoleLevel < requiredRoleLevel) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n              Access Denied\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              You don't have permission to access this page.\n            </p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n              Required role: {requiredRole} | Your role: {user.role}\n            </p>\n          </div>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,MAAM,iBAAgD,CAAC,EAC5D,QAAQ,EACR,eAAe,QAAQ,EACxB;IACC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAiB;KAAO;IAErC,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAI3D;IAEA,yCAAyC;IACzC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,yBAAyB;IACzB,IAAI,QAAQ,cAAc;QACxB,MAAM,gBAAgB;YACpB,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QAEA,MAAM,gBAAgB,aAAa,CAAC,KAAK,IAAI,CAAC;QAC9C,MAAM,oBAAoB,aAAa,CAAC,aAAa;QAErD,IAAI,gBAAgB,mBAAmB;YACrC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,8OAAC;4BAAE,WAAU;;gCAA2C;gCACtC;gCAAa;gCAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;QAK/D;IACF;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  Camera,\n  Activity,\n  Shield,\n  Zap,\n  TrendingUp,\n  AlertTriangle,\n  CheckCircle,\n  Clock\n} from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport { ProtectedRoute } from \"@/components/ProtectedRoute\"\n\nconst stats = [\n  {\n    name: \"Active Cameras\",\n    value: \"24\",\n    change: \"+2\",\n    changeType: \"positive\",\n    icon: Camera,\n    color: \"blue\",\n  },\n  {\n    name: \"Events Today\",\n    value: \"1,247\",\n    change: \"+12%\",\n    changeType: \"positive\",\n    icon: Activity,\n    color: \"green\",\n  },\n  {\n    name: \"AI Detections\",\n    value: \"89\",\n    change: \"+5\",\n    changeType: \"positive\",\n    icon: Zap,\n    color: \"purple\",\n  },\n  {\n    name: \"Security Alerts\",\n    value: \"3\",\n    change: \"-2\",\n    changeType: \"negative\",\n    icon: Shield,\n    color: \"red\",\n  },\n]\n\nconst recentEvents = [\n  {\n    id: 1,\n    type: \"Person Detected\",\n    camera: \"Front Entrance\",\n    time: \"2 minutes ago\",\n    status: \"active\",\n    confidence: 94\n  },\n  {\n    id: 2,\n    type: \"Vehicle Recognition\",\n    camera: \"Parking Lot A\",\n    time: \"5 minutes ago\",\n    status: \"resolved\",\n    confidence: 87\n  },\n  {\n    id: 3,\n    type: \"Motion Alert\",\n    camera: \"Warehouse Door\",\n    time: \"12 minutes ago\",\n    status: \"investigating\",\n    confidence: 76\n  },\n  {\n    id: 4,\n    type: \"Face Recognition\",\n    camera: \"Main Lobby\",\n    time: \"18 minutes ago\",\n    status: \"resolved\",\n    confidence: 92\n  },\n  {\n    id: 5,\n    type: \"Intrusion Alert\",\n    camera: \"Perimeter Fence\",\n    time: \"25 minutes ago\",\n    status: \"active\",\n    confidence: 89\n  }\n]\n\nfunction Dashboard() {\n  return (\n    <div className=\"flex-1 space-y-6 p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            Dashboard\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Welcome back! Here's what's happening with your security system.\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <Button variant=\"outline\">\n            <TrendingUp className=\"mr-2 h-4 w-4\" />\n            View Reports\n          </Button>\n          <Button>\n            <Shield className=\"mr-2 h-4 w-4\" />\n            Arm All Cameras\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n        {stats.map((stat, index) => (\n          <motion.div\n            key={stat.name}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                  {stat.name}\n                </CardTitle>\n                <stat.icon className={`h-4 w-4 text-${stat.color}-600`} />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {stat.value}\n                </div>\n                <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                  <span\n                    className={`${\n                      stat.changeType === \"positive\"\n                        ? \"text-green-600\"\n                        : \"text-red-600\"\n                    }`}\n                  >\n                    {stat.change}\n                  </span>{\" \"}\n                  from last hour\n                </p>\n              </CardContent>\n            </Card>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Recent Events and Analytics */}\n      <div className=\"grid gap-6 lg:grid-cols-3\">\n        {/* Recent Events */}\n        <div className=\"lg:col-span-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Activity className=\"h-5 w-5\" />\n                <span>Recent Events</span>\n              </CardTitle>\n              <CardDescription>\n                Latest security events from your cameras\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {recentEvents.map((event) => (\n                <motion.div\n                  key={event.id}\n                  className=\"flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                  whileHover={{ scale: 1.02 }}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex-shrink-0\">\n                      {event.status === \"active\" && (\n                        <AlertTriangle className=\"h-5 w-5 text-red-500\" />\n                      )}\n                      {event.status === \"resolved\" && (\n                        <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                      )}\n                      {event.status === \"investigating\" && (\n                        <Clock className=\"h-5 w-5 text-amber-500\" />\n                      )}\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {event.type}\n                      </p>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        {event.camera} • {event.time}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Badge\n                      variant={\n                        event.status === \"active\"\n                          ? \"destructive\"\n                          : event.status === \"resolved\"\n                          ? \"success\"\n                          : \"warning\"\n                      }\n                      size=\"sm\"\n                    >\n                      {event.confidence}%\n                    </Badge>\n                  </div>\n                </motion.div>\n              ))}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* System Status */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Shield className=\"h-5 w-5\" />\n              <span>System Status</span>\n            </CardTitle>\n            <CardDescription>\n              Current status of all system components\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Camera Network\n                </span>\n                <Badge variant=\"success\">Online</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  AI Processing\n                </span>\n                <Badge variant=\"success\">Active</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Database\n                </span>\n                <Badge variant=\"success\">Connected</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Storage\n                </span>\n                <Badge variant=\"warning\">78% Full</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Notifications\n                </span>\n                <Badge variant=\"success\">Enabled</Badge>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n\nexport default function DashboardPage() {\n  return (\n    <ProtectedRoute>\n      <Dashboard />\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AAjBA;;;;;;;;AAmBA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;CACD;AAED,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAIlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,8OAAC,kIAAA,CAAA,SAAM;;kDACL,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,KAAK,IAAI;;;;;;sDAEZ,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;;8CAExD,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDACC,WAAW,GACT,KAAK,UAAU,KAAK,aAChB,mBACA,gBACJ;8DAED,KAAK,MAAM;;;;;;gDACN;gDAAI;;;;;;;;;;;;;;;;;;;uBAzBb,KAAK,IAAI;;;;;;;;;;0BAmCpB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,MAAM,MAAM,KAAK,0BAChB,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAE1B,MAAM,MAAM,KAAK,4BAChB,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAExB,MAAM,MAAM,KAAK,iCAChB,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;sEAGrB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,MAAM,IAAI;;;;;;8EAEb,8OAAC;oEAAE,WAAU;;wEACV,MAAM,MAAM;wEAAC;wEAAI,MAAM,IAAI;;;;;;;;;;;;;;;;;;;8DAIlC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDACJ,SACE,MAAM,MAAM,KAAK,WACb,gBACA,MAAM,MAAM,KAAK,aACjB,YACA;wDAEN,MAAK;;4DAEJ,MAAM,UAAU;4DAAC;;;;;;;;;;;;;2CApCjB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;kCA8CvB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;AAEe,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}
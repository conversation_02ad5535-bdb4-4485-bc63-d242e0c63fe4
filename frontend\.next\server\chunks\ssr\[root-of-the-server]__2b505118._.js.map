{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/api.ts"], "sourcesContent": ["// API Service Layer for Frontend-Backend Integration\n\nimport {\n  LoginRequest,\n  RegisterRequest,\n  AuthResponse,\n  RefreshTokenRequest,\n  Camera,\n  CreateCameraRequest,\n  UpdateCameraRequest,\n  CameraStats,\n  Event,\n  CreateEventRequest,\n  EventFilters,\n  AcknowledgeEventRequest,\n  ResolveEventRequest,\n  EventStats,\n  ApiResponse,\n  PaginatedResponse,\n  ApiError,\n  HealthStatus,\n  SystemStatus\n} from '@/types/api'\n\nclass ApiService {\n  private baseUrl: string\n  private accessToken: string | null = null\n  private refreshToken: string | null = null\n\n  constructor() {\n    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'\n    \n    // Load tokens from localStorage on initialization\n    if (typeof window !== 'undefined') {\n      this.accessToken = localStorage.getItem('accessToken')\n      this.refreshToken = localStorage.getItem('refreshToken')\n    }\n  }\n\n  // ===== PRIVATE HELPER METHODS =====\n  private async makeRequest<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseUrl}${endpoint}`\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    }\n\n    // Add authorization header if token exists\n    if (this.accessToken) {\n      config.headers = {\n        ...config.headers,\n        Authorization: `Bearer ${this.accessToken}`,\n      }\n    }\n\n    try {\n      const response = await fetch(url, config)\n      \n      // Handle 401 - try to refresh token\n      if (response.status === 401 && this.refreshToken) {\n        const refreshed = await this.refreshAccessToken()\n        if (refreshed) {\n          // Retry the original request with new token\n          config.headers = {\n            ...config.headers,\n            Authorization: `Bearer ${this.accessToken}`,\n          }\n          const retryResponse = await fetch(url, config)\n          return this.handleResponse<T>(retryResponse)\n        }\n      }\n\n      return this.handleResponse<T>(response)\n    } catch (error) {\n      console.error('API request failed:', error)\n      throw new Error('Network error occurred')\n    }\n  }\n\n  private async handleResponse<T>(response: Response): Promise<T> {\n    const contentType = response.headers.get('content-type')\n    const isJson = contentType?.includes('application/json')\n    \n    const data = isJson ? await response.json() : await response.text()\n\n    if (!response.ok) {\n      const error: ApiError = {\n        error: data.error || `HTTP ${response.status}`,\n        details: data.details,\n        status: response.status,\n      }\n      throw error\n    }\n\n    return data\n  }\n\n  private setTokens(accessToken: string, refreshToken: string) {\n    this.accessToken = accessToken\n    this.refreshToken = refreshToken\n    \n    if (typeof window !== 'undefined') {\n      localStorage.setItem('accessToken', accessToken)\n      localStorage.setItem('refreshToken', refreshToken)\n    }\n  }\n\n  private clearTokens() {\n    this.accessToken = null\n    this.refreshToken = null\n    \n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('accessToken')\n      localStorage.removeItem('refreshToken')\n    }\n  }\n\n  // ===== AUTHENTICATION METHODS =====\n  async login(credentials: LoginRequest): Promise<AuthResponse> {\n    const response = await this.makeRequest<any>('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    })\n\n    // Backend returns token and refreshToken directly, not nested under tokens\n    this.setTokens(response.token, response.refreshToken)\n\n    // Transform response to match expected AuthResponse format\n    return {\n      message: response.message,\n      user: response.user,\n      tokens: {\n        accessToken: response.token,\n        refreshToken: response.refreshToken\n      }\n    }\n  }\n\n  async register(userData: RegisterRequest): Promise<AuthResponse> {\n    const response = await this.makeRequest<any>('/api/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    })\n\n    // Backend returns token and refreshToken directly, not nested under tokens\n    this.setTokens(response.token, response.refreshToken)\n\n    // Transform response to match expected AuthResponse format\n    return {\n      message: response.message,\n      user: response.user,\n      tokens: {\n        accessToken: response.token,\n        refreshToken: response.refreshToken\n      }\n    }\n  }\n\n  async refreshAccessToken(): Promise<boolean> {\n    if (!this.refreshToken) return false\n\n    try {\n      const response = await this.makeRequest<any>('/api/auth/refresh', {\n        method: 'POST',\n        body: JSON.stringify({ refreshToken: this.refreshToken }),\n      })\n\n      // Backend returns token and refreshToken directly, not nested under tokens\n      this.setTokens(response.token, response.refreshToken)\n      return true\n    } catch (error) {\n      this.clearTokens()\n      return false\n    }\n  }\n\n  async logout(): Promise<void> {\n    this.clearTokens()\n  }\n\n  async getProfile(): Promise<AuthResponse['user']> {\n    const response = await this.makeRequest<{ user: AuthResponse['user'] }>('/api/auth/me')\n    return response.user\n  }\n\n  // ===== CAMERA METHODS =====\n  async getCameras(filters?: {\n    status?: string\n    location?: string\n    limit?: number\n    page?: number\n  }): Promise<{ cameras: Camera[]; pagination?: any }> {\n    const params = new URLSearchParams()\n    if (filters?.status) params.append('status', filters.status)\n    if (filters?.location) params.append('location', filters.location)\n    if (filters?.limit) params.append('limit', filters.limit.toString())\n    if (filters?.page) params.append('page', filters.page.toString())\n\n    const queryString = params.toString()\n    const endpoint = `/api/cameras${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<{ cameras: Camera[]; pagination?: any }>(endpoint)\n  }\n\n  async getCamera(id: string): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`)\n    return response.camera\n  }\n\n  async createCamera(cameraData: CreateCameraRequest): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>('/api/cameras', {\n      method: 'POST',\n      body: JSON.stringify(cameraData),\n    })\n    return response.camera\n  }\n\n  async updateCamera(id: string, updates: UpdateCameraRequest): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n    return response.camera\n  }\n\n  async deleteCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async armCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}/arm`, {\n      method: 'POST',\n    })\n  }\n\n  async disarmCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}/disarm`, {\n      method: 'POST',\n    })\n  }\n\n  async getCameraStatus(id: string): Promise<any> {\n    return this.makeRequest(`/api/cameras/${id}/status`)\n  }\n\n  async getCameraSnapshot(id: string): Promise<Blob> {\n    const response = await fetch(`${this.baseURL}/api/cameras/${id}/snapshot`, {\n      headers: {\n        'Authorization': `Bearer ${this.accessToken}`,\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error('Failed to get camera snapshot')\n    }\n\n    return response.blob()\n  }\n\n  async getCameraStreamUrl(id: string): Promise<{ streamUrl: string }> {\n    return this.makeRequest(`/api/cameras/${id}/stream`)\n  }\n\n  // Discovery API methods\n  async startNetworkScan(scanOptions: {\n    networkRange: string\n    portRange: number[]\n    timeout?: number\n    maxConcurrent?: number\n    credentials?: Array<{ username: string; password: string }>\n  }): Promise<{ message: string; scanId: string }> {\n    return this.makeRequest('/api/discovery/scan', {\n      method: 'POST',\n      body: JSON.stringify(scanOptions),\n    })\n  }\n\n  async getScanStatus(): Promise<{ isScanning: boolean; progress: number; total: number }> {\n    return this.makeRequest('/api/discovery/status')\n  }\n\n  async stopNetworkScan(): Promise<{ message: string }> {\n    return this.makeRequest('/api/discovery/stop', {\n      method: 'POST',\n    })\n  }\n\n  async pingHost(ipAddress: string, port?: number, timeout?: number): Promise<{\n    ipAddress: string\n    port: number\n    isReachable: boolean\n    responseTime: number\n  }> {\n    return this.makeRequest('/api/discovery/ping', {\n      method: 'POST',\n      body: JSON.stringify({ ipAddress, port, timeout }),\n    })\n  }\n\n  async testCameraConnection(credentials: {\n    ipAddress: string\n    port: number\n    username: string\n    password: string\n    useHttps?: boolean\n  }): Promise<{\n    ipAddress: string\n    port: number\n    isConnected: boolean\n    responseTime: number\n  }> {\n    return this.makeRequest('/api/discovery/test-camera', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    })\n  }\n\n  async getCameraStats(): Promise<CameraStats> {\n    return this.makeRequest<CameraStats>('/api/cameras/stats')\n  }\n\n  // ===== EVENT METHODS =====\n  async getEvents(filters?: EventFilters): Promise<{ events: Event[]; pagination?: any }> {\n    const params = new URLSearchParams()\n    if (filters?.type) params.append('type', filters.type)\n    if (filters?.severity) params.append('severity', filters.severity)\n    if (filters?.status) params.append('status', filters.status)\n    if (filters?.cameraId) params.append('cameraId', filters.cameraId)\n    if (filters?.startDate) params.append('startDate', filters.startDate)\n    if (filters?.endDate) params.append('endDate', filters.endDate)\n    if (filters?.limit) params.append('limit', filters.limit.toString())\n    if (filters?.page) params.append('page', filters.page.toString())\n\n    const queryString = params.toString()\n    const endpoint = `/api/events${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<{ events: Event[]; pagination?: any }>(endpoint)\n  }\n\n  async getEvent(id: string): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}`)\n    return response.event\n  }\n\n  async createEvent(eventData: CreateEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>('/api/events', {\n      method: 'POST',\n      body: JSON.stringify(eventData),\n    })\n    return response.event\n  }\n\n  async acknowledgeEvent(id: string, data: AcknowledgeEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/acknowledge`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n    return response.event\n  }\n\n  async resolveEvent(id: string, data: ResolveEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/resolve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n    return response.event\n  }\n\n  async getEventStats(): Promise<EventStats> {\n    return this.makeRequest<EventStats>('/api/events/stats')\n  }\n\n  // ===== SYSTEM METHODS =====\n  async getHealth(): Promise<HealthStatus> {\n    return this.makeRequest<HealthStatus>('/health')\n  }\n\n  async getSystemStatus(): Promise<SystemStatus> {\n    return this.makeRequest<SystemStatus>('/api/status')\n  }\n\n  // ===== UTILITY METHODS =====\n  isAuthenticated(): boolean {\n    return !!this.accessToken\n  }\n\n  getAccessToken(): string | null {\n    return this.accessToken\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService()\nexport default apiService\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAwBrD,MAAM;IACI,QAAe;IACf,cAA6B,KAAI;IACjC,eAA8B,KAAI;IAE1C,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,6DAAmC;QAElD,kDAAkD;QAClD,uCAAmC;;QAGnC;IACF;IAEA,qCAAqC;IACrC,MAAc,YACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,2CAA2C;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,OAAO,GAAG;gBACf,GAAG,OAAO,OAAO;gBACjB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;YAC7C;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,oCAAoC;YACpC,IAAI,SAAS,MAAM,KAAK,OAAO,IAAI,CAAC,YAAY,EAAE;gBAChD,MAAM,YAAY,MAAM,IAAI,CAAC,kBAAkB;gBAC/C,IAAI,WAAW;oBACb,4CAA4C;oBAC5C,OAAO,OAAO,GAAG;wBACf,GAAG,OAAO,OAAO;wBACjB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;oBAC7C;oBACA,MAAM,gBAAgB,MAAM,MAAM,KAAK;oBACvC,OAAO,IAAI,CAAC,cAAc,CAAI;gBAChC;YACF;YAEA,OAAO,IAAI,CAAC,cAAc,CAAI;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAc,eAAkB,QAAkB,EAAc;QAC9D,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,aAAa,SAAS;QAErC,MAAM,OAAO,SAAS,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI;QAEjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAkB;gBACtB,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;gBAC9C,SAAS,KAAK,OAAO;gBACrB,QAAQ,SAAS,MAAM;YACzB;YACA,MAAM;QACR;QAEA,OAAO;IACT;IAEQ,UAAU,WAAmB,EAAE,YAAoB,EAAE;QAC3D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QAEpB,uCAAmC;;QAGnC;IACF;IAEQ,cAAc;QACpB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QAEpB,uCAAmC;;QAGnC;IACF;IAEA,qCAAqC;IACrC,MAAM,MAAM,WAAyB,EAAyB;QAC5D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAM,mBAAmB;YAC9D,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,2EAA2E;QAC3E,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,EAAE,SAAS,YAAY;QAEpD,2DAA2D;QAC3D,OAAO;YACL,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI;YACnB,QAAQ;gBACN,aAAa,SAAS,KAAK;gBAC3B,cAAc,SAAS,YAAY;YACrC;QACF;IACF;IAEA,MAAM,SAAS,QAAyB,EAAyB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAM,sBAAsB;YACjE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,2EAA2E;QAC3E,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,EAAE,SAAS,YAAY;QAEpD,2DAA2D;QAC3D,OAAO;YACL,SAAS,SAAS,OAAO;YACzB,MAAM,SAAS,IAAI;YACnB,QAAQ;gBACN,aAAa,SAAS,KAAK;gBAC3B,cAAc,SAAS,YAAY;YACrC;QACF;IACF;IAEA,MAAM,qBAAuC;QAC3C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAM,qBAAqB;gBAChE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE,cAAc,IAAI,CAAC,YAAY;gBAAC;YACzD;YAEA,2EAA2E;YAC3E,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,EAAE,SAAS,YAAY;YACpD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW;YAChB,OAAO;QACT;IACF;IAEA,MAAM,SAAwB;QAC5B,IAAI,CAAC,WAAW;IAClB;IAEA,MAAM,aAA4C;QAChD,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAiC;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,WAAW,OAKhB,EAAoD;QACnD,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAE9D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,OAAO,IAAI,CAAC,WAAW,CAA0C;IACnE;IAEA,MAAM,UAAU,EAAU,EAAmB;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,CAAC,aAAa,EAAE,IAAI;QAChF,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,UAA+B,EAAmB;QACnE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,gBAAgB;YAC1E,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,EAAU,EAAE,OAA4B,EAAmB;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,CAAC,aAAa,EAAE,IAAI,EAAE;YAChF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;YAC3C,QAAQ;QACV;IACF;IAEA,MAAM,UAAU,EAAU,EAAiB;QACzC,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,EAAE;YAC/C,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YAClD,QAAQ;QACV;IACF;IAEA,MAAM,gBAAgB,EAAU,EAAgB;QAC9C,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC;IACrD;IAEA,MAAM,kBAAkB,EAAU,EAAiB;QACjD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,SAAS,CAAC,EAAE;YACzE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;YAC/C;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAmB,EAAU,EAAkC;QACnE,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC;IACrD;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,WAMtB,EAAgD;QAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,uBAAuB;YAC7C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,gBAAmF;QACvF,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B;IAEA,MAAM,kBAAgD;QACpD,OAAO,IAAI,CAAC,WAAW,CAAC,uBAAuB;YAC7C,QAAQ;QACV;IACF;IAEA,MAAM,SAAS,SAAiB,EAAE,IAAa,EAAE,OAAgB,EAK9D;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,uBAAuB;YAC7C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAW;gBAAM;YAAQ;QAClD;IACF;IAEA,MAAM,qBAAqB,WAM1B,EAKE;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,8BAA8B;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,iBAAuC;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAc;IACvC;IAEA,4BAA4B;IAC5B,MAAM,UAAU,OAAsB,EAAkD;QACtF,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;QACrD,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;QACpE,IAAI,SAAS,SAAS,OAAO,MAAM,CAAC,WAAW,QAAQ,OAAO;QAC9D,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAE9D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAErE,OAAO,IAAI,CAAC,WAAW,CAAwC;IACjE;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,IAAI;QAC7E,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,YAAY,SAA6B,EAAkB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,eAAe;YACvE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,iBAAiB,EAAU,EAAE,IAA6B,EAAkB;QAChF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,GAAG,YAAY,CAAC,EAAE;YACzF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,aAAa,EAAU,EAAE,IAAyB,EAAkB;QACxE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,EAAE;YACrF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,gBAAqC;QACzC,OAAO,IAAI,CAAC,WAAW,CAAa;IACtC;IAEA,6BAA6B;IAC7B,MAAM,YAAmC;QACvC,OAAO,IAAI,CAAC,WAAW,CAAe;IACxC;IAEA,MAAM,kBAAyC;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAe;IACxC;IAEA,8BAA8B;IAC9B,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW;IAC3B;IAEA,iBAAgC;QAC9B,OAAO,IAAI,CAAC,WAAW;IACzB;AACF;AAGO,MAAM,aAAa,IAAI;uCACf", "debugId": null}}, {"offset": {"line": 317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { apiService } from '@/lib/api'\n\ninterface User {\n  id: string\n  username: string\n  email: string\n  firstName: string\n  lastName: string\n  role: 'admin' | 'operator' | 'viewer'\n  isActive: boolean\n  lastLogin?: string\n  preferences: {\n    theme: 'light' | 'dark' | 'system'\n    language: string\n    timezone: string\n    notifications: {\n      email: boolean\n      push: boolean\n      sms: boolean\n    }\n  }\n  createdAt: string\n  updatedAt: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  isAuthenticated: boolean\n  login: (email: string, password: string) => Promise<void>\n  register: (userData: {\n    firstName: string\n    lastName: string\n    username: string\n    email: string\n    password: string\n  }) => Promise<void>\n  logout: () => Promise<void>\n  refreshUser: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus()\n  }, [])\n\n  const checkAuthStatus = async () => {\n    try {\n      if (apiService.isAuthenticated()) {\n        const userProfile = await apiService.getProfile()\n        setUser(userProfile)\n      } else {\n        setUser(null)\n      }\n    } catch (error) {\n      console.error('Failed to check auth status:', error)\n      // Clear invalid tokens\n      await apiService.logout()\n      setUser(null)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await apiService.login({ email, password })\n      setUser(response.user)\n    } catch (error) {\n      throw error\n    }\n  }\n\n  const register = async (userData: {\n    firstName: string\n    lastName: string\n    username: string\n    email: string\n    password: string\n  }) => {\n    try {\n      const response = await apiService.register(userData)\n      setUser(response.user)\n    } catch (error) {\n      throw error\n    }\n  }\n\n  const logout = async () => {\n    try {\n      await apiService.logout()\n      setUser(null)\n    } catch (error) {\n      console.error('Logout error:', error)\n      // Clear user state even if logout request fails\n      setUser(null)\n    }\n  }\n\n  const refreshUser = async () => {\n    try {\n      if (apiService.isAuthenticated()) {\n        const userProfile = await apiService.getProfile()\n        setUser(userProfile)\n      }\n    } catch (error) {\n      console.error('Failed to refresh user:', error)\n      // If refresh fails, user might need to login again\n      setUser(null)\n    }\n  }\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    isAuthenticated: !!user,\n    login,\n    register,\n    logout,\n    refreshUser\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AA4CA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,iHAAA,CAAA,aAAU,CAAC,eAAe,IAAI;gBAChC,MAAM,cAAc,MAAM,iHAAA,CAAA,aAAU,CAAC,UAAU;gBAC/C,QAAQ;YACV,OAAO;gBACL,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,uBAAuB;YACvB,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM;YACvB,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAC1D,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO;QAOtB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,QAAQ,CAAC;YAC3C,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM;YACvB,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gDAAgD;YAChD,QAAQ;QACV;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,IAAI,iHAAA,CAAA,aAAU,CAAC,eAAe,IAAI;gBAChC,MAAM,cAAc,MAAM,iHAAA,CAAA,aAAU,CAAC,UAAU;gBAC/C,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,mDAAmD;YACnD,QAAQ;QACV;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n  }).format(new Date(date))\n}\n\nexport function formatFileSize(bytes: number): string {\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\"]\n  if (bytes === 0) return \"0 Bytes\"\n  const i = Math.floor(Math.log(bytes) / Math.log(1024))\n  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + \" \" + sizes[i]\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\",\n        secondary:\n          \"border-transparent bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300\",\n        destructive:\n          \"border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n        warning:\n          \"border-transparent bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300\",\n        outline: \n          \"text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600\",\n        purple:\n          \"border-transparent bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300\",\n        pink:\n          \"border-transparent bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,QACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-blue-600 text-white shadow-lg hover:bg-blue-700 hover:shadow-xl\",\n        destructive:\n          \"bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl\",\n        outline:\n          \"border border-gray-300 bg-transparent hover:bg-gray-50 hover:border-gray-400 dark:border-gray-600 dark:hover:bg-gray-800 dark:hover:border-gray-500\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700\",\n        ghost: \n          \"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100\",\n        link: \n          \"text-blue-600 underline-offset-4 hover:underline dark:text-blue-400\",\n        success:\n          \"bg-green-600 text-white shadow-lg hover:bg-green-700 hover:shadow-xl\",\n        warning:\n          \"bg-amber-600 text-white shadow-lg hover:bg-amber-700 hover:shadow-xl\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-12 rounded-lg px-8 text-base\",\n        xl: \"h-14 rounded-xl px-10 text-lg\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,iSACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MACE;YACF,SACE;YACF,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { motion } from \"framer-motion\"\nimport {\n  LayoutDashboard,\n  Camera,\n  Activity,\n  Settings,\n  Users,\n  Bell,\n  BarChart3,\n  Shield,\n  Zap,\n  LogOut,\n  User,\n  Search\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport { useAuth } from \"@/contexts/AuthContext\"\n\nconst navigation = [\n  {\n    name: \"Dashboard\",\n    href: \"/\",\n    icon: LayoutDashboard,\n    badge: null,\n  },\n  {\n    name: \"Live Events\",\n    href: \"/events\",\n    icon: Activity,\n    badge: \"12\",\n  },\n  {\n    name: \"Cameras\",\n    href: \"/cameras\",\n    icon: Camera,\n    badge: null,\n  },\n  {\n    name: \"Discovery\",\n    href: \"/discovery\",\n    icon: Search,\n    badge: null,\n  },\n  {\n    name: \"Analytics\",\n    href: \"/analytics\",\n    icon: BarChart3,\n    badge: null,\n  },\n  {\n    name: \"AI Models\",\n    href: \"/ai-models\",\n    icon: Zap,\n    badge: \"New\",\n  },\n  {\n    name: \"Notifications\",\n    href: \"/notifications\",\n    icon: Bell,\n    badge: \"3\",\n  },\n  {\n    name: \"Users\",\n    href: \"/users\",\n    icon: Users,\n    badge: null,\n  },\n  {\n    name: \"Security\",\n    href: \"/security\",\n    icon: Shield,\n    badge: null,\n  },\n  {\n    name: \"Settings\",\n    href: \"/settings\",\n    icon: Settings,\n    badge: null,\n  },\n]\n\ninterface SidebarProps {\n  className?: string\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname()\n  const { user, logout } = useAuth()\n\n  const handleLogout = async () => {\n    try {\n      await logout()\n      window.location.href = '/login'\n    } catch (error) {\n      console.error('Logout failed:', error)\n    }\n  }\n\n  return (\n    <div className={cn(\"flex h-full w-64 flex-col bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\", className)}>\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600\">\n            <Camera className=\"h-5 w-5 text-white\" />\n          </div>\n          <div>\n            <h1 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n              HikVision AI\n            </h1>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Monitoring System\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link key={item.name} href={item.href}>\n              <motion.div\n                className={cn(\n                  \"group flex items-center justify-between rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200\",\n                  isActive\n                    ? \"bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400\"\n                    : \"text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n                )}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <item.icon\n                    className={cn(\n                      \"h-5 w-5 transition-colors\",\n                      isActive\n                        ? \"text-blue-600 dark:text-blue-400\"\n                        : \"text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300\"\n                    )}\n                  />\n                  <span>{item.name}</span>\n                </div>\n                {item.badge && (\n                  <Badge \n                    variant={isActive ? \"default\" : \"secondary\"} \n                    size=\"sm\"\n                    className=\"ml-auto\"\n                  >\n                    {item.badge}\n                  </Badge>\n                )}\n              </motion.div>\n            </Link>\n          )\n        })}\n      </nav>\n\n      {/* Status Indicator */}\n      <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n        <div className=\"flex items-center space-x-3 rounded-lg bg-green-50 dark:bg-green-900/20 p-3\">\n          <div className=\"flex h-2 w-2 items-center justify-center\">\n            <div className=\"h-2 w-2 rounded-full bg-green-500 animate-pulse\" />\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-sm font-medium text-green-800 dark:text-green-400\">\n              System Online\n            </p>\n            <p className=\"text-xs text-green-600 dark:text-green-500\">\n              All services running\n            </p>\n          </div>\n        </div>\n\n        {/* User Section */}\n        {user && (\n          <div className=\"border-t border-gray-200 dark:border-gray-700 p-4\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center\">\n                  <User className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n                </div>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                  {user.firstName} {user.lastName}\n                </p>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                  {user.email}\n                </p>\n                <Badge variant=\"outline\" className=\"text-xs mt-1\">\n                  {user.role}\n                </Badge>\n              </div>\n            </div>\n            <Button\n              onClick={handleLogout}\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"w-full flex items-center justify-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400\"\n            >\n              <LogOut className=\"h-4 w-4\" />\n              <span>Sign Out</span>\n            </Button>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;AACA;AACA;AAvBA;;;;;;;;;;AAyBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,4NAAA,CAAA,kBAAe;QACrB,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE/B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qGAAqG;;0BAEtH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAGhE,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;wBAAiB,MAAM,KAAK,IAAI;kCACnC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kHACA,WACI,oEACA;4BAEN,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,KAAK,IAAI;4CACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6BACA,WACI,qCACA;;;;;;sDAGR,8OAAC;sDAAM,KAAK,IAAI;;;;;;;;;;;;gCAEjB,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;oCACJ,SAAS,WAAW,YAAY;oCAChC,MAAK;oCACL,WAAU;8CAET,KAAK,KAAK;;;;;;;;;;;;uBA5BR,KAAK,IAAI;;;;;gBAkCxB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAyD;;;;;;kDAGtE,8OAAC;wCAAE,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;oBAO7D,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;oDACV,KAAK,SAAS;oDAAC;oDAAE,KAAK,QAAQ;;;;;;;0DAEjC,8OAAC;gDAAE,WAAU;0DACV,KAAK,KAAK;;;;;;0DAEb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,KAAK,IAAI;;;;;;;;;;;;;;;;;;0CAIhB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/layout/LayoutWrapper.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from 'react'\nimport { usePathname } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Sidebar } from './sidebar'\nimport { Loader2 } from 'lucide-react'\n\ninterface LayoutWrapperProps {\n  children: React.ReactNode\n}\n\nexport const LayoutWrapper: React.FC<LayoutWrapperProps> = ({ children }) => {\n  const pathname = usePathname()\n  const { isAuthenticated, loading } = useAuth()\n\n  // Routes that don't need authentication (public routes)\n  const publicRoutes = ['/login', '/register']\n  const isPublicRoute = publicRoutes.includes(pathname)\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin text-blue-600\" />\n          <span className=\"text-gray-600 dark:text-gray-400\">Loading...</span>\n        </div>\n      </div>\n    )\n  }\n\n  // For public routes (login/register), show full-width layout\n  if (isPublicRoute) {\n    return <>{children}</>\n  }\n\n  // For authenticated routes, show sidebar + main content\n  if (isAuthenticated) {\n    return (\n      <div className=\"flex h-full\">\n        <Sidebar />\n        <main className=\"flex-1 overflow-auto\">\n          {children}\n        </main>\n      </div>\n    )\n  }\n\n  // For protected routes when not authenticated, the ProtectedRoute component will handle redirect\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAYO,MAAM,gBAA8C,CAAC,EAAE,QAAQ,EAAE;IACtE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3C,wDAAwD;IACxD,MAAM,eAAe;QAAC;QAAU;KAAY;IAC5C,MAAM,gBAAgB,aAAa,QAAQ,CAAC;IAE5C,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAI3D;IAEA,6DAA6D;IAC7D,IAAI,eAAe;QACjB,qBAAO;sBAAG;;IACZ;IAEA,wDAAwD;IACxD,IAAI,iBAAiB;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,uIAAA,CAAA,UAAO;;;;;8BACR,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;IAIT;IAEA,iGAAiG;IACjG,qBAAO;kBAAG;;AACZ", "debugId": null}}]}
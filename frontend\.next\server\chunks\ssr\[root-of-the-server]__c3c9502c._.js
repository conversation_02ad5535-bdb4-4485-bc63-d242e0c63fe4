module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API Service Layer for Frontend-Backend Integration
__turbopack_context__.s({
    "apiService": (()=>apiService),
    "default": (()=>__TURBOPACK__default__export__)
});
class ApiService {
    baseUrl;
    accessToken = null;
    refreshToken = null;
    constructor(){
        this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
        // Load tokens from localStorage on initialization
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    // ===== PRIVATE HELPER METHODS =====
    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };
        // Add authorization header if token exists
        if (this.accessToken) {
            config.headers = {
                ...config.headers,
                Authorization: `Bearer ${this.accessToken}`
            };
        }
        try {
            const response = await fetch(url, config);
            // Handle 401 - try to refresh token
            if (response.status === 401 && this.refreshToken) {
                const refreshed = await this.refreshAccessToken();
                if (refreshed) {
                    // Retry the original request with new token
                    config.headers = {
                        ...config.headers,
                        Authorization: `Bearer ${this.accessToken}`
                    };
                    const retryResponse = await fetch(url, config);
                    return this.handleResponse(retryResponse);
                }
            }
            return this.handleResponse(response);
        } catch (error) {
            console.error('API request failed:', error);
            throw new Error('Network error occurred');
        }
    }
    async handleResponse(response) {
        const contentType = response.headers.get('content-type');
        const isJson = contentType?.includes('application/json');
        const data = isJson ? await response.json() : await response.text();
        if (!response.ok) {
            const error = {
                error: data.error || `HTTP ${response.status}`,
                details: data.details,
                status: response.status
            };
            throw error;
        }
        return data;
    }
    setTokens(accessToken, refreshToken) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    clearTokens() {
        this.accessToken = null;
        this.refreshToken = null;
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }
    // ===== AUTHENTICATION METHODS =====
    async login(credentials) {
        const response = await this.makeRequest('/api/auth/login', {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
        this.setTokens(response.tokens.accessToken, response.tokens.refreshToken);
        return response;
    }
    async register(userData) {
        const response = await this.makeRequest('/api/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });
        this.setTokens(response.tokens.accessToken, response.tokens.refreshToken);
        return response;
    }
    async refreshAccessToken() {
        if (!this.refreshToken) return false;
        try {
            const response = await this.makeRequest('/api/auth/refresh', {
                method: 'POST',
                body: JSON.stringify({
                    refreshToken: this.refreshToken
                })
            });
            this.setTokens(response.tokens.accessToken, response.tokens.refreshToken);
            return true;
        } catch (error) {
            this.clearTokens();
            return false;
        }
    }
    async logout() {
        this.clearTokens();
    }
    async getProfile() {
        const response = await this.makeRequest('/api/auth/me');
        return response.user;
    }
    // ===== CAMERA METHODS =====
    async getCameras(filters) {
        const params = new URLSearchParams();
        if (filters?.status) params.append('status', filters.status);
        if (filters?.location) params.append('location', filters.location);
        if (filters?.limit) params.append('limit', filters.limit.toString());
        if (filters?.page) params.append('page', filters.page.toString());
        const queryString = params.toString();
        const endpoint = `/api/cameras${queryString ? `?${queryString}` : ''}`;
        return this.makeRequest(endpoint);
    }
    async getCamera(id) {
        const response = await this.makeRequest(`/api/cameras/${id}`);
        return response.camera;
    }
    async createCamera(cameraData) {
        const response = await this.makeRequest('/api/cameras', {
            method: 'POST',
            body: JSON.stringify(cameraData)
        });
        return response.camera;
    }
    async updateCamera(id, updates) {
        const response = await this.makeRequest(`/api/cameras/${id}`, {
            method: 'PATCH',
            body: JSON.stringify(updates)
        });
        return response.camera;
    }
    async deleteCamera(id) {
        await this.makeRequest(`/api/cameras/${id}`, {
            method: 'DELETE'
        });
    }
    async armCamera(id) {
        await this.makeRequest(`/api/cameras/${id}/arm`, {
            method: 'POST'
        });
    }
    async disarmCamera(id) {
        await this.makeRequest(`/api/cameras/${id}/disarm`, {
            method: 'POST'
        });
    }
    async getCameraStats() {
        return this.makeRequest('/api/cameras/stats');
    }
    // ===== EVENT METHODS =====
    async getEvents(filters) {
        const params = new URLSearchParams();
        if (filters?.type) params.append('type', filters.type);
        if (filters?.severity) params.append('severity', filters.severity);
        if (filters?.status) params.append('status', filters.status);
        if (filters?.cameraId) params.append('cameraId', filters.cameraId);
        if (filters?.startDate) params.append('startDate', filters.startDate);
        if (filters?.endDate) params.append('endDate', filters.endDate);
        if (filters?.limit) params.append('limit', filters.limit.toString());
        if (filters?.page) params.append('page', filters.page.toString());
        const queryString = params.toString();
        const endpoint = `/api/events${queryString ? `?${queryString}` : ''}`;
        return this.makeRequest(endpoint);
    }
    async getEvent(id) {
        const response = await this.makeRequest(`/api/events/${id}`);
        return response.event;
    }
    async createEvent(eventData) {
        const response = await this.makeRequest('/api/events', {
            method: 'POST',
            body: JSON.stringify(eventData)
        });
        return response.event;
    }
    async acknowledgeEvent(id, data) {
        const response = await this.makeRequest(`/api/events/${id}/acknowledge`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
        return response.event;
    }
    async resolveEvent(id, data) {
        const response = await this.makeRequest(`/api/events/${id}/resolve`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
        return response.event;
    }
    async getEventStats() {
        return this.makeRequest('/api/events/stats');
    }
    // ===== SYSTEM METHODS =====
    async getHealth() {
        return this.makeRequest('/health');
    }
    async getSystemStatus() {
        return this.makeRequest('/api/status');
    }
    // ===== UTILITY METHODS =====
    isAuthenticated() {
        return !!this.accessToken;
    }
    getAccessToken() {
        return this.accessToken;
    }
}
const apiService = new ApiService();
const __TURBOPACK__default__export__ = apiService;
}}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-ssr] (ecmascript)");
"use client";
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
const AuthProvider = ({ children })=>{
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Check if user is authenticated on app load
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        checkAuthStatus();
    }, []);
    const checkAuthStatus = async ()=>{
        try {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].isAuthenticated()) {
                const userProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].getProfile();
                setUser(userProfile);
            } else {
                // No token, user is not authenticated
                setUser(null);
            }
        } catch (error) {
            console.error('Failed to check auth status:', error);
            // Clear invalid tokens
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].logout();
            setUser(null);
        } finally{
            setLoading(false);
        }
    };
    const login = async (email, password)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].login({
                email,
                password
            });
            setUser(response.user);
        } catch (error) {
            throw error;
        }
    };
    const register = async (userData)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].register(userData);
            setUser(response.user);
        } catch (error) {
            throw error;
        }
    };
    const logout = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].logout();
            setUser(null);
        } catch (error) {
            console.error('Logout error:', error);
            // Clear user state even if logout request fails
            setUser(null);
        }
    };
    const refreshUser = async ()=>{
        try {
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].isAuthenticated()) {
                const userProfile = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["apiService"].getProfile();
                setUser(userProfile);
            }
        } catch (error) {
            console.error('Failed to refresh user:', error);
            // If refresh fails, user might need to login again
            setUser(null);
        }
    };
    const value = {
        user,
        loading,
        isAuthenticated: !!user,
        login,
        register,
        logout,
        refreshUser
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
};
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__c3c9502c._.js.map
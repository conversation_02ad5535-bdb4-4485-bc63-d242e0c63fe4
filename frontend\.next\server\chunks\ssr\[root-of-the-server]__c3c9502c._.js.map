{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/api.ts"], "sourcesContent": ["// API Service Layer for Frontend-Backend Integration\n\nimport {\n  LoginRequest,\n  RegisterRequest,\n  AuthResponse,\n  RefreshTokenRequest,\n  Camera,\n  CreateCameraRequest,\n  UpdateCameraRequest,\n  CameraStats,\n  Event,\n  CreateEventRequest,\n  EventFilters,\n  AcknowledgeEventRequest,\n  ResolveEventRequest,\n  EventStats,\n  ApiResponse,\n  PaginatedResponse,\n  ApiError,\n  HealthStatus,\n  SystemStatus\n} from '@/types/api'\n\nclass ApiService {\n  private baseUrl: string\n  private accessToken: string | null = null\n  private refreshToken: string | null = null\n\n  constructor() {\n    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'\n    \n    // Load tokens from localStorage on initialization\n    if (typeof window !== 'undefined') {\n      this.accessToken = localStorage.getItem('accessToken')\n      this.refreshToken = localStorage.getItem('refreshToken')\n    }\n  }\n\n  // ===== PRIVATE HELPER METHODS =====\n  private async makeRequest<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseUrl}${endpoint}`\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    }\n\n    // Add authorization header if token exists\n    if (this.accessToken) {\n      config.headers = {\n        ...config.headers,\n        Authorization: `Bearer ${this.accessToken}`,\n      }\n    }\n\n    try {\n      const response = await fetch(url, config)\n      \n      // Handle 401 - try to refresh token\n      if (response.status === 401 && this.refreshToken) {\n        const refreshed = await this.refreshAccessToken()\n        if (refreshed) {\n          // Retry the original request with new token\n          config.headers = {\n            ...config.headers,\n            Authorization: `Bearer ${this.accessToken}`,\n          }\n          const retryResponse = await fetch(url, config)\n          return this.handleResponse<T>(retryResponse)\n        }\n      }\n\n      return this.handleResponse<T>(response)\n    } catch (error) {\n      console.error('API request failed:', error)\n      throw new Error('Network error occurred')\n    }\n  }\n\n  private async handleResponse<T>(response: Response): Promise<T> {\n    const contentType = response.headers.get('content-type')\n    const isJson = contentType?.includes('application/json')\n    \n    const data = isJson ? await response.json() : await response.text()\n\n    if (!response.ok) {\n      const error: ApiError = {\n        error: data.error || `HTTP ${response.status}`,\n        details: data.details,\n        status: response.status,\n      }\n      throw error\n    }\n\n    return data\n  }\n\n  private setTokens(accessToken: string, refreshToken: string) {\n    this.accessToken = accessToken\n    this.refreshToken = refreshToken\n    \n    if (typeof window !== 'undefined') {\n      localStorage.setItem('accessToken', accessToken)\n      localStorage.setItem('refreshToken', refreshToken)\n    }\n  }\n\n  private clearTokens() {\n    this.accessToken = null\n    this.refreshToken = null\n    \n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('accessToken')\n      localStorage.removeItem('refreshToken')\n    }\n  }\n\n  // ===== AUTHENTICATION METHODS =====\n  async login(credentials: LoginRequest): Promise<AuthResponse> {\n    const response = await this.makeRequest<AuthResponse>('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    })\n\n    this.setTokens(response.tokens.accessToken, response.tokens.refreshToken)\n    return response\n  }\n\n  async register(userData: RegisterRequest): Promise<AuthResponse> {\n    const response = await this.makeRequest<AuthResponse>('/api/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    })\n\n    this.setTokens(response.tokens.accessToken, response.tokens.refreshToken)\n    return response\n  }\n\n  async refreshAccessToken(): Promise<boolean> {\n    if (!this.refreshToken) return false\n\n    try {\n      const response = await this.makeRequest<AuthResponse>('/api/auth/refresh', {\n        method: 'POST',\n        body: JSON.stringify({ refreshToken: this.refreshToken }),\n      })\n\n      this.setTokens(response.tokens.accessToken, response.tokens.refreshToken)\n      return true\n    } catch (error) {\n      this.clearTokens()\n      return false\n    }\n  }\n\n  async logout(): Promise<void> {\n    this.clearTokens()\n  }\n\n  async getProfile(): Promise<AuthResponse['user']> {\n    const response = await this.makeRequest<{ user: AuthResponse['user'] }>('/api/auth/me')\n    return response.user\n  }\n\n  // ===== CAMERA METHODS =====\n  async getCameras(filters?: {\n    status?: string\n    location?: string\n    limit?: number\n    page?: number\n  }): Promise<{ cameras: Camera[]; pagination?: any }> {\n    const params = new URLSearchParams()\n    if (filters?.status) params.append('status', filters.status)\n    if (filters?.location) params.append('location', filters.location)\n    if (filters?.limit) params.append('limit', filters.limit.toString())\n    if (filters?.page) params.append('page', filters.page.toString())\n\n    const queryString = params.toString()\n    const endpoint = `/api/cameras${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<{ cameras: Camera[]; pagination?: any }>(endpoint)\n  }\n\n  async getCamera(id: string): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`)\n    return response.camera\n  }\n\n  async createCamera(cameraData: CreateCameraRequest): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>('/api/cameras', {\n      method: 'POST',\n      body: JSON.stringify(cameraData),\n    })\n    return response.camera\n  }\n\n  async updateCamera(id: string, updates: UpdateCameraRequest): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n    return response.camera\n  }\n\n  async deleteCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async armCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}/arm`, {\n      method: 'POST',\n    })\n  }\n\n  async disarmCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}/disarm`, {\n      method: 'POST',\n    })\n  }\n\n  async getCameraStats(): Promise<CameraStats> {\n    return this.makeRequest<CameraStats>('/api/cameras/stats')\n  }\n\n  // ===== EVENT METHODS =====\n  async getEvents(filters?: EventFilters): Promise<{ events: Event[]; pagination?: any }> {\n    const params = new URLSearchParams()\n    if (filters?.type) params.append('type', filters.type)\n    if (filters?.severity) params.append('severity', filters.severity)\n    if (filters?.status) params.append('status', filters.status)\n    if (filters?.cameraId) params.append('cameraId', filters.cameraId)\n    if (filters?.startDate) params.append('startDate', filters.startDate)\n    if (filters?.endDate) params.append('endDate', filters.endDate)\n    if (filters?.limit) params.append('limit', filters.limit.toString())\n    if (filters?.page) params.append('page', filters.page.toString())\n\n    const queryString = params.toString()\n    const endpoint = `/api/events${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<{ events: Event[]; pagination?: any }>(endpoint)\n  }\n\n  async getEvent(id: string): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}`)\n    return response.event\n  }\n\n  async createEvent(eventData: CreateEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>('/api/events', {\n      method: 'POST',\n      body: JSON.stringify(eventData),\n    })\n    return response.event\n  }\n\n  async acknowledgeEvent(id: string, data: AcknowledgeEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/acknowledge`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n    return response.event\n  }\n\n  async resolveEvent(id: string, data: ResolveEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/resolve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n    return response.event\n  }\n\n  async getEventStats(): Promise<EventStats> {\n    return this.makeRequest<EventStats>('/api/events/stats')\n  }\n\n  // ===== SYSTEM METHODS =====\n  async getHealth(): Promise<HealthStatus> {\n    return this.makeRequest<HealthStatus>('/health')\n  }\n\n  async getSystemStatus(): Promise<SystemStatus> {\n    return this.makeRequest<SystemStatus>('/api/status')\n  }\n\n  // ===== UTILITY METHODS =====\n  isAuthenticated(): boolean {\n    return !!this.accessToken\n  }\n\n  getAccessToken(): string | null {\n    return this.accessToken\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService()\nexport default apiService\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAwBrD,MAAM;IACI,QAAe;IACf,cAA6B,KAAI;IACjC,eAA8B,KAAI;IAE1C,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QAElD,kDAAkD;QAClD,uCAAmC;;QAGnC;IACF;IAEA,qCAAqC;IACrC,MAAc,YACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,2CAA2C;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,OAAO,GAAG;gBACf,GAAG,OAAO,OAAO;gBACjB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;YAC7C;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,oCAAoC;YACpC,IAAI,SAAS,MAAM,KAAK,OAAO,IAAI,CAAC,YAAY,EAAE;gBAChD,MAAM,YAAY,MAAM,IAAI,CAAC,kBAAkB;gBAC/C,IAAI,WAAW;oBACb,4CAA4C;oBAC5C,OAAO,OAAO,GAAG;wBACf,GAAG,OAAO,OAAO;wBACjB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;oBAC7C;oBACA,MAAM,gBAAgB,MAAM,MAAM,KAAK;oBACvC,OAAO,IAAI,CAAC,cAAc,CAAI;gBAChC;YACF;YAEA,OAAO,IAAI,CAAC,cAAc,CAAI;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAc,eAAkB,QAAkB,EAAc;QAC9D,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,aAAa,SAAS;QAErC,MAAM,OAAO,SAAS,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI;QAEjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAkB;gBACtB,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;gBAC9C,SAAS,KAAK,OAAO;gBACrB,QAAQ,SAAS,MAAM;YACzB;YACA,MAAM;QACR;QAEA,OAAO;IACT;IAEQ,UAAU,WAAmB,EAAE,YAAoB,EAAE;QAC3D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QAEpB,uCAAmC;;QAGnC;IACF;IAEQ,cAAc;QACpB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QAEpB,uCAAmC;;QAGnC;IACF;IAEA,qCAAqC;IACrC,MAAM,MAAM,WAAyB,EAAyB;QAC5D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAe,mBAAmB;YACvE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,YAAY;QACxE,OAAO;IACT;IAEA,MAAM,SAAS,QAAyB,EAAyB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAe,sBAAsB;YAC1E,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,YAAY;QACxE,OAAO;IACT;IAEA,MAAM,qBAAuC;QAC3C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAe,qBAAqB;gBACzE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE,cAAc,IAAI,CAAC,YAAY;gBAAC;YACzD;YAEA,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,YAAY;YACxE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW;YAChB,OAAO;QACT;IACF;IAEA,MAAM,SAAwB;QAC5B,IAAI,CAAC,WAAW;IAClB;IAEA,MAAM,aAA4C;QAChD,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAiC;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,WAAW,OAKhB,EAAoD;QACnD,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAE9D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,OAAO,IAAI,CAAC,WAAW,CAA0C;IACnE;IAEA,MAAM,UAAU,EAAU,EAAmB;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,CAAC,aAAa,EAAE,IAAI;QAChF,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,UAA+B,EAAmB;QACnE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,gBAAgB;YAC1E,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,EAAU,EAAE,OAA4B,EAAmB;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,CAAC,aAAa,EAAE,IAAI,EAAE;YAChF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;YAC3C,QAAQ;QACV;IACF;IAEA,MAAM,UAAU,EAAU,EAAiB;QACzC,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,EAAE;YAC/C,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YAClD,QAAQ;QACV;IACF;IAEA,MAAM,iBAAuC;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAc;IACvC;IAEA,4BAA4B;IAC5B,MAAM,UAAU,OAAsB,EAAkD;QACtF,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;QACrD,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;QACpE,IAAI,SAAS,SAAS,OAAO,MAAM,CAAC,WAAW,QAAQ,OAAO;QAC9D,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAE9D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAErE,OAAO,IAAI,CAAC,WAAW,CAAwC;IACjE;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,IAAI;QAC7E,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,YAAY,SAA6B,EAAkB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,eAAe;YACvE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,iBAAiB,EAAU,EAAE,IAA6B,EAAkB;QAChF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,GAAG,YAAY,CAAC,EAAE;YACzF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,aAAa,EAAU,EAAE,IAAyB,EAAkB;QACxE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,EAAE;YACrF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,gBAAqC;QACzC,OAAO,IAAI,CAAC,WAAW,CAAa;IACtC;IAEA,6BAA6B;IAC7B,MAAM,YAAmC;QACvC,OAAO,IAAI,CAAC,WAAW,CAAe;IACxC;IAEA,MAAM,kBAAyC;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAe;IACxC;IAEA,8BAA8B;IAC9B,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW;IAC3B;IAEA,iBAAgC;QAC9B,OAAO,IAAI,CAAC,WAAW;IACzB;AACF;AAGO,MAAM,aAAa,IAAI;uCACf", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { apiService } from '@/lib/api'\n\ninterface User {\n  id: string\n  username: string\n  email: string\n  firstName: string\n  lastName: string\n  role: 'admin' | 'operator' | 'viewer'\n  isActive: boolean\n  lastLogin?: string\n  preferences: {\n    theme: 'light' | 'dark' | 'system'\n    language: string\n    timezone: string\n    notifications: {\n      email: boolean\n      push: boolean\n      sms: boolean\n    }\n  }\n  createdAt: string\n  updatedAt: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  isAuthenticated: boolean\n  login: (email: string, password: string) => Promise<void>\n  register: (userData: {\n    firstName: string\n    lastName: string\n    username: string\n    email: string\n    password: string\n  }) => Promise<void>\n  logout: () => Promise<void>\n  refreshUser: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  // Check if user is authenticated on app load\n  useEffect(() => {\n    checkAuthStatus()\n  }, [])\n\n  const checkAuthStatus = async () => {\n    try {\n      if (apiService.isAuthenticated()) {\n        const userProfile = await apiService.getProfile()\n        setUser(userProfile)\n      } else {\n        // No token, user is not authenticated\n        setUser(null)\n      }\n    } catch (error) {\n      console.error('Failed to check auth status:', error)\n      // Clear invalid tokens\n      await apiService.logout()\n      setUser(null)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const login = async (email: string, password: string) => {\n    try {\n      const response = await apiService.login({ email, password })\n      setUser(response.user)\n    } catch (error) {\n      throw error\n    }\n  }\n\n  const register = async (userData: {\n    firstName: string\n    lastName: string\n    username: string\n    email: string\n    password: string\n  }) => {\n    try {\n      const response = await apiService.register(userData)\n      setUser(response.user)\n    } catch (error) {\n      throw error\n    }\n  }\n\n  const logout = async () => {\n    try {\n      await apiService.logout()\n      setUser(null)\n    } catch (error) {\n      console.error('Logout error:', error)\n      // Clear user state even if logout request fails\n      setUser(null)\n    }\n  }\n\n  const refreshUser = async () => {\n    try {\n      if (apiService.isAuthenticated()) {\n        const userProfile = await apiService.getProfile()\n        setUser(userProfile)\n      }\n    } catch (error) {\n      console.error('Failed to refresh user:', error)\n      // If refresh fails, user might need to login again\n      setUser(null)\n    }\n  }\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    isAuthenticated: !!user,\n    login,\n    register,\n    logout,\n    refreshUser\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AA4CA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE;IACpE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,iHAAA,CAAA,aAAU,CAAC,eAAe,IAAI;gBAChC,MAAM,cAAc,MAAM,iHAAA,CAAA,aAAU,CAAC,UAAU;gBAC/C,QAAQ;YACV,OAAO;gBACL,sCAAsC;gBACtC,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,uBAAuB;YACvB,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM;YACvB,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,KAAK,CAAC;gBAAE;gBAAO;YAAS;YAC1D,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO;QAOtB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,QAAQ,CAAC;YAC3C,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,iHAAA,CAAA,aAAU,CAAC,MAAM;YACvB,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,gDAAgD;YAChD,QAAQ;QACV;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,IAAI,iHAAA,CAAA,aAAU,CAAC,eAAe,IAAI;gBAChC,MAAM,cAAc,MAAM,iHAAA,CAAA,aAAU,CAAC,UAAU;gBAC/C,QAAQ;YACV;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,mDAAmD;YACnD,QAAQ;QACV;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 381, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}
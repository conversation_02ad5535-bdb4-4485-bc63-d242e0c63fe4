{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ProtectedRoute.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Loader2 } from 'lucide-react'\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode\n  requiredRole?: 'admin' | 'operator' | 'viewer'\n}\n\nexport const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  requiredRole = 'viewer' \n}) => {\n  const { user, loading, isAuthenticated } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [loading, isAuthenticated, router])\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin text-blue-600\" />\n          <span className=\"text-gray-600 dark:text-gray-400\">Loading...</span>\n        </div>\n      </div>\n    )\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    return null\n  }\n\n  // Check role permissions\n  if (user && requiredRole) {\n    const roleHierarchy = {\n      viewer: 1,\n      operator: 2,\n      admin: 3\n    }\n\n    const userRoleLevel = roleHierarchy[user.role]\n    const requiredRoleLevel = roleHierarchy[requiredRole]\n\n    if (userRoleLevel < requiredRoleLevel) {\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n              Access Denied\n            </h1>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n              You don't have permission to access this page.\n            </p>\n            <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n              Required role: {requiredRole} | Your role: {user.role}\n            </p>\n          </div>\n        </div>\n      )\n    }\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYO,MAAM,iBAAgD,CAAC,EAC5D,QAAQ,EACR,eAAe,QAAQ,EACxB;IACC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;YAChC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAiB;KAAO;IAErC,qDAAqD;IACrD,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAI3D;IAEA,yCAAyC;IACzC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,yBAAyB;IACzB,IAAI,QAAQ,cAAc;QACxB,MAAM,gBAAgB;YACpB,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QAEA,MAAM,gBAAgB,aAAa,CAAC,KAAK,IAAI,CAAC;QAC9C,MAAM,oBAAoB,aAAa,CAAC,aAAa;QAErD,IAAI,gBAAgB,mBAAmB;YACrC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAwC;;;;;;sCAGrD,8OAAC;4BAAE,WAAU;;gCAA2C;gCACtC;gCAAa;gCAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;QAK/D;IACF;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  Camera,\n  Activity,\n  Shield,\n  Zap,\n  TrendingUp,\n  AlertTriangle,\n  CheckCircle,\n  Clock\n} from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport { ProtectedRoute } from \"@/components/ProtectedRoute\"\n\nconst stats = [\n  {\n    name: \"Active Cameras\",\n    value: \"24\",\n    change: \"+2\",\n    changeType: \"positive\",\n    icon: Camera,\n    color: \"blue\",\n  },\n  {\n    name: \"Events Today\",\n    value: \"1,247\",\n    change: \"+12%\",\n    changeType: \"positive\",\n    icon: Activity,\n    color: \"green\",\n  },\n  {\n    name: \"AI Detections\",\n    value: \"89\",\n    change: \"+5\",\n    changeType: \"positive\",\n    icon: Zap,\n    color: \"purple\",\n  },\n  {\n    name: \"Security Alerts\",\n    value: \"3\",\n    change: \"-2\",\n    changeType: \"negative\",\n    icon: Shield,\n    color: \"red\",\n  },\n]\n\nconst recentEvents = [\n  {\n    id: 1,\n    type: \"Person Detected\",\n    camera: \"Front Entrance\",\n    time: \"2 minutes ago\",\n    status: \"active\",\n    confidence: 94\n  },\n  {\n    id: 2,\n    type: \"Vehicle Recognition\",\n    camera: \"Parking Lot A\",\n    time: \"5 minutes ago\",\n    status: \"resolved\",\n    confidence: 87\n  },\n  {\n    id: 3,\n    type: \"Motion Alert\",\n    camera: \"Warehouse Door\",\n    time: \"12 minutes ago\",\n    status: \"investigating\",\n    confidence: 76\n  },\n  {\n    id: 4,\n    type: \"Face Recognition\",\n    camera: \"Main Lobby\",\n    time: \"18 minutes ago\",\n    status: \"resolved\",\n    confidence: 92\n  },\n  {\n    id: 5,\n    type: \"Intrusion Alert\",\n    camera: \"Perimeter Fence\",\n    time: \"25 minutes ago\",\n    status: \"active\",\n    confidence: 89\n  }\n]\n\nfunction Dashboard() {\n  return (\n    <div className=\"flex-1 space-y-6 p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            Dashboard\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Welcome back! Here's what's happening with your security system.\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <Button variant=\"outline\">\n            <TrendingUp className=\"mr-2 h-4 w-4\" />\n            View Reports\n          </Button>\n          <Button>\n            <Shield className=\"mr-2 h-4 w-4\" />\n            Arm All Cameras\n          </Button>\n        </div>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-4\">\n        {stats.map((stat, index) => (\n          <motion.div\n            key={stat.name}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n          >\n            <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium text-gray-600 dark:text-gray-400\">\n                  {stat.name}\n                </CardTitle>\n                <stat.icon className={`h-4 w-4 text-${stat.color}-600`} />\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  {stat.value}\n                </div>\n                <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                  <span\n                    className={`${\n                      stat.changeType === \"positive\"\n                        ? \"text-green-600\"\n                        : \"text-red-600\"\n                    }`}\n                  >\n                    {stat.change}\n                  </span>{\" \"}\n                  from last hour\n                </p>\n              </CardContent>\n            </Card>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* Recent Events and Analytics */}\n      <div className=\"grid gap-6 lg:grid-cols-3\">\n        {/* Recent Events */}\n        <div className=\"lg:col-span-2\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Activity className=\"h-5 w-5\" />\n                <span>Recent Events</span>\n              </CardTitle>\n              <CardDescription>\n                Latest security events from your cameras\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {recentEvents.map((event) => (\n                <motion.div\n                  key={event.id}\n                  className=\"flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors\"\n                  whileHover={{ scale: 1.02 }}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex-shrink-0\">\n                      {event.status === \"active\" && (\n                        <AlertTriangle className=\"h-5 w-5 text-red-500\" />\n                      )}\n                      {event.status === \"resolved\" && (\n                        <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                      )}\n                      {event.status === \"investigating\" && (\n                        <Clock className=\"h-5 w-5 text-amber-500\" />\n                      )}\n                    </div>\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                        {event.type}\n                      </p>\n                      <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                        {event.camera} • {event.time}\n                      </p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Badge\n                      variant={\n                        event.status === \"active\"\n                          ? \"destructive\"\n                          : event.status === \"resolved\"\n                          ? \"success\"\n                          : \"warning\"\n                      }\n                      size=\"sm\"\n                    >\n                      {event.confidence}%\n                    </Badge>\n                  </div>\n                </motion.div>\n              ))}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* System Status */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Shield className=\"h-5 w-5\" />\n              <span>System Status</span>\n            </CardTitle>\n            <CardDescription>\n              Current status of all system components\n            </CardDescription>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Camera Network\n                </span>\n                <Badge variant=\"success\">Online</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  AI Processing\n                </span>\n                <Badge variant=\"success\">Active</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Database\n                </span>\n                <Badge variant=\"success\">Connected</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Storage\n                </span>\n                <Badge variant=\"warning\">78% Full</Badge>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Notifications\n                </span>\n                <Badge variant=\"success\">Enabled</Badge>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n\nexport default function DashboardPage() {\n  return (\n    <ProtectedRoute>\n      <Dashboard />\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AAjBA;;;;;;;;AAmBA,MAAM,QAAQ;IACZ;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,YAAY;QACZ,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,YAAY;IACd;CACD;AAED,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAIlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGzC,8OAAC,kIAAA,CAAA,SAAM;;kDACL,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,KAAK,IAAI;;;;;;sDAEZ,8OAAC,KAAK,IAAI;4CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;;8CAExD,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDACC,WAAW,GACT,KAAK,UAAU,KAAK,aAChB,mBACA,gBACJ;8DAED,KAAK,MAAM;;;;;;gDACN;gDAAI;;;;;;;;;;;;;;;;;;;uBAzBb,KAAK,IAAI;;;;;;;;;;0BAmCpB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,aAAa,GAAG,CAAC,CAAC,sBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;gEACZ,MAAM,MAAM,KAAK,0BAChB,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAE1B,MAAM,MAAM,KAAK,4BAChB,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAExB,MAAM,MAAM,KAAK,iCAChB,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;sEAGrB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EACV,MAAM,IAAI;;;;;;8EAEb,8OAAC;oEAAE,WAAU;;wEACV,MAAM,MAAM;wEAAC;wEAAI,MAAM,IAAI;;;;;;;;;;;;;;;;;;;8DAIlC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDACJ,SACE,MAAM,MAAM,KAAK,WACb,gBACA,MAAM,MAAM,KAAK,aACjB,YACA;wDAEN,MAAK;;4DAEJ,MAAM,UAAU;4DAAC;;;;;;;;;;;;;2CApCjB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;kCA8CvB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA2C;;;;;;8DAG3D,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;AAEe,SAAS;IACtB,qBACE,8OAAC,oIAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1016, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
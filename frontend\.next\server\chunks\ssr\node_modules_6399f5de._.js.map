{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-context/src/create-context.tsx"], "sourcesContent": ["import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  const Provider: React.FC<ContextValueType & { children: React.ReactNode }> = (props) => {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  };\n\n  Provider.displayName = rootComponentName + 'Provider';\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    const Provider: React.FC<\n      ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    > = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    };\n\n    Provider.displayName = rootComponentName + 'Provider';\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "names": ["createContext", "useContext", "createScope", "nextScopes"], "mappings": ";;;;;AAAA,YAAY,WAAW;AAaZ;;;AAXX,SAASA,eACP,iBAAA,EACA,cAAA,EACA;IACA,MAAM,oNAAgB,gBAAA,EAA4C,cAAc;IAEhF,MAAM,WAAuE,CAAC,UAAU;QACtF,MAAM,EAAE,QAAA,EAAU,GAAG,QAAQ,CAAA,GAAI;QAGjC,MAAM,kNAAc,UAAA,EAAQ,IAAM,SAAS,OAAO,MAAA,CAAO,OAAO,CAAC;QACjE,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,QAAQ,QAAA,EAAR;YAAiB;YAAe;QAAA,CAAS;IACnD;IAEA,SAAS,WAAA,GAAc,oBAAoB;IAE3C,SAASC,YAAW,YAAA,EAAsB;QACxC,MAAM,oNAAgB,aAAA,EAAW,OAAO;QACxC,IAAI,QAAS,CAAA,OAAO;QACpB,IAAI,mBAAmB,KAAA,EAAW,CAAA,OAAO;QAEzC,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,YAAY,CAAA,yBAAA,EAA4B,iBAAiB,CAAA,EAAA,CAAI;IACpF;IAEA,OAAO;QAAC;QAAUA,WAAU;KAAA;AAC9B;AAaA,SAAS,mBAAmB,SAAA,EAAmB,yBAAwC,CAAC,CAAA,EAAG;IACzF,IAAI,kBAAyB,CAAC,CAAA;IAM9B,SAASD,eACP,iBAAA,EACA,cAAA,EACA;QACA,MAAM,wNAAoB,gBAAA,EAA4C,cAAc;QACpF,MAAM,QAAQ,gBAAgB,MAAA;QAC9B,kBAAkB,CAAC;eAAG;YAAiB,cAAc;SAAA;QAErD,MAAM,WAEF,CAAC,UAAU;YACb,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,QAAQ,CAAA,GAAI;YACxC,MAAM,UAAU,OAAA,CAAQ,SAAS,CAAA,EAAA,CAAI,KAAK,CAAA,IAAK;YAG/C,MAAM,SAAc,mNAAA,EAAQ,IAAM,SAAS,OAAO,MAAA,CAAO,OAAO,CAAC;YACjE,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,QAAQ,QAAA,EAAR;gBAAiB;gBAAe;YAAA,CAAS;QACnD;QAEA,SAAS,WAAA,GAAc,oBAAoB;QAE3C,SAASC,YAAW,YAAA,EAAsB,KAAA,EAA4C;YACpF,MAAM,UAAU,OAAA,CAAQ,SAAS,CAAA,EAAA,CAAI,KAAK,CAAA,IAAK;YAC/C,MAAM,WAAgB,sNAAA,EAAW,OAAO;YACxC,IAAI,QAAS,CAAA,OAAO;YACpB,IAAI,mBAAmB,KAAA,EAAW,CAAA,OAAO;YAEzC,MAAM,IAAI,MAAM,CAAA,EAAA,EAAK,YAAY,CAAA,yBAAA,EAA4B,iBAAiB,CAAA,EAAA,CAAI;QACpF;QAEA,OAAO;YAAC;YAAUA,WAAU;SAAA;IAC9B;IAMA,MAAM,cAA2B,MAAM;QACrC,MAAM,gBAAgB,gBAAgB,GAAA,CAAI,CAAC,mBAAmB;YAC5D,iNAAa,gBAAA,EAAc,cAAc;QAC3C,CAAC;QACD,OAAO,SAAS,SAAS,KAAA,EAAc;YACrC,MAAM,WAAW,OAAA,CAAQ,SAAS,CAAA,IAAK;YACvC,iNAAa,UAAA,EACX,IAAA,CAAO;oBAAE,CAAC,CAAA,OAAA,EAAU,SAAS,EAAE,CAAA,EAAG;wBAAE,GAAG,KAAA;wBAAO,CAAC,SAAS,CAAA,EAAG;oBAAS;gBAAE,CAAA,GACtE;gBAAC;gBAAO,QAAQ;aAAA;QAEpB;IACF;IAEA,YAAY,SAAA,GAAY;IACxB,OAAO;QAACD;QAAe,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;KAAA;AACrF;AAMA,SAAS,qBAAA,GAAwB,MAAA,EAAuB;IACtD,MAAM,YAAY,MAAA,CAAO,CAAC,CAAA;IAC1B,IAAI,OAAO,MAAA,KAAW,EAAG,CAAA,OAAO;IAEhC,MAAM,cAA2B,MAAM;QACrC,MAAM,aAAa,OAAO,GAAA,CAAI,CAACE,eAAAA,CAAiB;gBAC9C,UAAUA,aAAY;gBACtB,WAAWA,aAAY,SAAA;YACzB,CAAA,CAAE;QAEF,OAAO,SAAS,kBAAkB,cAAA,EAAgB;YAChD,MAAM,aAAa,WAAW,MAAA,CAAO,CAACC,aAAY,EAAE,QAAA,EAAU,SAAA,CAAU,CAAA,KAAM;gBAI5E,MAAM,aAAa,SAAS,cAAc;gBAC1C,MAAM,eAAe,UAAA,CAAW,CAAA,OAAA,EAAU,SAAS,EAAE,CAAA;gBACrD,OAAO;oBAAE,GAAGA,WAAAA;oBAAY,GAAG,YAAA;gBAAa;YAC1C,GAAG,CAAC,CAAC;YAEL,iNAAa,UAAA,EAAQ,IAAA,CAAO;oBAAE,CAAC,CAAA,OAAA,EAAU,UAAU,SAAS,EAAE,CAAA,EAAG;gBAAW,CAAA,GAAI;gBAAC,UAAU;aAAC;QAC9F;IACF;IAEA,YAAY,SAAA,GAAY,UAAU,SAAA;IAClC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-compose-refs/src/compose-refs.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype PossibleRef<T> = React.Ref<T> | undefined;\n\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */\nfunction setRef<T>(ref: PossibleRef<T>, value: T) {\n  if (typeof ref === 'function') {\n    return ref(value);\n  } else if (ref !== null && ref !== undefined) {\n    ref.current = value;\n  }\n}\n\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */\nfunction composeRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == 'function') {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n\n    // React <19 will log an error to the console if a callback ref returns a\n    // value. We don't use ref cleanups internally so this will only happen if a\n    // user's ref callback returns a value, which we only expect if they are\n    // using the cleanup functionality added in React 19.\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == 'function') {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\n\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */\nfunction useComposedRefs<T>(...refs: PossibleRef<T>[]): React.RefCallback<T> {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return React.useCallback(composeRefs(...refs), refs);\n}\n\nexport { composeRefs, useComposedRefs };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;;AAQvB,SAAS,OAAU,GAAA,EAAqB,KAAA,EAAU;IAChD,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,IAAI,KAAK;IAClB,OAAA,IAAW,QAAQ,QAAQ,QAAQ,KAAA,GAAW;QAC5C,IAAI,OAAA,GAAU;IAChB;AACF;AAMA,SAAS,YAAA,GAAkB,IAAA,EAA8C;IACvE,OAAO,CAAC,SAAS;QACf,IAAI,aAAa;QACjB,MAAM,WAAW,KAAK,GAAA,CAAI,CAAC,QAAQ;YACjC,MAAM,UAAU,OAAO,KAAK,IAAI;YAChC,IAAI,CAAC,cAAc,OAAO,WAAW,YAAY;gBAC/C,aAAa;YACf;YACA,OAAO;QACT,CAAC;QAMD,IAAI,YAAY;YACd,OAAO,MAAM;gBACX,IAAA,IAAS,IAAI,GAAG,IAAI,SAAS,MAAA,EAAQ,IAAK;oBACxC,MAAM,UAAU,QAAA,CAAS,CAAC,CAAA;oBAC1B,IAAI,OAAO,WAAW,YAAY;wBAChC,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAA,CAAK,CAAC,CAAA,EAAG,IAAI;oBACtB;gBACF;YACF;QACF;IACF;AACF;AAMA,SAAS,gBAAA,GAAsB,IAAA,EAA8C;IAE3E,iNAAa,cAAA,EAAY,YAAY,GAAG,IAAI,GAAG,IAAI;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-slot/src/slot.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeRefs } from '@radix-ui/react-compose-refs';\n\n/* -------------------------------------------------------------------------------------------------\n * Slot\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotProps extends React.HTMLAttributes<HTMLElement> {\n  children?: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlot(ownerName: string) {\n  const SlotClone = createSlotClone(ownerName);\n  const Slot = React.forwardRef<HTMLElement, SlotProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = React.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n\n    if (slottable) {\n      // the new element to render is the one passed as a child of `Slottable`\n      const newElement = slottable.props.children;\n\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          // because the new element will be the one rendered, we are only interested\n          // in grabbing its children (`newElement.props.children`)\n          if (React.Children.count(newElement) > 1) return React.Children.only(null);\n          return React.isValidElement(newElement)\n            ? (newElement.props as { children: React.ReactNode }).children\n            : null;\n        } else {\n          return child;\n        }\n      });\n\n      return (\n        <SlotClone {...slotProps} ref={forwardedRef}>\n          {React.isValidElement(newElement)\n            ? React.cloneElement(newElement, undefined, newChildren)\n            : null}\n        </SlotClone>\n      );\n    }\n\n    return (\n      <SlotClone {...slotProps} ref={forwardedRef}>\n        {children}\n      </SlotClone>\n    );\n  });\n\n  Slot.displayName = `${ownerName}.Slot`;\n  return Slot;\n}\n\nconst Slot = createSlot('Slot');\n\n/* -------------------------------------------------------------------------------------------------\n * SlotClone\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SlotCloneProps {\n  children: React.ReactNode;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ function createSlotClone(ownerName: string) {\n  const SlotClone = React.forwardRef<any, SlotCloneProps>((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n\n    if (React.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props = mergeProps(slotProps, children.props as AnyProps);\n      // do not pass ref to React.Fragment for React 19 compatibility\n      if (children.type !== React.Fragment) {\n        props.ref = forwardedRef ? composeRefs(forwardedRef, childrenRef) : childrenRef;\n      }\n      return React.cloneElement(children, props);\n    }\n\n    return React.Children.count(children) > 1 ? React.Children.only(null) : null;\n  });\n\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Slottable\n * -----------------------------------------------------------------------------------------------*/\n\nconst SLOTTABLE_IDENTIFIER = Symbol('radix.slottable');\n\ninterface SlottableProps {\n  children: React.ReactNode;\n}\n\ninterface SlottableComponent extends React.FC<SlottableProps> {\n  __radixId: symbol;\n}\n\n/* @__NO_SIDE_EFFECTS__ */ export function createSlottable(ownerName: string) {\n  const Slottable: SlottableComponent = ({ children }) => {\n    return <>{children}</>;\n  };\n  Slottable.displayName = `${ownerName}.Slottable`;\n  Slottable.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable;\n}\n\nconst Slottable = createSlottable('Slottable');\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype AnyProps = Record<string, any>;\n\nfunction isSlottable(\n  child: React.ReactNode\n): child is React.ReactElement<SlottableProps, typeof Slottable> {\n  return (\n    React.isValidElement(child) &&\n    typeof child.type === 'function' &&\n    '__radixId' in child.type &&\n    child.type.__radixId === SLOTTABLE_IDENTIFIER\n  );\n}\n\nfunction mergeProps(slotProps: AnyProps, childProps: AnyProps) {\n  // all child props should override\n  const overrideProps = { ...childProps };\n\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      // if the handler exists on both, we compose them\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args: unknown[]) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      }\n      // but if it exists only on the slot, we use only this one\n      else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    }\n    // if it's `style`, we merge them\n    else if (propName === 'style') {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === 'className') {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(' ');\n    }\n  }\n\n  return { ...slotProps, ...overrideProps };\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element.props as { ref?: React.Ref<unknown> }).ref;\n  }\n\n  // Not DEV\n  return (element.props as { ref?: React.Ref<unknown> }).ref || (element as any).ref;\n}\n\nexport {\n  Slot,\n  Slottable,\n  //\n  Slot as Root,\n};\nexport type { SlotProps };\n"], "names": ["Fragment", "Slot", "props", "Slottable"], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,mBAAmB;AAmCpB,SAkEG,YAAAA,WAlEH;;;;AAAA,uBAAA;AAzB0B,SAAS,WAAW,SAAA,EAAmB;IACvE,MAAM,YAAY,aAAA,GAAA,gBAAgB,SAAS;IAC3C,MAAMC,kNAAa,aAAA,EAAmC,CAAC,OAAO,iBAAiB;QAC7E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QACnC,MAAM,sNAAsB,WAAA,CAAS,OAAA,CAAQ,QAAQ;QACrD,MAAM,YAAY,cAAc,IAAA,CAAK,WAAW;QAEhD,IAAI,WAAW;YAEb,MAAM,aAAa,UAAU,KAAA,CAAM,QAAA;YAEnC,MAAM,cAAc,cAAc,GAAA,CAAI,CAAC,UAAU;gBAC/C,IAAI,UAAU,WAAW;oBAGvB,IAAU,iNAAA,CAAS,KAAA,CAAM,UAAU,IAAI,EAAG,CAAA,6MAAa,WAAA,CAAS,IAAA,CAAK,IAAI;oBACzE,WAAa,uNAAA,EAAe,UAAU,IACjC,WAAW,KAAA,CAAwC,QAAA,GACpD;gBACN,OAAO;oBACL,OAAO;gBACT;YACF,CAAC;YAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;gBAAW,GAAG,SAAA;gBAAW,KAAK;gBAC5B,oNAAM,iBAAA,EAAe,UAAU,QACtB,qNAAA,EAAa,YAAY,KAAA,GAAW,WAAW,IACrD;YAAA,CACN;QAEJ;QAEA,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAA;YAAW,GAAG,SAAA;YAAW,KAAK;YAC5B;QAAA,CACH;IAEJ,CAAC;IAEDA,MAAK,WAAA,GAAc,GAAG,SAAS,CAAA,KAAA,CAAA;IAC/B,OAAOA;AACT;AAEA,IAAM,OAAO,aAAA,GAAA,WAAW,MAAM;AAAA,uBAAA;AAUH,SAAS,gBAAgB,SAAA,EAAmB;IACrE,MAAM,qNAAkB,cAAA,EAAgC,CAAC,OAAO,iBAAiB;QAC/E,MAAM,EAAE,QAAA,EAAU,GAAG,UAAU,CAAA,GAAI;QAEnC,QAAU,uNAAA,EAAe,QAAQ,GAAG;YAClC,MAAM,cAAc,cAAc,QAAQ;YAC1C,MAAMC,SAAQ,WAAW,WAAW,SAAS,KAAiB;YAE9D,IAAI,SAAS,IAAA,2MAAe,WAAA,EAAU;gBACpCA,OAAM,GAAA,GAAM,gBAAe,6LAAA,EAAY,cAAc,WAAW,IAAI;YACtE;YACA,iNAAa,eAAA,EAAa,UAAUA,MAAK;QAC3C;QAEA,6MAAa,WAAA,CAAS,KAAA,CAAM,QAAQ,IAAI,0MAAU,WAAA,CAAS,IAAA,CAAK,IAAI,IAAI;IAC1E,CAAC;IAED,UAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpC,OAAO;AACT;AAMA,IAAM,uBAAuB,OAAO,iBAAiB;AAAA,uBAAA;AAUnB,SAAS,gBAAgB,SAAA,EAAmB;IAC5E,MAAMC,aAAgC,CAAC,EAAE,QAAA,CAAS,CAAA,KAAM;QACtD,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,0NAAAH,WAAAA,EAAA;YAAG;QAAA,CAAS;IACrB;IACAG,WAAU,WAAA,GAAc,GAAG,SAAS,CAAA,UAAA,CAAA;IACpCA,WAAU,SAAA,GAAY;IACtB,OAAOA;AACT;AAEA,IAAM,YAAY,aAAA,GAAA,gBAAgB,WAAW;AAM7C,SAAS,YACP,KAAA,EAC+D;IAC/D,QACQ,0NAAA,EAAe,KAAK,KAC1B,OAAO,MAAM,IAAA,KAAS,cACtB,eAAe,MAAM,IAAA,IACrB,MAAM,IAAA,CAAK,SAAA,KAAc;AAE7B;AAEA,SAAS,WAAW,SAAA,EAAqB,UAAA,EAAsB;IAE7D,MAAM,gBAAgB;QAAE,GAAG,UAAA;IAAW;IAEtC,IAAA,MAAW,YAAY,WAAY;QACjC,MAAM,gBAAgB,SAAA,CAAU,QAAQ,CAAA;QACxC,MAAM,iBAAiB,UAAA,CAAW,QAAQ,CAAA;QAE1C,MAAM,YAAY,WAAW,IAAA,CAAK,QAAQ;QAC1C,IAAI,WAAW;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,aAAA,CAAc,QAAQ,CAAA,GAAI,CAAA,GAAI,SAAoB;oBAChD,MAAM,SAAS,eAAe,GAAG,IAAI;oBACrC,cAAc,GAAG,IAAI;oBACrB,OAAO;gBACT;YACF,OAAA,IAES,eAAe;gBACtB,aAAA,CAAc,QAAQ,CAAA,GAAI;YAC5B;QACF,OAAA,IAES,aAAa,SAAS;YAC7B,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAE,GAAG,aAAA;gBAAe,GAAG,cAAA;YAAe;QAClE,OAAA,IAAW,aAAa,aAAa;YACnC,aAAA,CAAc,QAAQ,CAAA,GAAI;gBAAC;gBAAe,cAAc;aAAA,CAAE,MAAA,CAAO,OAAO,EAAE,IAAA,CAAK,GAAG;QACpF;IACF;IAEA,OAAO;QAAE,GAAG,SAAA;QAAW,GAAG,aAAA;IAAc;AAC1C;AAOA,SAAS,cAAc,OAAA,EAA6B;IAElD,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAQ,QAAQ,KAAA,CAAuC,GAAA;IACzD;IAGA,OAAQ,QAAQ,KAAA,CAAuC,GAAA,IAAQ,QAAgB,GAAA;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA4ChB;;;;;AA1CX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,4KAAO,aAAA,EAAW,CAAA,UAAA,EAAa,IAAI,EAAE;IAC3C,MAAM,iNAAa,aAAA,EAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;YAChC,MAAA,CAAe,OAAO,GAAA,CAAI,UAAU,CAAC,CAAA,GAAI;QAC5C;QAEA,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,CAAA,UAAA,EAAa,IAAI,EAAA;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,kNAAS,YAAA,EAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-progress/src/progress.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Progress\n * -----------------------------------------------------------------------------------------------*/\n\nconst PROGRESS_NAME = 'Progress';\nconst DEFAULT_MAX = 100;\n\ntype ScopedProps<P> = P & { __scopeProgress?: Scope };\nconst [createProgressContext, createProgressScope] = createContextScope(PROGRESS_NAME);\n\ntype ProgressState = 'indeterminate' | 'complete' | 'loading';\ntype ProgressContextValue = { value: number | null; max: number };\nconst [ProgressProvider, useProgressContext] =\n  createProgressContext<ProgressContextValue>(PROGRESS_NAME);\n\ntype ProgressElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ProgressProps extends PrimitiveDivProps {\n  value?: number | null | undefined;\n  max?: number;\n  getValueLabel?(value: number, max: number): string;\n}\n\nconst Progress = React.forwardRef<ProgressElement, ProgressProps>(\n  (props: ScopedProps<ProgressProps>, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, 'Progress'));\n    }\n\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, 'Progress'));\n    }\n\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : undefined;\n\n    return (\n      <ProgressProvider scope={__scopeProgress} value={value} max={max}>\n        <Primitive.div\n          aria-valuemax={max}\n          aria-valuemin={0}\n          aria-valuenow={isNumber(value) ? value : undefined}\n          aria-valuetext={valueLabel}\n          role=\"progressbar\"\n          data-state={getProgressState(value, max)}\n          data-value={value ?? undefined}\n          data-max={max}\n          {...progressProps}\n          ref={forwardedRef}\n        />\n      </ProgressProvider>\n    );\n  }\n);\n\nProgress.displayName = PROGRESS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * ProgressIndicator\n * -----------------------------------------------------------------------------------------------*/\n\nconst INDICATOR_NAME = 'ProgressIndicator';\n\ntype ProgressIndicatorElement = React.ComponentRef<typeof Primitive.div>;\ninterface ProgressIndicatorProps extends PrimitiveDivProps {}\n\nconst ProgressIndicator = React.forwardRef<ProgressIndicatorElement, ProgressIndicatorProps>(\n  (props: ScopedProps<ProgressIndicatorProps>, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return (\n      <Primitive.div\n        data-state={getProgressState(context.value, context.max)}\n        data-value={context.value ?? undefined}\n        data-max={context.max}\n        {...indicatorProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nProgressIndicator.displayName = INDICATOR_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction defaultGetValueLabel(value: number, max: number) {\n  return `${Math.round((value / max) * 100)}%`;\n}\n\nfunction getProgressState(value: number | undefined | null, maxValue: number): ProgressState {\n  return value == null ? 'indeterminate' : value === maxValue ? 'complete' : 'loading';\n}\n\nfunction isNumber(value: any): value is number {\n  return typeof value === 'number';\n}\n\nfunction isValidMaxNumber(max: any): max is number {\n  // prettier-ignore\n  return (\n    isNumber(max) &&\n    !isNaN(max) &&\n    max > 0\n  );\n}\n\nfunction isValidValueNumber(value: any, max: number): value is number {\n  // prettier-ignore\n  return (\n    isNumber(value) &&\n    !isNaN(value) &&\n    value <= max &&\n    value >= 0\n  );\n}\n\n// Split this out for clearer readability of the error message.\nfunction getInvalidMaxError(propValue: string, componentName: string) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\n\nfunction getInvalidValueError(propValue: string, componentName: string) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\n\nconst Root = Progress;\nconst Indicator = ProgressIndicator;\n\nexport {\n  createProgressScope,\n  //\n  Progress,\n  ProgressIndicator,\n  //\n  Root,\n  Indicator,\n};\nexport type { ProgressProps, ProgressIndicatorProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AAoDlB;;;;;;AA5CR,IAAM,gBAAgB;AACtB,IAAM,cAAc;AAGpB,IAAM,CAAC,uBAAuB,mBAAmB,CAAA,2KAAI,qBAAA,EAAmB,aAAa;AAIrF,IAAM,CAAC,kBAAkB,kBAAkB,CAAA,GACzC,sBAA4C,aAAa;AAU3D,IAAM,oNAAiB,cAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EACJ,eAAA,EACA,OAAO,YAAY,IAAA,EACnB,KAAK,OAAA,EACL,gBAAgB,oBAAA,EAChB,GAAG,eACL,GAAI;IAEJ,IAAA,CAAK,WAAW,YAAY,CAAA,KAAM,CAAC,iBAAiB,OAAO,GAAG;QAC5D,QAAQ,KAAA,CAAM,mBAAmB,GAAG,OAAO,EAAA,EAAI,UAAU,CAAC;IAC5D;IAEA,MAAM,MAAM,iBAAiB,OAAO,IAAI,UAAU;IAElD,IAAI,cAAc,QAAQ,CAAC,mBAAmB,WAAW,GAAG,GAAG;QAC7D,QAAQ,KAAA,CAAM,qBAAqB,GAAG,SAAS,EAAA,EAAI,UAAU,CAAC;IAChE;IAEA,MAAM,QAAQ,mBAAmB,WAAW,GAAG,IAAI,YAAY;IAC/D,MAAM,aAAa,SAAS,KAAK,IAAI,cAAc,OAAO,GAAG,IAAI,KAAA;IAEjE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kBAAA;QAAiB,OAAO;QAAiB;QAAc;QACtD,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,iBAAe;YACf,iBAAe;YACf,iBAAe,SAAS,KAAK,IAAI,QAAQ,KAAA;YACzC,kBAAgB;YAChB,MAAK;YACL,cAAY,iBAAiB,OAAO,GAAG;YACvC,cAAY,SAAS,KAAA;YACrB,YAAU;YACT,GAAG,aAAA;YACJ,KAAK;QAAA;IACP,CACF;AAEJ;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,iBAAiB;AAKvB,IAAM,8NAA0B,aAAA,EAC9B,CAAC,OAA4C,iBAAiB;IAC5D,MAAM,EAAE,eAAA,EAAiB,GAAG,eAAe,CAAA,GAAI;IAC/C,MAAM,UAAU,mBAAmB,gBAAgB,eAAe;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,cAAY,iBAAiB,QAAQ,KAAA,EAAO,QAAQ,GAAG;QACvD,cAAY,QAAQ,KAAA,IAAS,KAAA;QAC7B,YAAU,QAAQ,GAAA;QACjB,GAAG,cAAA;QACJ,KAAK;IAAA;AAGX;AAGF,kBAAkB,WAAA,GAAc;AAIhC,SAAS,qBAAqB,KAAA,EAAe,GAAA,EAAa;IACxD,OAAO,GAAG,KAAK,KAAA,CAAO,QAAQ,MAAO,GAAG,CAAC,CAAA,CAAA,CAAA;AAC3C;AAEA,SAAS,iBAAiB,KAAA,EAAkC,QAAA,EAAiC;IAC3F,OAAO,SAAS,OAAO,kBAAkB,UAAU,WAAW,aAAa;AAC7E;AAEA,SAAS,SAAS,KAAA,EAA6B;IAC7C,OAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,iBAAiB,GAAA,EAAyB;IAEjD,OACE,SAAS,GAAG,KACZ,CAAC,MAAM,GAAG,KACV,MAAM;AAEV;AAEA,SAAS,mBAAmB,KAAA,EAAY,GAAA,EAA8B;IAEpE,OACE,SAAS,KAAK,KACd,CAAC,MAAM,KAAK,KACZ,SAAS,OACT,SAAS;AAEb;AAGA,SAAS,mBAAmB,SAAA,EAAmB,aAAA,EAAuB;IACpE,OAAO,CAAA,gCAAA,EAAmC,SAAS,CAAA,iBAAA,EAAoB,aAAa,CAAA,sEAAA,EAAyE,WAAW,CAAA,GAAA,CAAA;AAC1K;AAEA,SAAS,qBAAqB,SAAA,EAAmB,aAAA,EAAuB;IACtE,OAAO,CAAA,kCAAA,EAAqC,SAAS,CAAA,iBAAA,EAAoB,aAAa,CAAA;;8CAAA,EAExC,WAAW,CAAA;;;uBAAA,CAAA;AAI3D;AAEA,IAAM,OAAO;AACb,IAAM,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/primitive/src/primitive.tsx"], "sourcesContent": ["function composeEventHandlers<E extends { defaultPrevented: boolean }>(\n  originalEventHandler?: (event: E) => void,\n  ourEventHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) {\n  return function handleEvent(event: E) {\n    originalEventHandler?.(event);\n\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\nexport { composeEventHandlers };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,qBACP,oBAAA,EACA,eAAA,EACA,EAAE,2BAA2B,IAAA,CAAK,CAAA,GAAI,CAAC,CAAA,EACvC;IACA,OAAO,SAAS,YAAY,KAAA,EAAU;QACpC,uBAAuB,KAAK;QAE5B,IAAI,6BAA6B,SAAS,CAAC,MAAM,gBAAA,EAAkB;YACjE,OAAO,kBAAkB,KAAK;QAChC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-collection/src/collection-legacy.tsx", "file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-collection/src/collection.tsx", "file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-collection/src/ordered-dictionary.ts"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\n// We have resorted to returning slots directly rather than exposing primitives that can then\n// be slotted like `<CollectionItem as={Slot}>…</CollectionItem>`.\n// This is because we encountered issues with generic types that cannot be statically analysed\n// due to creating them dynamically via createCollection.\n\nfunction createCollection<ItemElement extends HTMLElement, ItemData = {}>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionRef: React.RefObject<CollectionElement | null>;\n    itemMap: Map<\n      React.RefObject<ItemElement | null>,\n      { ref: React.RefObject<ItemElement | null> } & ItemData\n    >;\n  };\n\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: new Map() }\n  );\n\n  const CollectionProvider: React.FC<{ children?: React.ReactNode; scope: any }> = (props) => {\n    const { scope, children } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const itemMap = React.useRef<ContextValue['itemMap']>(new Map()).current;\n    return (\n      <CollectionProviderImpl scope={scope} itemMap={itemMap} collectionRef={ref}>\n        {children}\n      </CollectionProviderImpl>\n    );\n  };\n\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      React.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...(itemData as unknown as ItemData) });\n        return () => void context.itemMap.delete(ref);\n      });\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const context = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    const getItems = React.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current!) - orderedNodes.indexOf(b.ref.current!)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n\n    return getItems;\n  }\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n", "import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createSlot, type Slot } from '@radix-ui/react-slot';\nimport type { EntryOf } from './ordered-dictionary';\nimport { OrderedDict } from './ordered-dictionary';\n\ntype SlotProps = React.ComponentPropsWithoutRef<typeof Slot>;\ntype CollectionElement = HTMLElement;\ninterface CollectionProps extends SlotProps {\n  scope: any;\n}\n\ninterface BaseItemData {\n  id?: string;\n}\n\ntype ItemDataWithElement<\n  ItemData extends BaseItemData,\n  ItemElement extends HTMLElement,\n> = ItemData & {\n  element: ItemElement;\n};\n\ntype ItemMap<ItemElement extends HTMLElement, ItemData extends BaseItemData> = OrderedDict<\n  ItemElement,\n  ItemDataWithElement<ItemData, ItemElement>\n>;\n\nfunction createCollection<\n  ItemElement extends HTMLElement,\n  ItemData extends BaseItemData = BaseItemData,\n>(name: string) {\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionProvider\n   * ---------------------------------------------------------------------------------------------*/\n\n  const PROVIDER_NAME = name + 'CollectionProvider';\n  const [createCollectionContext, createCollectionScope] = createContextScope(PROVIDER_NAME);\n\n  type ContextValue = {\n    collectionElement: CollectionElement | null;\n    collectionRef: React.Ref<CollectionElement | null>;\n    collectionRefObject: React.RefObject<CollectionElement | null>;\n    itemMap: ItemMap<ItemElement, ItemData>;\n    setItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>;\n  };\n\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext<ContextValue>(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0,\n    }\n  );\n\n  type CollectionState = [\n    ItemMap: ItemMap<ItemElement, ItemData>,\n    SetItemMap: React.Dispatch<React.SetStateAction<ItemMap<ItemElement, ItemData>>>,\n  ];\n\n  const CollectionProvider: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state?: CollectionState;\n  }> = ({ state, ...props }) => {\n    return state ? (\n      <CollectionProviderImpl {...props} state={state} />\n    ) : (\n      <CollectionInit {...props} />\n    );\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n\n  const CollectionInit: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n  }> = (props) => {\n    const state = useInitCollection();\n    return <CollectionProviderImpl {...props} state={state} />;\n  };\n  CollectionInit.displayName = PROVIDER_NAME + 'Init';\n\n  const CollectionProviderImpl: React.FC<{\n    children?: React.ReactNode;\n    scope: any;\n    state: CollectionState;\n  }> = (props) => {\n    const { scope, children, state } = props;\n    const ref = React.useRef<CollectionElement>(null);\n    const [collectionElement, setCollectionElement] = React.useState<CollectionElement | null>(\n      null\n    );\n    const composeRefs = useComposedRefs(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n\n    React.useEffect(() => {\n      if (!collectionElement) return;\n\n      const observer = getChildListObserver(() => {\n        // setItemMap((map) => {\n        //   const copy = new OrderedDict(map).toSorted(([, a], [, b]) =>\n        //     !a.element || !b.element ? 0 : isElementPreceding(a.element, b.element) ? -1 : 1\n        //   );\n        //   // check if the order has changed\n        //   let index = -1;\n        //   for (const entry of copy) {\n        //     index++;\n        //     const key = map.keyAt(index)!;\n        //     const [copyKey] = entry;\n        //     if (key !== copyKey) {\n        //       // order has changed!\n        //       return copy;\n        //     }\n        //   }\n        //   return map;\n        // });\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true,\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n\n    return (\n      <CollectionContextProvider\n        scope={scope}\n        itemMap={itemMap}\n        setItemMap={setItemMap}\n        collectionRef={composeRefs}\n        collectionRefObject={ref}\n        collectionElement={collectionElement}\n      >\n        {children}\n      </CollectionContextProvider>\n    );\n  };\n\n  CollectionProviderImpl.displayName = PROVIDER_NAME + 'Impl';\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionSlot\n   * ---------------------------------------------------------------------------------------------*/\n\n  const COLLECTION_SLOT_NAME = name + 'CollectionSlot';\n\n  const CollectionSlotImpl = createSlot(COLLECTION_SLOT_NAME);\n  const CollectionSlot = React.forwardRef<CollectionElement, CollectionProps>(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = useComposedRefs(forwardedRef, context.collectionRef);\n      return <CollectionSlotImpl ref={composedRefs}>{children}</CollectionSlotImpl>;\n    }\n  );\n\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * CollectionItem\n   * ---------------------------------------------------------------------------------------------*/\n\n  const ITEM_SLOT_NAME = name + 'CollectionItemSlot';\n  const ITEM_DATA_ATTR = 'data-radix-collection-item';\n\n  type CollectionItemSlotProps = ItemData & {\n    children: React.ReactNode;\n    scope: any;\n  };\n\n  const CollectionItemSlotImpl = createSlot(ITEM_SLOT_NAME);\n  const CollectionItemSlot = React.forwardRef<ItemElement, CollectionItemSlotProps>(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = React.useRef<ItemElement>(null);\n      const [element, setElement] = React.useState<ItemElement | null>(null);\n      const composedRefs = useComposedRefs(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n\n      const { setItemMap } = context;\n\n      const itemDataRef = React.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n\n      React.useEffect(() => {\n        const itemData = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n\n          if (!map.has(element)) {\n            map.set(element, { ...(itemData as unknown as ItemData), element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n\n          return map\n            .set(element, { ...(itemData as unknown as ItemData), element })\n            .toSorted(sortByDocumentPosition);\n        });\n\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n\n      return (\n        <CollectionItemSlotImpl {...{ [ITEM_DATA_ATTR]: '' }} ref={composedRefs as any}>\n          {children}\n        </CollectionItemSlotImpl>\n      );\n    }\n  );\n\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n\n  /* -----------------------------------------------------------------------------------------------\n   * useInitCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useInitCollection() {\n    return React.useState<ItemMap<ItemElement, ItemData>>(new OrderedDict());\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * useCollection\n   * ---------------------------------------------------------------------------------------------*/\n\n  function useCollection(scope: any) {\n    const { itemMap } = useCollectionContext(name + 'CollectionConsumer', scope);\n\n    return itemMap;\n  }\n\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection,\n  };\n\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions,\n  ] as const;\n}\n\nexport { createCollection };\nexport type { CollectionProps };\n\nfunction shallowEqual(a: any, b: any) {\n  if (a === b) return true;\n  if (typeof a !== 'object' || typeof b !== 'object') return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\n\nfunction isElementPreceding(a: Element, b: Element) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\n\nfunction sortByDocumentPosition<E extends HTMLElement, T extends BaseItemData>(\n  a: EntryOf<ItemMap<E, T>>,\n  b: EntryOf<ItemMap<E, T>>\n) {\n  return !a[1].element || !b[1].element\n    ? 0\n    : isElementPreceding(a[1].element, b[1].element)\n      ? -1\n      : 1;\n}\n\nfunction getChildListObserver(callback: () => void) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === 'childList') {\n        callback();\n        return;\n      }\n    }\n  });\n\n  return observer;\n}\n", "// Not a real member because it shouldn't be accessible, but the super class\n// calls `set` which needs to read the instanciation state, so it can't be a\n// private member.\nconst __instanciated = new WeakMap<OrderedDict<any, any>, boolean>();\nexport class OrderedDict<K, V> extends Map<K, V> {\n  #keys: K[];\n\n  constructor(iterable?: Iterable<readonly [K, V]> | null | undefined);\n  constructor(entries?: readonly (readonly [K, V])[] | null) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n\n  set(key: K, value: V) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n\n  insert(index: number, key: K, value: V) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n\n    if (safeIndex === this.size || (has && safeIndex === this.size - 1) || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n\n    const size = this.size + (has ? 0 : 1);\n\n    // If you insert at, say, -2, without this bit you'd replace the\n    // second-to-last item and push the rest up one, which means the new item is\n    // 3rd to last. This isn't very intuitive; inserting at -2 is more like\n    // saying \"make this item the second to last\".\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n\n    const keys = [...this.#keys];\n    let nextValue: V | undefined;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i]!;\n        if (keys[i] === key) {\n          nextKey = keys[i + 1]!;\n        }\n        if (has) {\n          // delete first to ensure that the item is moved to the end\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1]!;\n        const currentValue = nextValue!;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n\n  with(index: number, key: K, value: V) {\n    const copy = new OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n\n  before(key: K) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n\n  after(key: K) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return undefined;\n    }\n    return this.entryAt(index);\n  }\n\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key: K, newKey: K, value: V) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n\n  first() {\n    return this.entryAt(0);\n  }\n\n  last() {\n    return this.entryAt(-1);\n  }\n\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n\n  delete(key: K) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n\n  deleteAt(index: number) {\n    const key = this.keyAt(index);\n    if (key !== undefined) {\n      return this.delete(key);\n    }\n    return false;\n  }\n\n  at(index: number) {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return this.get(key);\n    }\n  }\n\n  entryAt(index: number): [K, V] | undefined {\n    const key = at(this.#keys, index);\n    if (key !== undefined) {\n      return [key, this.get(key)!];\n    }\n  }\n\n  indexOf(key: K) {\n    return this.#keys.indexOf(key);\n  }\n\n  keyAt(index: number) {\n    return at(this.#keys, index);\n  }\n\n  from(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n\n  keyFrom(key: K, offset: number) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return undefined;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n\n  find(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return undefined;\n  }\n\n  findIndex(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => boolean,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n\n  filter<KK extends K, VV extends V>(\n    predicate: (entry: [K, V], index: number, dict: OrderedDict<K, V>) => entry is [KK, VV],\n    thisArg?: any\n  ): OrderedDict<KK, VV>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ): OrderedDict<K, V>;\n\n  filter(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    const entries: Array<[K, V]> = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  map<U>(\n    callbackfn: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => U,\n    thisArg?: any\n  ): OrderedDict<K, U> {\n    const entries: [K, U][] = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new OrderedDict(entries);\n  }\n\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduce(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduce<U>(\n    callbackfn: (\n      previousValue: U,\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduce<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0)!;\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V]\n  ): [K, V];\n  reduceRight(\n    callbackfn: (\n      previousValue: [K, V],\n      currentEntry: [K, V],\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => [K, V],\n    initialValue: [K, V]\n  ): [K, V];\n  reduceRight<U>(\n    callbackfn: (\n      previousValue: [K, V],\n      currentValue: U,\n      currentIndex: number,\n      dictionary: OrderedDict<K, V>\n    ) => U,\n    initialValue: U\n  ): U;\n\n  reduceRight<U>(\n    ...args: [\n      (\n        previousValue: U,\n        currentEntry: [K, V],\n        currentIndex: number,\n        dictionary: OrderedDict<K, V>\n      ) => U,\n      U?,\n    ]\n  ) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1)!;\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index)!;\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry as any;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n\n  toSorted(compareFn?: (a: [K, V], b: [K, V]) => number): OrderedDict<K, V> {\n    const entries = [...this.entries()].sort(compareFn);\n    return new OrderedDict(entries);\n  }\n\n  toReversed(): OrderedDict<K, V> {\n    const reversed = new OrderedDict<K, V>();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n\n  toSpliced(start: number, deleteCount?: number): OrderedDict<K, V>;\n  toSpliced(start: number, deleteCount: number, ...items: [K, V][]): OrderedDict<K, V>;\n\n  toSpliced(...args: [start: number, deleteCount: number, ...items: [K, V][]]) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new OrderedDict(entries);\n  }\n\n  slice(start?: number, end?: number) {\n    const result = new OrderedDict<K, V>();\n    let stop = this.size - 1;\n\n    if (start === undefined) {\n      return result;\n    }\n\n    if (start < 0) {\n      start = start + this.size;\n    }\n\n    if (end !== undefined && end > 0) {\n      stop = end - 1;\n    }\n\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index)!;\n      const element = this.get(key)!;\n      result.set(key, element);\n    }\n    return result;\n  }\n\n  every(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n\n  some(\n    predicate: (entry: [K, V], index: number, dictionary: OrderedDict<K, V>) => unknown,\n    thisArg?: any\n  ) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n}\n\nexport type KeyOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<infer K, any> ? K : never;\nexport type ValueOf<D extends OrderedDict<any, any>> =\n  D extends OrderedDict<any, infer V> ? V : never;\nexport type EntryOf<D extends OrderedDict<any, any>> = [KeyOf<D>, ValueOf<D>];\nexport type KeyFrom<E extends EntryOf<any>> = E[0];\nexport type ValueFrom<E extends EntryOf<any>> = E[1];\n\nfunction at<T>(array: ArrayLike<T>, index: number): T | undefined {\n  if ('at' in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? undefined : array[actualIndex];\n}\n\nfunction toSafeIndex(array: ArrayLike<any>, index: number) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\n\nfunction toSafeInteger(number: number) {\n  // eslint-disable-next-line no-self-compare\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n"], "names": ["React", "createContextScope", "useComposedRefs", "createSlot", "jsx", "createCollection", "createContextScope", "React", "useComposedRefs", "createSlot", "itemData"], "mappings": ";;;;;AAAA,OAAO,WAAW;AAClB,SAAS,0BAA0B;AACnC,SAAS,uBAAuB;AAChC,SAAS,kBAA6B;AAuChC;;;;;;;AA1BN,SAAS,iBAAiE,IAAA,EAAc;IAKtF,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,2KAAI,qBAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,wBAAwB,oBAAoB,CAAA,GAAI,wBACrD,eACA;QAAE,eAAe;YAAE,SAAS;QAAK;QAAG,SAAS,aAAA,GAAA,IAAI,IAAI;IAAE;IAGzD,MAAM,qBAA2E,CAAC,UAAU;QAC1F,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,4MAAM,UAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,gNAAU,UAAA,CAAM,MAAA,CAAgC,aAAA,GAAA,IAAI,IAAI,CAAC,EAAE,OAAA;QACjE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;YAAuB;YAAc;YAAkB,eAAe;YACpE;QAAA,CACH;IAEJ;IAEA,mBAAmB,WAAA,GAAc;IAMjC,MAAM,uBAAuB,OAAO;IAEpC,MAAM,0LAAqB,aAAA,EAAW,oBAAoB;IAC1D,MAAM,uNAAiB,UAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,+LAAe,kBAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,8LAAyB,aAAA,EAAW,cAAc;IACxD,MAAM,2NAAqB,UAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,4MAAM,UAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,+LAAe,kBAAA,EAAgB,cAAc,GAAG;QACtD,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,qMAAA,CAAA,UAAA,CAAM,SAAA,CAAU,MAAM;YACpB,QAAQ,OAAA,CAAQ,GAAA,CAAI,KAAK;gBAAE;gBAAK,GAAI,QAAA;YAAiC,CAAC;YACtE,OAAO,IAAM,KAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,GAAG;QAC9C,CAAC;QAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,UAAU,qBAAqB,OAAO,sBAAsB,KAAK;QAEvE,MAAM,iNAAW,UAAA,CAAM,WAAA,CAAY,MAAM;YACvC,MAAM,iBAAiB,QAAQ,aAAA,CAAc,OAAA;YAC7C,IAAI,CAAC,eAAgB,CAAA,OAAO,CAAC,CAAA;YAC7B,MAAM,eAAe,MAAM,IAAA,CAAK,eAAe,gBAAA,CAAiB,CAAA,CAAA,EAAI,cAAc,CAAA,CAAA,CAAG,CAAC;YACtF,MAAM,QAAQ,MAAM,IAAA,CAAK,QAAQ,OAAA,CAAQ,MAAA,CAAO,CAAC;YACjD,MAAM,eAAe,MAAM,IAAA,CACzB,CAAC,GAAG,IAAM,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ,IAAI,aAAa,OAAA,CAAQ,EAAE,GAAA,CAAI,OAAQ;YAEtF,OAAO;QACT,GAAG;YAAC,QAAQ,aAAA;YAAe,QAAQ,OAAO;SAAC;QAE3C,OAAO;IACT;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;QACA;KACF;AACF;;;;;;AE9HA,IAAM,iBAAiB,aAAA,GAAA,IAAI,QAAwC;AAC5D,IAAM,cAAN,MAAM,qBAA0B,IAAU;KAC/C,IAAA,CAAA;IAGA,YAAY,OAAA,CAA+C;QACzD,KAAA,CAAM,OAAO;QACb,IAAA,EAAK,IAAA,GAAQ,CAAC;eAAG,KAAA,CAAM,KAAK,CAAC;SAAA;QAC7B,eAAe,GAAA,CAAI,IAAA,EAAM,IAAI;IAC/B;IAEA,IAAI,GAAA,EAAQ,KAAA,EAAU;QACpB,IAAI,eAAe,GAAA,CAAI,IAAI,GAAG;YAC5B,IAAI,IAAA,CAAK,GAAA,CAAI,GAAG,GAAG;gBACjB,IAAA,EAAK,IAAA,CAAM,IAAA,CAAK,KAAA,CAAM,OAAA,CAAQ,GAAG,CAAC,CAAA,GAAI;YACxC,OAAO;gBACL,IAAA,EAAK,IAAA,CAAM,IAAA,CAAK,GAAG;YACrB;QACF;QACA,KAAA,CAAM,IAAI,KAAK,KAAK;QACpB,OAAO,IAAA;IACT;IAEA,OAAO,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACtC,MAAM,MAAM,IAAA,CAAK,GAAA,CAAI,GAAG;QACxB,MAAM,SAAS,IAAA,EAAK,IAAA,CAAM,MAAA;QAC1B,MAAM,gBAAgB,cAAc,KAAK;QACzC,IAAI,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;QAChE,MAAM,YAAY,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;QAElE,IAAI,cAAc,IAAA,CAAK,IAAA,IAAS,OAAO,cAAc,IAAA,CAAK,IAAA,GAAO,KAAM,cAAc,CAAA,GAAI;YACvF,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACnB,OAAO,IAAA;QACT;QAEA,MAAM,OAAO,IAAA,CAAK,IAAA,GAAA,CAAQ,MAAM,IAAI,CAAA;QAMpC,IAAI,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAM,OAAO,CAAC;eAAG,IAAA,CAAK,KAAK;SAAA;QAC3B,IAAI;QACJ,IAAI,aAAa;QACjB,IAAA,IAAS,IAAI,aAAa,IAAI,MAAM,IAAK;YACvC,IAAI,gBAAgB,GAAG;gBACrB,IAAI,UAAU,IAAA,CAAK,CAAC,CAAA;gBACpB,IAAI,IAAA,CAAK,CAAC,CAAA,KAAM,KAAK;oBACnB,UAAU,IAAA,CAAK,IAAI,CAAC,CAAA;gBACtB;gBACA,IAAI,KAAK;oBAEP,IAAA,CAAK,MAAA,CAAO,GAAG;gBACjB;gBACA,YAAY,IAAA,CAAK,GAAA,CAAI,OAAO;gBAC5B,IAAA,CAAK,GAAA,CAAI,KAAK,KAAK;YACrB,OAAO;gBACL,IAAI,CAAC,cAAc,IAAA,CAAK,IAAI,CAAC,CAAA,KAAM,KAAK;oBACtC,aAAa;gBACf;gBACA,MAAM,aAAa,IAAA,CAAK,aAAa,IAAI,IAAI,CAAC,CAAA;gBAC9C,MAAM,eAAe;gBACrB,YAAY,IAAA,CAAK,GAAA,CAAI,UAAU;gBAC/B,IAAA,CAAK,MAAA,CAAO,UAAU;gBACtB,IAAA,CAAK,GAAA,CAAI,YAAY,YAAY;YACnC;QACF;QACA,OAAO,IAAA;IACT;IAEA,KAAK,KAAA,EAAe,GAAA,EAAQ,KAAA,EAAU;QACpC,MAAM,OAAO,IAAI,aAAY,IAAI;QACjC,KAAK,MAAA,CAAO,OAAO,KAAK,KAAK;QAC7B,OAAO;IACT;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG,IAAI;QACxC,IAAI,QAAQ,GAAG;YACb,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,UAAU,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACrC,MAAM,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,OAAO,QAAQ,KAAK;IACzC;IAEA,MAAM,GAAA,EAAQ;QACZ,IAAI,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;QAClC,QAAQ,UAAU,CAAA,KAAM,UAAU,IAAA,CAAK,IAAA,GAAO,IAAI,CAAA,IAAK,QAAQ;QAC/D,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,OAAO,IAAA,CAAK,OAAA,CAAQ,KAAK;IAC3B;IAAA;;GAAA,GAKA,SAAS,GAAA,EAAQ,MAAA,EAAW,KAAA,EAAU;QACpC,MAAM,QAAQ,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;QACpC,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,IAAA;QACT;QACA,OAAO,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG,QAAQ,KAAK;IAC7C;IAEA,QAAQ;QACN,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAC;IACvB;IAEA,OAAO;QACL,OAAO,IAAA,CAAK,OAAA,CAAQ,CAAA,CAAE;IACxB;IAEA,QAAQ;QACN,IAAA,EAAK,IAAA,GAAQ,CAAC,CAAA;QACd,OAAO,KAAA,CAAM,MAAM;IACrB;IAEA,OAAO,GAAA,EAAQ;QACb,MAAM,UAAU,KAAA,CAAM,OAAO,GAAG;QAChC,IAAI,SAAS;YACX,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG,GAAG,CAAC;QAC9C;QACA,OAAO;IACT;IAEA,SAAS,KAAA,EAAe;QACtB,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;QAC5B,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,MAAA,CAAO,GAAG;QACxB;QACA,OAAO;IACT;IAEA,GAAG,KAAA,EAAe;QAChB,MAAM,MAAM,GAAG,IAAA,EAAK,IAAA,EAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO,IAAA,CAAK,GAAA,CAAI,GAAG;QACrB;IACF;IAEA,QAAQ,KAAA,EAAmC;QACzC,MAAM,MAAM,GAAG,IAAA,CAAK,KAAA,EAAO,KAAK;QAChC,IAAI,QAAQ,KAAA,GAAW;YACrB,OAAO;gBAAC;gBAAK,IAAA,CAAK,GAAA,CAAI,GAAG,CAAE;aAAA;QAC7B;IACF;IAEA,QAAQ,GAAA,EAAQ;QACd,OAAO,IAAA,EAAK,IAAA,CAAM,OAAA,CAAQ,GAAG;IAC/B;IAEA,MAAM,KAAA,EAAe;QACnB,OAAO,GAAG,IAAA,EAAK,IAAA,EAAO,KAAK;IAC7B;IAEA,KAAK,GAAA,EAAQ,MAAA,EAAgB;QAC3B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,EAAA,CAAG,IAAI;IACrB;IAEA,QAAQ,GAAA,EAAQ,MAAA,EAAgB;QAC9B,MAAM,QAAQ,IAAA,CAAK,OAAA,CAAQ,GAAG;QAC9B,IAAI,UAAU,CAAA,GAAI;YAChB,OAAO,KAAA;QACT;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,EAAG,CAAA,OAAO;QACrB,IAAI,QAAQ,IAAA,CAAK,IAAA,CAAM,CAAA,OAAO,IAAA,CAAK,IAAA,GAAO;QAC1C,OAAO,IAAA,CAAK,KAAA,CAAM,IAAI;IACxB;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,KAAA;IACT;IAEA,UACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO,CAAA;IACT;IAYA,OACE,SAAA,EACA,OAAA,EACA;QACA,MAAM,UAAyB,CAAC,CAAA;QAChC,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,QAAQ,IAAA,CAAK,KAAK;YACpB;YACA;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,IACE,UAAA,EACA,OAAA,EACmB;QACnB,MAAM,UAAoB,CAAC,CAAA;QAC3B,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,QAAQ,IAAA,CAAK;gBAAC,KAAA,CAAM,CAAC,CAAA;gBAAG,QAAQ,KAAA,CAAM,YAAY,SAAS;oBAAC;oBAAO;oBAAO,IAAI;iBAAC,CAAC;aAAC;YACjF;QACF;QACA,OAAO,IAAI,aAAY,OAAO;IAChC;IA6BA,OAAA,GACK,IAAA,EASH;QACA,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,QAAQ;QACZ,IAAI,cAAc,gBAAgB,IAAA,CAAK,EAAA,CAAG,CAAC;QAC3C,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,UAAU,KAAK,KAAK,MAAA,KAAW,GAAG;gBACpC,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;YACA;QACF;QACA,OAAO;IACT;IA6BA,YAAA,GACK,IAAA,EASH;QACA,MAAM,CAAC,YAAY,YAAY,CAAA,GAAI;QACnC,IAAI,cAAc,gBAAgB,IAAA,CAAK,EAAA,CAAG,CAAA,CAAE;QAC5C,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,QAAQ,IAAA,CAAK,EAAA,CAAG,KAAK;YAC3B,IAAI,UAAU,IAAA,CAAK,IAAA,GAAO,KAAK,KAAK,MAAA,KAAW,GAAG;gBAChD,cAAc;YAChB,OAAO;gBACL,cAAc,QAAQ,KAAA,CAAM,YAAY,IAAA,EAAM;oBAAC;oBAAa;oBAAO;oBAAO,IAAI;iBAAC;YACjF;QACF;QACA,OAAO;IACT;IAEA,SAAS,SAAA,EAAiE;QACxE,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA,CAAE,IAAA,CAAK,SAAS;QAClD,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,aAAgC;QAC9B,MAAM,WAAW,IAAI,aAAkB;QACvC,IAAA,IAAS,QAAQ,IAAA,CAAK,IAAA,GAAO,GAAG,SAAS,GAAG,QAAS;YACnD,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,SAAS,GAAA,CAAI,KAAK,OAAO;QAC3B;QACA,OAAO;IACT;IAKA,UAAA,GAAa,IAAA,EAAgE;QAC3E,MAAM,UAAU,CAAC;eAAG,IAAA,CAAK,OAAA,CAAQ,CAAC;SAAA;QAClC,QAAQ,MAAA,CAAO,GAAG,IAAI;QACtB,OAAO,IAAI,aAAY,OAAO;IAChC;IAEA,MAAM,KAAA,EAAgB,GAAA,EAAc;QAClC,MAAM,SAAS,IAAI,aAAkB;QACrC,IAAI,OAAO,IAAA,CAAK,IAAA,GAAO;QAEvB,IAAI,UAAU,KAAA,GAAW;YACvB,OAAO;QACT;QAEA,IAAI,QAAQ,GAAG;YACb,QAAQ,QAAQ,IAAA,CAAK,IAAA;QACvB;QAEA,IAAI,QAAQ,KAAA,KAAa,MAAM,GAAG;YAChC,OAAO,MAAM;QACf;QAEA,IAAA,IAAS,QAAQ,OAAO,SAAS,MAAM,QAAS;YAC9C,MAAM,MAAM,IAAA,CAAK,KAAA,CAAM,KAAK;YAC5B,MAAM,UAAU,IAAA,CAAK,GAAA,CAAI,GAAG;YAC5B,OAAO,GAAA,CAAI,KAAK,OAAO;QACzB;QACA,OAAO;IACT;IAEA,MACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,CAAC,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC5D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;IAEA,KACE,SAAA,EACA,OAAA,EACA;QACA,IAAI,QAAQ;QACZ,KAAA,MAAW,SAAS,IAAA,CAAM;YACxB,IAAI,QAAQ,KAAA,CAAM,WAAW,SAAS;gBAAC;gBAAO;gBAAO,IAAI;aAAC,GAAG;gBAC3D,OAAO;YACT;YACA;QACF;QACA,OAAO;IACT;AACF;AAUA,SAAS,GAAM,KAAA,EAAqB,KAAA,EAA8B;IAChE,IAAI,QAAQ,MAAM,SAAA,EAAW;QAC3B,OAAO,MAAM,SAAA,CAAU,EAAA,CAAG,IAAA,CAAK,OAAO,KAAK;IAC7C;IACA,MAAM,cAAc,YAAY,OAAO,KAAK;IAC5C,OAAO,gBAAgB,CAAA,IAAK,KAAA,IAAY,KAAA,CAAM,WAAW,CAAA;AAC3D;AAEA,SAAS,YAAY,KAAA,EAAuB,KAAA,EAAe;IACzD,MAAM,SAAS,MAAM,MAAA;IACrB,MAAM,gBAAgB,cAAc,KAAK;IACzC,MAAM,cAAc,iBAAiB,IAAI,gBAAgB,SAAS;IAClE,OAAO,cAAc,KAAK,eAAe,SAAS,CAAA,IAAK;AACzD;AAEA,SAAS,cAAc,MAAA,EAAgB;IAErC,OAAO,WAAW,UAAU,WAAW,IAAI,IAAI,KAAK,KAAA,CAAM,MAAM;AAClE;;ADtbA,SAASK,kBAGP,IAAA,EAAc;IAKd,MAAM,gBAAgB,OAAO;IAC7B,MAAM,CAAC,yBAAyB,qBAAqB,CAAA,IAAIC,4LAAAA,EAAmB,aAAa;IAUzF,MAAM,CAAC,2BAA2B,oBAAoB,CAAA,GAAI,wBACxD,eACA;QACE,mBAAmB;QACnB,eAAe;YAAE,SAAS;QAAK;QAC/B,qBAAqB;YAAE,SAAS;QAAK;QACrC,SAAS,IAAI,YAAY;QACzB,YAAY,IAAM,KAAA;IACpB;IAQF,MAAM,qBAID,CAAC,EAAE,KAAA,EAAO,GAAG,MAAM,CAAA,KAAM;QAC5B,OAAO,QACL,aAAA,OAAAF,8NAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc,IAEjD,aAAA,GAAAA,kOAAAA,EAAC,gBAAA;YAAgB,GAAG,KAAA;QAAA,CAAO;IAE/B;IACA,mBAAmB,WAAA,GAAc;IAEjC,MAAM,iBAGD,CAAC,UAAU;QACd,MAAM,QAAQ,kBAAkB;QAChC,OAAO,aAAA,+NAAAA,MAAAA,EAAC,wBAAA;YAAwB,GAAG,KAAA;YAAO;QAAA,CAAc;IAC1D;IACA,eAAe,WAAA,GAAc,gBAAgB;IAE7C,MAAM,yBAID,CAAC,UAAU;QACd,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,KAAA,CAAM,CAAA,GAAI;QACnC,MAAM,4MAAMG,UAAAA,CAAM,MAAA,CAA0B,IAAI;QAChD,MAAM,CAAC,mBAAmB,oBAAoB,CAAA,yMAAIA,UAAAA,CAAM,QAAA,CACtD;QAEF,MAAM,eAAcC,iMAAAA,EAAgB,KAAK,oBAAoB;QAC7D,MAAM,CAAC,SAAS,UAAU,CAAA,GAAI;8MAE9BD,UAAAA,CAAM,SAAA,CAAU,MAAM;YACpB,IAAI,CAAC,kBAAmB,CAAA;YAExB,MAAM,WAAW,qBAAqB,KAkBtC,CAlB4C,AAkB3C;YACD,SAAS,OAAA,CAAQ,mBAAmB;gBAClC,WAAW;gBACX,SAAS;YACX,CAAC;YACD,OAAO,MAAM;gBACX,SAAS,UAAA,CAAW;YACtB;QACF,GAAG;YAAC,iBAAiB;SAAC;QAEtB,OACE,aAAA,+NAAAH,MAAAA,EAAC,2BAAA;YACC;YACA;YACA;YACA,eAAe;YACf,qBAAqB;YACrB;YAEC;QAAA;IAGP;IAEA,uBAAuB,WAAA,GAAc,gBAAgB;IAMrD,MAAM,uBAAuB,OAAO;IAEpC,MAAM,0LAAqBK,aAAAA,EAAW,oBAAoB;IAC1D,MAAM,uNAAiBF,UAAAA,CAAM,UAAA,CAC3B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAI;QAC5B,MAAM,UAAU,qBAAqB,sBAAsB,KAAK;QAChE,MAAM,gBAAeC,iMAAAA,EAAgB,cAAc,QAAQ,aAAa;QACxE,OAAO,aAAA,+NAAAJ,MAAAA,EAAC,oBAAA;YAAmB,KAAK;YAAe;QAAA,CAAS;IAC1D;IAGF,eAAe,WAAA,GAAc;IAM7B,MAAM,iBAAiB,OAAO;IAC9B,MAAM,iBAAiB;IAOvB,MAAM,8LAAyBK,aAAAA,EAAW,cAAc;IACxD,MAAM,qBAAqBF,gNAAAA,CAAM,UAAA,CAC/B,CAAC,OAAO,iBAAiB;QACvB,MAAM,EAAE,KAAA,EAAO,QAAA,EAAU,GAAG,SAAS,CAAA,GAAI;QACzC,MAAM,4MAAMA,UAAAA,CAAM,MAAA,CAAoB,IAAI;QAC1C,MAAM,CAAC,SAAS,UAAU,CAAA,wMAAIA,WAAAA,CAAM,QAAA,CAA6B,IAAI;QACrE,MAAM,+LAAeC,kBAAAA,EAAgB,cAAc,KAAK,UAAU;QAClE,MAAM,UAAU,qBAAqB,gBAAgB,KAAK;QAE1D,MAAM,EAAE,UAAA,CAAW,CAAA,GAAI;QAEvB,MAAM,cAAcD,gNAAAA,CAAM,MAAA,CAAO,QAAQ;QACzC,IAAI,CAAC,aAAa,YAAY,OAAA,EAAS,QAAQ,GAAG;YAChD,YAAY,OAAA,GAAU;QACxB;QACA,MAAM,mBAAmB,YAAY,OAAA;8MAErCA,UAAAA,CAAM,SAAA,CAAU,MAAM;YACpB,MAAMG,YAAW;YACjB,WAAW,CAAC,QAAQ;gBAClB,IAAI,CAAC,SAAS;oBACZ,OAAO;gBACT;gBAEA,IAAI,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;oBACrB,IAAI,GAAA,CAAI,SAAS;wBAAE,GAAIA,SAAAA;wBAAkC;oBAAQ,CAAC;oBAClE,OAAO,IAAI,QAAA,CAAS,sBAAsB;gBAC5C;gBAEA,OAAO,IACJ,GAAA,CAAI,SAAS;oBAAE,GAAIA,SAAAA;oBAAkC;gBAAQ,CAAC,EAC9D,QAAA,CAAS,sBAAsB;YACpC,CAAC;YAED,OAAO,MAAM;gBACX,WAAW,CAAC,QAAQ;oBAClB,IAAI,CAAC,WAAW,CAAC,IAAI,GAAA,CAAI,OAAO,GAAG;wBACjC,OAAO;oBACT;oBACA,IAAI,MAAA,CAAO,OAAO;oBAClB,OAAO,IAAI,YAAY,GAAG;gBAC5B,CAAC;YACH;QACF,GAAG;YAAC;YAAS;YAAkB,UAAU;SAAC;QAE1C,OACE,aAAA,GAAAN,kOAAAA,EAAC,wBAAA;YAAwB,GAAG;gBAAE,CAAC,cAAc,CAAA,EAAG;YAAG,CAAA;YAAG,KAAK;YACxD;QAAA,CACH;IAEJ;IAGF,mBAAmB,WAAA,GAAc;IAMjC,SAAS,oBAAoB;QAC3B,OAAOG,gNAAAA,CAAM,QAAA,CAAyC,IAAI,YAAY,CAAC;IACzE;IAMA,SAAS,cAAc,KAAA,EAAY;QACjC,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,qBAAqB,OAAO,sBAAsB,KAAK;QAE3E,OAAO;IACT;IAEA,MAAM,YAAY;QAChB;QACA;QACA;IACF;IAEA,OAAO;QACL;YAAE,UAAU;YAAoB,MAAM;YAAgB,UAAU;QAAmB;QACnF;KACF;AACF;AAKA,SAAS,aAAa,CAAA,EAAQ,CAAA,EAAQ;IACpC,IAAI,MAAM,EAAG,CAAA,OAAO;IACpB,IAAI,OAAO,MAAM,YAAY,OAAO,MAAM,SAAU,CAAA,OAAO;IAC3D,IAAI,KAAK,QAAQ,KAAK,KAAM,CAAA,OAAO;IACnC,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,MAAM,QAAQ,OAAO,IAAA,CAAK,CAAC;IAC3B,IAAI,MAAM,MAAA,KAAW,MAAM,MAAA,CAAQ,CAAA,OAAO;IAC1C,KAAA,MAAW,OAAO,MAAO;QACvB,IAAI,CAAC,OAAO,SAAA,CAAU,cAAA,CAAe,IAAA,CAAK,GAAG,GAAG,EAAG,CAAA,OAAO;QAC1D,IAAI,CAAA,CAAE,GAAG,CAAA,KAAM,CAAA,CAAE,GAAG,CAAA,CAAG,CAAA,OAAO;IAChC;IACA,OAAO;AACT;AAEA,SAAS,mBAAmB,CAAA,EAAY,CAAA,EAAY;IAClD,OAAO,CAAC,CAAA,CAAE,EAAE,uBAAA,CAAwB,CAAC,IAAI,KAAK,2BAAA;AAChD;AAEA,SAAS,uBACP,CAAA,EACA,CAAA,EACA;IACA,OAAO,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,IAAW,CAAC,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,GAC1B,IACA,mBAAmB,CAAA,CAAE,CAAC,CAAA,CAAE,OAAA,EAAS,CAAA,CAAE,CAAC,CAAA,CAAE,OAAO,IAC3C,CAAA,IACA;AACR;AAEA,SAAS,qBAAqB,QAAA,EAAsB;IAClD,MAAM,WAAW,IAAI,iBAAiB,CAAC,kBAAkB;QACvD,KAAA,MAAW,YAAY,cAAe;YACpC,IAAI,SAAS,IAAA,KAAS,aAAa;gBACjC,SAAS;gBACT;YACF;QACF;IACF,CAAC;IAED,OAAO;AACT", "ignoreList": [0, 1, 2], "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-use-layout-effect/src/use-layout-effect.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "names": ["useLayoutEffect"], "mappings": ";;;;AAAA,YAAY,WAAW;;AASvB,IAAMA,mBAAkB,YAAY,iNAAiB,kBAAA,GAAkB,KAAO,CAAD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-id/src/id.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// We spaces with `.trim().toString()` to prevent bundlers from trying to `import { useId } from 'react';`\nconst useReactId = (React as any)[' useId '.trim().toString()] || (() => undefined);\nlet count = 0;\n\nfunction useId(deterministicId?: string): string {\n  const [id, setId] = React.useState<string | undefined>(useReactId());\n  // React versions older than 18 will have client-side ids only.\n  useLayoutEffect(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : '');\n}\n\nexport { useId };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;;;AAGhC,IAAM,aAAc,qMAAA,CAAc,UAAU,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA,IAAA,CAAM,IAAM,KAAA,CAAA;AACzE,IAAI,QAAQ;AAEZ,SAAS,MAAM,eAAA,EAAkC;IAC/C,MAAM,CAAC,IAAI,KAAK,CAAA,GAAU,sMAAA,QAAA,CAA6B,WAAW,CAAC;IAEnE,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,CAAC,gBAAiB,CAAA,MAAM,CAAC,UAAY,WAAW,OAAO,OAAO,CAAC;IACrE,GAAG;QAAC,eAAe;KAAC;IACpB,OAAO,mBAAA,CAAoB,KAAK,CAAA,MAAA,EAAS,EAAE,EAAA,GAAK,EAAA;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-use-callback-ref/src/use-callback-ref.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\n * prop or avoid re-executing effects when passed as a dependency\n */\nfunction useCallbackRef<T extends (...args: any[]) => any>(callback: T | undefined): T {\n  const callbackRef = React.useRef(callback);\n\n  React.useEffect(() => {\n    callbackRef.current = callback;\n  });\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => callbackRef.current?.(...args)) as T, []);\n}\n\nexport { useCallbackRef };\n"], "names": [], "mappings": ";;;;AAAA,YAAY,WAAW;;AAMvB,SAAS,eAAkD,QAAA,EAA4B;IACrF,MAAM,wNAAoB,SAAA,EAAO,QAAQ;8MAEnC,YAAA,EAAU,MAAM;QACpB,YAAY,OAAA,GAAU;IACxB,CAAC;IAGD,iNAAa,UAAA,EAAQ,IAAO,CAAA,GAAI,OAAS,YAAY,OAAA,GAAU,GAAG,IAAI,GAAS,CAAC,CAAC;AACnF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-use-effect-event/src/use-effect-event.tsx"], "sourcesContent": ["/* eslint-disable react-hooks/rules-of-hooks */\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport * as React from 'react';\n\ntype AnyFunction = (...args: any[]) => any;\n\n// See https://github.com/webpack/webpack/issues/14814\nconst useReactEffectEvent = (React as any)[' useEffectEvent '.trim().toString()];\nconst useReactInsertionEffect = (React as any)[' useInsertionEffect '.trim().toString()];\n\n/**\n * Designed to approximate the behavior on `experimental_useEffectEvent` as best\n * as possible until its stable release, and back-fill it as a shim as needed.\n */\nexport function useEffectEvent<T extends AnyFunction>(callback?: T): T {\n  if (typeof useReactEffectEvent === 'function') {\n    return useReactEffectEvent(callback);\n  }\n\n  const ref = React.useRef<AnyFunction | undefined>(() => {\n    throw new Error('Cannot call an event handler while rendering.');\n  });\n  // See https://github.com/webpack/webpack/issues/14814\n  if (typeof useReactInsertionEffect === 'function') {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    useLayoutEffect(() => {\n      ref.current = callback;\n    });\n  }\n\n  // https://github.com/facebook/react/issues/19240\n  return React.useMemo(() => ((...args) => ref.current?.(...args)) as T, []);\n}\n"], "names": [], "mappings": ";;;;AACA,SAAS,uBAAuB;AAChC,YAAY,WAAW;;;AAKvB,IAAM,sBAAuB,qMAAA,CAAc,mBAAmB,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA;AAC/E,IAAM,0BAA2B,qMAAA,CAAc,uBAAuB,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA;AAMhF,SAAS,eAAsC,QAAA,EAAiB;IACrE,IAAI,OAAO,wBAAwB,YAAY;QAC7C,OAAO,oBAAoB,QAAQ;IACrC;IAEA,MAAM,MAAY,sMAAA,MAAA,CAAgC,MAAM;QACtD,MAAM,IAAI,MAAM,+CAA+C;IACjE,CAAC;IAED,IAAI,OAAO,4BAA4B,YAAY;QACjD,wBAAwB,MAAM;YAC5B,IAAI,OAAA,GAAU;QAChB,CAAC;IACH,OAAO;QACL,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;YACpB,IAAI,OAAA,GAAU;QAChB,CAAC;IACH;IAGA,OAAa,sMAAA,OAAA,CAAQ,IAAO,CAAA,GAAI,OAAS,IAAI,OAAA,GAAU,GAAG,IAAI,GAAS,CAAC,CAAC;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-use-controllable-state/src/use-controllable-state.tsx", "file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-use-controllable-state/src/use-controllable-state-reducer.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n// Prevent bundlers from trying to optimize the import\nconst useInsertionEffect: typeof useLayoutEffect =\n  (React as any)[' useInsertionEffect '.trim().toString()] || useLayoutEffect;\n\ntype ChangeHandler<T> = (state: T) => void;\ntype SetStateFn<T> = React.Dispatch<React.SetStateAction<T>>;\n\ninterface UseControllableStateParams<T> {\n  prop?: T | undefined;\n  defaultProp: T;\n  onChange?: ChangeHandler<T>;\n  caller?: string;\n}\n\nexport function useControllableState<T>({\n  prop,\n  defaultProp,\n  onChange = () => {},\n  caller,\n}: UseControllableStateParams<T>): [T, SetStateFn<T>] {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange,\n  });\n  const isControlled = prop !== undefined;\n  const value = isControlled ? prop : uncontrolledProp;\n\n  // OK to disable conditionally calling hooks here because they will always run\n  // consistently in the same environment. Bundlers should be able to remove the\n  // code block entirely in production.\n  /* eslint-disable react-hooks/rules-of-hooks */\n  if (process.env.NODE_ENV !== 'production') {\n    const isControlledRef = React.useRef(prop !== undefined);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? 'controlled' : 'uncontrolled';\n        const to = isControlled ? 'controlled' : 'uncontrolled';\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks */\n\n  const setValue = React.useCallback<SetStateFn<T>>(\n    (nextValue) => {\n      if (isControlled) {\n        const value = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value !== prop) {\n          onChangeRef.current?.(value);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n\n  return [value, setValue];\n}\n\nfunction useUncontrolledState<T>({\n  defaultProp,\n  onChange,\n}: Omit<UseControllableStateParams<T>, 'prop'>): [\n  Value: T,\n  setValue: React.Dispatch<React.SetStateAction<T>>,\n  OnChangeRef: React.RefObject<ChangeHandler<T> | undefined>,\n] {\n  const [value, setValue] = React.useState(defaultProp);\n  const prevValueRef = React.useRef(value);\n\n  const onChangeRef = React.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n\n  React.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n\n  return [value, setValue, onChangeRef];\n}\n\nfunction isFunction(value: unknown): value is (...args: any[]) => any {\n  return typeof value === 'function';\n}\n", "import * as React from 'react';\nimport { useEffectEvent } from '@radix-ui/react-use-effect-event';\n\ntype ChangeHandler<T> = (state: T) => void;\n\ninterface UseControllableStateParams<T> {\n  prop: T | undefined;\n  defaultProp: T;\n  onChange: ChangeHandler<T> | undefined;\n  caller: string;\n}\n\ninterface AnyAction {\n  type: string;\n}\n\nconst SYNC_STATE = Symbol('RADIX:SYNC_STATE');\n\ninterface SyncStateAction<T> {\n  type: typeof SYNC_STATE;\n  state: T;\n}\n\nexport function useControllableStateReducer<T, S extends {}, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialState: S\n): [S & { state: T }, React.Dispatch<A>];\n\nexport function useControllableStateReducer<T, S extends {}, I, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialArg: I,\n  init: (i: I & { state: T }) => S\n): [S & { state: T }, React.Dispatch<A>];\n\nexport function useControllableStateReducer<T, S extends {}, A extends AnyAction>(\n  reducer: (prevState: S & { state: T }, action: A) => S & { state: T },\n  userArgs: UseControllableStateParams<T>,\n  initialArg: any,\n  init?: (i: any) => Omit<S, 'state'>\n): [S & { state: T }, React.Dispatch<A>] {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== undefined;\n\n  const onChange = useEffectEvent(onChangeProp);\n\n  // OK to disable conditionally calling hooks here because they will always run\n  // consistently in the same environment. Bundlers should be able to remove the\n  // code block entirely in production.\n  /* eslint-disable react-hooks/rules-of-hooks */\n  if (process.env.NODE_ENV !== 'production') {\n    const isControlledRef = React.useRef(controlledState !== undefined);\n    React.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? 'controlled' : 'uncontrolled';\n        const to = isControlled ? 'controlled' : 'uncontrolled';\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  /* eslint-enable react-hooks/rules-of-hooks */\n\n  type InternalState = S & { state: T };\n  const args: [InternalState] = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    // @ts-expect-error\n    args.push(init);\n  }\n\n  const [internalState, dispatch] = React.useReducer(\n    (state: InternalState, action: A | SyncStateAction<T>): InternalState => {\n      if (action.type === SYNC_STATE) {\n        return { ...state, state: action.state };\n      }\n\n      const next = reducer(state, action);\n      if (isControlled && !Object.is(next.state, state.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n\n  const uncontrolledState = internalState.state;\n  const prevValueRef = React.useRef(uncontrolledState);\n  React.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n\n  const state = React.useMemo(() => {\n    const isControlled = controlledState !== undefined;\n    if (isControlled) {\n      return { ...internalState, state: controlledState };\n    }\n\n    return internalState;\n  }, [internalState, controlledState]);\n\n  React.useEffect(() => {\n    // Sync internal state for controlled components so that reducer is called\n    // with the correct state values\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n\n  return [state, dispatch as React.Dispatch<A>];\n}\n"], "names": ["value", "React", "state", "isControlled"], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,uBAAuB;ACAhC,SAAS,sBAAsB;;;ADG/B,IAAM,qBACH,qMAAA,CAAc,uBAAuB,IAAA,CAAK,EAAE,QAAA,CAAS,CAAC,CAAA,wLAAK,kBAAA;AAYvD,SAAS,qBAAwB,EACtC,IAAA,EACA,WAAA,EACA,WAAW,KAAO,CAAA,AAAD,EACjB,MAAA,EACF,EAAsD;IACpD,MAAM,CAAC,kBAAkB,qBAAqB,WAAW,CAAA,GAAI,qBAAqB;QAChF;QACA;IACF,CAAC;IACD,MAAM,eAAe,SAAS,KAAA;IAC9B,MAAM,QAAQ,eAAe,OAAO;IAMpC,IAAI,oCAAuC;QACzC,MAAM,kBAAwB,sMAAA,MAAA,CAAO,SAAS,KAAA,CAAS;QACjD,sMAAA,SAAA,CAAU,MAAM;YACpB,MAAM,gBAAgB,gBAAgB,OAAA;YACtC,IAAI,kBAAkB,cAAc;gBAClC,MAAM,OAAO,gBAAgB,eAAe;gBAC5C,MAAM,KAAK,eAAe,eAAe;gBACzC,QAAQ,IAAA,CACN,GAAG,MAAM,CAAA,kBAAA,EAAqB,IAAI,CAAA,IAAA,EAAO,EAAE,CAAA,0KAAA,CAAA;YAE/C;YACA,gBAAgB,OAAA,GAAU;QAC5B,GAAG;YAAC;YAAc,MAAM;SAAC;IAC3B;IAGA,MAAM,WAAiB,sMAAA,WAAA,CACrB,CAAC,cAAc;QACb,IAAI,cAAc;YAChB,MAAMA,SAAQ,WAAW,SAAS,IAAI,UAAU,IAAI,IAAI;YACxD,IAAIA,WAAU,MAAM;gBAClB,YAAY,OAAA,GAAUA,MAAK;YAC7B;QACF,OAAO;YACL,oBAAoB,SAAS;QAC/B;IACF,GACA;QAAC;QAAc;QAAM;QAAqB,WAAW;KAAA;IAGvD,OAAO;QAAC;QAAO,QAAQ;KAAA;AACzB;AAEA,SAAS,qBAAwB,EAC/B,WAAA,EACA,QAAA,EACF,EAIE;IACA,MAAM,CAAC,OAAO,QAAQ,CAAA,GAAU,sMAAA,QAAA,CAAS,WAAW;IACpD,MAAM,eAAqB,sMAAA,MAAA,CAAO,KAAK;IAEvC,MAAM,cAAoB,sMAAA,MAAA,CAAO,QAAQ;IACzC,mBAAmB,MAAM;QACvB,YAAY,OAAA,GAAU;IACxB,GAAG;QAAC,QAAQ;KAAC;IAEP,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,aAAa,OAAA,KAAY,OAAO;YAClC,YAAY,OAAA,GAAU,KAAK;YAC3B,aAAa,OAAA,GAAU;QACzB;IACF,GAAG;QAAC;QAAO,YAAY;KAAC;IAExB,OAAO;QAAC;QAAO;QAAU,WAAW;KAAA;AACtC;AAEA,SAAS,WAAW,KAAA,EAAkD;IACpE,OAAO,OAAO,UAAU;AAC1B;;;AC/EA,IAAM,aAAa,OAAO,kBAAkB;AAoBrC,SAAS,4BACd,OAAA,EACA,QAAA,EACA,UAAA,EACA,IAAA,EACuC;IACvC,MAAM,EAAE,MAAM,eAAA,EAAiB,WAAA,EAAa,UAAU,YAAA,EAAc,MAAA,CAAO,CAAA,GAAI;IAC/E,MAAM,eAAe,oBAAoB,KAAA;IAEzC,MAAM,kMAAW,iBAAA,EAAe,YAAY;IAM5C,IAAI,oCAAuC;QACzC,MAAM,kBAAwB,sMAAA,MAAA,CAAO,oBAAoB,KAAA,CAAS;QAC5D,sMAAA,SAAA,CAAU,MAAM;YACpB,MAAM,gBAAgB,gBAAgB,OAAA;YACtC,IAAI,kBAAkB,cAAc;gBAClC,MAAM,OAAO,gBAAgB,eAAe;gBAC5C,MAAM,KAAK,eAAe,eAAe;gBACzC,QAAQ,IAAA,CACN,GAAG,MAAM,CAAA,kBAAA,EAAqB,IAAI,CAAA,IAAA,EAAO,EAAE,CAAA,0KAAA,CAAA;YAE/C;YACA,gBAAgB,OAAA,GAAU;QAC5B,GAAG;YAAC;YAAc,MAAM;SAAC;IAC3B;IAIA,MAAM,OAAwB;QAAC;YAAE,GAAG,UAAA;YAAY,OAAO;QAAY,CAAC;KAAA;IACpE,IAAI,MAAM;QAER,KAAK,IAAA,CAAK,IAAI;IAChB;IAEA,MAAM,CAAC,eAAe,QAAQ,CAAA,GAAU,sMAAA,UAAA,CACtC,CAACE,QAAsB,WAAkD;QACvE,IAAI,OAAO,IAAA,KAAS,YAAY;YAC9B,OAAO;gBAAE,GAAGA,MAAAA;gBAAO,OAAO,OAAO,KAAA;YAAM;QACzC;QAEA,MAAM,OAAO,QAAQA,QAAO,MAAM;QAClC,IAAI,gBAAgB,CAAC,OAAO,EAAA,CAAG,KAAK,KAAA,EAAOA,OAAM,KAAK,GAAG;YACvD,SAAS,KAAK,KAAK;QACrB;QACA,OAAO;IACT,MACG;IAGL,MAAM,oBAAoB,cAAc,KAAA;IACxC,MAAM,eAAqB,sMAAA,MAAA,CAAO,iBAAiB;IAC7C,sMAAA,SAAA,CAAU,MAAM;QACpB,IAAI,aAAa,OAAA,KAAY,mBAAmB;YAC9C,aAAa,OAAA,GAAU;YACvB,IAAI,CAAC,cAAc;gBACjB,SAAS,iBAAiB;YAC5B;QACF;IACF,GAAG;QAAC;QAAU;QAAmB;QAAc,YAAY;KAAC;IAE5D,MAAM,QAAc,sMAAA,OAAA,CAAQ,MAAM;QAChC,MAAMC,gBAAe,oBAAoB,KAAA;QACzC,IAAIA,eAAc;YAChB,OAAO;gBAAE,GAAG,aAAA;gBAAe,OAAO;YAAgB;QACpD;QAEA,OAAO;IACT,GAAG;QAAC;QAAe,eAAe;KAAC;IAE7B,sMAAA,SAAA,CAAU,MAAM;QAGpB,IAAI,gBAAgB,CAAC,OAAO,EAAA,CAAG,iBAAiB,cAAc,KAAK,GAAG;YACpE,SAAS;gBAAE,MAAM;gBAAY,OAAO;YAAgB,CAAC;QACvD;IACF,GAAG;QAAC;QAAiB,cAAc,KAAA;QAAO,YAAY;KAAC;IAEvD,OAAO;QAAC;QAAO,QAA6B;KAAA;AAC9C", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-direction/src/direction.tsx"], "sourcesContent": ["import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n"], "names": [], "mappings": ";;;;;;AAAA,YAAY,WAAW;AAed;;;AAZT,IAAM,6NAAyB,gBAAA,EAAqC,KAAA,CAAS;AAU7E,IAAM,oBAAsD,CAAC,UAAU;IACrE,MAAM,EAAE,GAAA,EAAK,QAAA,CAAS,CAAA,GAAI;IAC1B,OAAO,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,iBAAiB,QAAA,EAAjB;QAA0B,OAAO;QAAM;IAAA,CAAS;AAC1D;AAIA,SAAS,aAAa,QAAA,EAAsB;IAC1C,MAAM,sNAAkB,aAAA,EAAW,gBAAgB;IACnD,OAAO,YAAY,aAAa;AAClC;AAEA,IAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-roving-focus/src/roving-focus-group.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n"], "names": [], "mappings": ";;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,wBAAwB;AACjC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,aAAa;AACtB,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,4BAA4B;AACrC,SAAS,oBAAoB;AAgEnB;;;;;;;;;;;;;AA5DV,IAAM,cAAc;AACpB,IAAM,gBAAgB;IAAE,SAAS;IAAO,YAAY;AAAK;AAMzD,IAAM,aAAa;AAGnB,IAAM,CAAC,YAAY,eAAe,qBAAqB,CAAA,8KAAI,mBAAA,EAGzD,UAAU;AAGZ,IAAM,CAAC,+BAA+B,2BAA2B,CAAA,2KAAI,qBAAA,EACnE,YACA;IAAC,qBAAqB;CAAA;AA+BxB,IAAM,CAAC,qBAAqB,qBAAqB,CAAA,GAC/C,8BAAkD,UAAU;AAK9D,IAAM,6NAAyB,aAAA,EAC7B,CAAC,OAA2C,iBAAiB;IAC3D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QAAoB,OAAO,MAAM,uBAAA;QAChC,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,IAAA,EAAX;YAAgB,OAAO,MAAM,uBAAA;YAC5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,sBAAA;gBAAsB,GAAG,KAAA;gBAAO,KAAK;YAAA,CAAc;QAAA,CACtD;IAAA,CACF;AAEJ;AAGF,iBAAiB,WAAA,GAAc;AAgB/B,IAAM,iOAA6B,aAAA,EAGjC,CAAC,OAA+C,iBAAiB;IACjE,MAAM,EACJ,uBAAA,EACA,WAAA,EACA,OAAO,KAAA,EACP,GAAA,EACA,kBAAkB,oBAAA,EAClB,uBAAA,EACA,wBAAA,EACA,YAAA,EACA,4BAA4B,KAAA,EAC5B,GAAG,YACL,GAAI;IACJ,MAAM,gNAAY,SAAA,EAAoC,IAAI;IAC1D,MAAM,mBAAe,8LAAA,EAAgB,cAAc,GAAG;IACtD,MAAM,sLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,gMAAI,uBAAA,EAAqB;QACnE,MAAM;QACN,aAAa,2BAA2B;QACxC,UAAU;QACV,QAAQ;IACV,CAAC;IACD,MAAM,CAAC,kBAAkB,mBAAmB,CAAA,4MAAU,YAAA,EAAS,KAAK;IACpE,MAAM,0MAAmB,iBAAA,EAAe,YAAY;IACpD,MAAM,WAAW,cAAc,uBAAuB;IACtD,MAAM,4NAAwB,SAAA,EAAO,KAAK;IAC1C,MAAM,CAAC,qBAAqB,sBAAsB,CAAA,IAAU,oNAAA,EAAS,CAAC;8MAEhE,YAAA,EAAU,MAAM;QACpB,MAAM,OAAO,IAAI,OAAA;QACjB,IAAI,MAAM;YACR,KAAK,gBAAA,CAAiB,aAAa,gBAAgB;YACnD,OAAO,IAAM,KAAK,mBAAA,CAAoB,aAAa,gBAAgB;QACrE;IACF,GAAG;QAAC,gBAAgB;KAAC;IAErB,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,qBAAA;QACC,OAAO;QACP;QACA,KAAK;QACL;QACA;QACA,uNAAmB,cAAA,EACjB,CAAC,YAAc,oBAAoB,SAAS,GAC5C;YAAC,mBAAmB;SAAA;QAEtB,0NAAsB,cAAA,EAAY,IAAM,oBAAoB,IAAI,GAAG,CAAC,CAAC;QACrE,oBAA0B,wNAAA,EACxB,IAAM,uBAAuB,CAAC,YAAc,YAAY,CAAC,GACzD,CAAC,CAAA;QAEH,iOAA6B,cAAA,EAC3B,IAAM,uBAAuB,CAAC,YAAc,YAAY,CAAC,GACzD,CAAC,CAAA;QAGH,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,UAAU,oBAAoB,wBAAwB,IAAI,CAAA,IAAK;YAC/D,oBAAkB;YACjB,GAAG,UAAA;YACJ,KAAK;YACL,OAAO;gBAAE,SAAS;gBAAQ,GAAG,MAAM,KAAA;YAAM;YACzC,8KAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,MAAM;gBACzD,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,aAAS,oLAAA,EAAqB,MAAM,OAAA,EAAS,CAAC,UAAU;gBAKtD,MAAM,kBAAkB,CAAC,gBAAgB,OAAA;gBAEzC,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,IAAiB,mBAAmB,CAAC,kBAAkB;oBAChF,MAAM,kBAAkB,IAAI,YAAY,aAAa,aAAa;oBAClE,MAAM,aAAA,CAAc,aAAA,CAAc,eAAe;oBAEjD,IAAI,CAAC,gBAAgB,gBAAA,EAAkB;wBACrC,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;wBACxD,MAAM,aAAa,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,MAAM;wBACnD,MAAM,cAAc,MAAM,IAAA,CAAK,CAAC,OAAS,KAAK,EAAA,KAAO,gBAAgB;wBACrE,MAAM,iBAAiB;4BAAC;4BAAY,aAAa;+BAAG,KAAK;yBAAA,CAAE,MAAA,CACzD;wBAEF,MAAM,iBAAiB,eAAe,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;wBACrE,WAAW,gBAAgB,yBAAyB;oBACtD;gBACF;gBAEA,gBAAgB,OAAA,GAAU;YAC5B,CAAC;YACD,yKAAQ,uBAAA,EAAqB,MAAM,MAAA,EAAQ,IAAM,oBAAoB,KAAK,CAAC;QAAA;IAC7E;AAGN,CAAC;AAMD,IAAM,YAAY;AAalB,IAAM,iOAA6B,aAAA,EACjC,CAAC,OAA0C,iBAAiB;IAC1D,MAAM,EACJ,uBAAA,EACA,YAAY,IAAA,EACZ,SAAS,KAAA,EACT,SAAA,EACA,QAAA,EACA,GAAG,WACL,GAAI;IACJ,MAAM,4KAAS,QAAA,CAAM;IACrB,MAAM,KAAK,aAAa;IACxB,MAAM,UAAU,sBAAsB,WAAW,uBAAuB;IACxE,MAAM,mBAAmB,QAAQ,gBAAA,KAAqB;IACtD,MAAM,WAAW,cAAc,uBAAuB;IAEtD,MAAM,EAAE,kBAAA,EAAoB,qBAAA,EAAuB,gBAAA,CAAiB,CAAA,GAAI;QAElE,kNAAA,EAAU,MAAM;QACpB,IAAI,WAAW;YACb,mBAAmB;YACnB,OAAO,IAAM,sBAAsB;QACrC;IACF,GAAG;QAAC;QAAW;QAAoB,qBAAqB;KAAC;IAEzD,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,WAAW,QAAA,EAAX;QACC,OAAO;QACP;QACA;QACA;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;YACC,UAAU,mBAAmB,IAAI,CAAA;YACjC,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;YACL,8KAAa,uBAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,UAAW,CAAA,MAAM,cAAA,CAAe;qBAEhC,QAAQ,WAAA,CAAY,EAAE;YAC7B,CAAC;YACD,UAAS,uLAAA,EAAqB,MAAM,OAAA,EAAS,IAAM,QAAQ,WAAA,CAAY,EAAE,CAAC;YAC1E,4KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI,MAAM,GAAA,KAAQ,SAAS,MAAM,QAAA,EAAU;oBACzC,QAAQ,cAAA,CAAe;oBACvB;gBACF;gBAEA,IAAI,MAAM,MAAA,KAAW,MAAM,aAAA,CAAe,CAAA;gBAE1C,MAAM,cAAc,eAAe,OAAO,QAAQ,WAAA,EAAa,QAAQ,GAAG;gBAE1E,IAAI,gBAAgB,KAAA,GAAW;oBAC7B,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,IAAW,MAAM,MAAA,IAAU,MAAM,QAAA,CAAU,CAAA;oBACtE,MAAM,cAAA,CAAe;oBACrB,MAAM,QAAQ,SAAS,EAAE,MAAA,CAAO,CAAC,OAAS,KAAK,SAAS;oBACxD,IAAI,iBAAiB,MAAM,GAAA,CAAI,CAAC,OAAS,KAAK,GAAA,CAAI,OAAQ;oBAE1D,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;yBAAA,IAC1C,gBAAgB,UAAU,gBAAgB,QAAQ;wBACzD,IAAI,gBAAgB,OAAQ,CAAA,eAAe,OAAA,CAAQ;wBACnD,MAAM,eAAe,eAAe,OAAA,CAAQ,MAAM,aAAa;wBAC/D,iBAAiB,QAAQ,IAAA,GACrB,UAAU,gBAAgB,eAAe,CAAC,IAC1C,eAAe,KAAA,CAAM,eAAe,CAAC;oBAC3C;oBAMA,WAAW,IAAM,WAAW,cAAc,CAAC;gBAC7C;YACF,CAAC;YAEA,UAAA,OAAO,aAAa,aACjB,SAAS;gBAAE;gBAAkB,YAAY,oBAAoB;YAAK,CAAC,IACnE;QAAA;IACN;AAGN;AAGF,qBAAqB,WAAA,GAAc;AAKnC,IAAM,0BAAuD;IAC3D,WAAW;IAAQ,SAAS;IAC5B,YAAY;IAAQ,WAAW;IAC/B,QAAQ;IAAS,MAAM;IACvB,UAAU;IAAQ,KAAK;AACzB;AAEA,SAAS,qBAAqB,GAAA,EAAa,GAAA,EAAiB;IAC1D,IAAI,QAAQ,MAAO,CAAA,OAAO;IAC1B,OAAO,QAAQ,cAAc,eAAe,QAAQ,eAAe,cAAc;AACnF;AAIA,SAAS,eAAe,KAAA,EAA4B,WAAA,EAA2B,GAAA,EAAiB;IAC9F,MAAM,MAAM,qBAAqB,MAAM,GAAA,EAAK,GAAG;IAC/C,IAAI,gBAAgB,cAAc;QAAC;QAAa,YAAY;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACpF,IAAI,gBAAgB,gBAAgB;QAAC;QAAW,WAAW;KAAA,CAAE,QAAA,CAAS,GAAG,EAAG,CAAA,OAAO,KAAA;IACnF,OAAO,uBAAA,CAAwB,GAAG,CAAA;AACpC;AAEA,SAAS,WAAW,UAAA,EAA2B,gBAAgB,KAAA,EAAO;IACpE,MAAM,6BAA6B,SAAS,aAAA;IAC5C,KAAA,MAAW,aAAa,WAAY;QAElC,IAAI,cAAc,2BAA4B,CAAA;QAC9C,UAAU,KAAA,CAAM;YAAE;QAAc,CAAC;QACjC,IAAI,SAAS,aAAA,KAAkB,2BAA4B,CAAA;IAC7D;AACF;AAMA,SAAS,UAAa,KAAA,EAAY,UAAA,EAAoB;IACpD,OAAO,MAAM,GAAA,CAAO,CAAC,GAAG,QAAU,KAAA,CAAA,CAAO,aAAa,KAAA,IAAS,MAAM,MAAM,CAAE;AAC/E;AAEA,IAAM,OAAO;AACb,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-presence/src/presence.tsx", "file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-presence/src/use-state-machine.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useStateMachine } from './use-state-machine';\n\ninterface PresenceProps {\n  children: React.ReactElement | ((props: { present: boolean }) => React.ReactElement);\n  present: boolean;\n}\n\nconst Presence: React.FC<PresenceProps> = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n\n  const child = (\n    typeof children === 'function'\n      ? children({ present: presence.isPresent })\n      : React.Children.only(children)\n  ) as React.ReactElement<{ ref?: React.Ref<HTMLElement> }>;\n\n  const ref = useComposedRefs(presence.ref, getElementRef(child));\n  const forceMount = typeof children === 'function';\n  return forceMount || presence.isPresent ? React.cloneElement(child, { ref }) : null;\n};\n\nPresence.displayName = 'Presence';\n\n/* -------------------------------------------------------------------------------------------------\n * usePresence\n * -----------------------------------------------------------------------------------------------*/\n\nfunction usePresence(present: boolean) {\n  const [node, setNode] = React.useState<HTMLElement>();\n  const stylesRef = React.useRef<CSSStyleDeclaration | null>(null);\n  const prevPresentRef = React.useRef(present);\n  const prevAnimationNameRef = React.useRef<string>('none');\n  const initialState = present ? 'mounted' : 'unmounted';\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: 'unmounted',\n      ANIMATION_OUT: 'unmountSuspended',\n    },\n    unmountSuspended: {\n      MOUNT: 'mounted',\n      ANIMATION_END: 'unmounted',\n    },\n    unmounted: {\n      MOUNT: 'mounted',\n    },\n  });\n\n  React.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === 'mounted' ? currentAnimationName : 'none';\n  }, [state]);\n\n  useLayoutEffect(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n\n      if (present) {\n        send('MOUNT');\n      } else if (currentAnimationName === 'none' || styles?.display === 'none') {\n        // If there is no exit animation or the element is hidden, animations won't run\n        // so we unmount instantly\n        send('UNMOUNT');\n      } else {\n        /**\n         * When `present` changes to `false`, we check changes to animation-name to\n         * determine whether an animation has started. We chose this approach (reading\n         * computed styles) because there is no `animationrun` event and `animationstart`\n         * fires after `animation-delay` has expired which would be too late.\n         */\n        const isAnimating = prevAnimationName !== currentAnimationName;\n\n        if (wasPresent && isAnimating) {\n          send('ANIMATION_OUT');\n        } else {\n          send('UNMOUNT');\n        }\n      }\n\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n\n  useLayoutEffect(() => {\n    if (node) {\n      let timeoutId: number;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      /**\n       * Triggering an ANIMATION_OUT during an ANIMATION_IN will fire an `animationcancel`\n       * event for ANIMATION_IN after we have entered `unmountSuspended` state. So, we\n       * make sure we only trigger ANIMATION_END for the currently active animation.\n       */\n      const handleAnimationEnd = (event: AnimationEvent) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          // With React 18 concurrency this update is applied a frame after the\n          // animation ends, creating a flash of visible content. By setting the\n          // animation fill mode to \"forwards\", we force the node to keep the\n          // styles of the last keyframe, removing the flash.\n          //\n          // Previously we flushed the update via ReactDom.flushSync, but with\n          // exit animations this resulted in the node being removed from the\n          // DOM before the synthetic animationEnd event was dispatched, meaning\n          // user-provided event handlers would not be called.\n          // https://github.com/radix-ui/primitives/pull/1849\n          send('ANIMATION_END');\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = 'forwards';\n            // Reset the style after the node had time to unmount (for cases\n            // where the component chooses not to unmount). Doing this any\n            // sooner than `setTimeout` (e.g. with `requestAnimationFrame`)\n            // still causes a flash.\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === 'forwards') {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event: AnimationEvent) => {\n        if (event.target === node) {\n          // if animation occurred, store its name as the previous animation.\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener('animationstart', handleAnimationStart);\n      node.addEventListener('animationcancel', handleAnimationEnd);\n      node.addEventListener('animationend', handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener('animationstart', handleAnimationStart);\n        node.removeEventListener('animationcancel', handleAnimationEnd);\n        node.removeEventListener('animationend', handleAnimationEnd);\n      };\n    } else {\n      // Transition to the unmounted state if the node is removed prematurely.\n      // We avoid doing so during cleanup as the node may change but still exist.\n      send('ANIMATION_END');\n    }\n  }, [node, send]);\n\n  return {\n    isPresent: ['mounted', 'unmountSuspended'].includes(state),\n    ref: React.useCallback((node: HTMLElement) => {\n      stylesRef.current = node ? getComputedStyle(node) : null;\n      setNode(node);\n    }, []),\n  };\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getAnimationName(styles: CSSStyleDeclaration | null) {\n  return styles?.animationName || 'none';\n}\n\n// Before React 19 accessing `element.props.ref` will throw a warning and suggest using `element.ref`\n// After React 19 accessing `element.ref` does the opposite.\n// https://github.com/facebook/react/pull/28348\n//\n// Access the ref using the method that doesn't yield a warning.\nfunction getElementRef(element: React.ReactElement<{ ref?: React.Ref<unknown> }>) {\n  // React <=18 in DEV\n  let getter = Object.getOwnPropertyDescriptor(element.props, 'ref')?.get;\n  let mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return (element as any).ref;\n  }\n\n  // React 19 in DEV\n  getter = Object.getOwnPropertyDescriptor(element, 'ref')?.get;\n  mayWarn = getter && 'isReactWarning' in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n\n  // Not DEV\n  return element.props.ref || (element as any).ref;\n}\n\nconst Root = Presence;\n\nexport {\n  Presence,\n  //\n  Root,\n};\nexport type { PresenceProps };\n", "import * as React from 'react';\n\ntype Machine<S> = { [k: string]: { [k: string]: S } };\ntype MachineState<T> = keyof T;\ntype MachineEvent<T> = keyof UnionToIntersection<T[keyof T]>;\n\n// 🤯 https://fettblog.eu/typescript-union-to-intersection/\ntype UnionToIntersection<T> = (T extends any ? (x: T) => any : never) extends (x: infer R) => any\n  ? R\n  : never;\n\nexport function useStateMachine<M>(\n  initialState: MachineState<M>,\n  machine: M & Machine<MachineState<M>>\n) {\n  return React.useReducer((state: MachineState<M>, event: MachineEvent<M>): MachineState<M> => {\n    const nextState = (machine[state] as any)[event];\n    return nextState ?? state;\n  }, initialState);\n}\n"], "names": ["React", "node"], "mappings": ";;;;;AAAA,YAAYA,YAAW;AACvB,SAAS,uBAAuB;AAChC,SAAS,uBAAuB;;;;;;ACSzB,SAAS,gBACd,YAAA,EACA,OAAA,EACA;IACA,iNAAa,aAAA,EAAW,CAAC,OAAwB,UAA4C;QAC3F,MAAM,YAAa,OAAA,CAAQ,KAAK,CAAA,CAAU,KAAK,CAAA;QAC/C,OAAO,aAAa;IACtB,GAAG,YAAY;AACjB;;ADTA,IAAM,WAAoC,CAAC,UAAU;IACnD,MAAM,EAAE,OAAA,EAAS,QAAA,CAAS,CAAA,GAAI;IAC9B,MAAM,WAAW,YAAY,OAAO;IAEpC,MAAM,QACJ,OAAO,aAAa,aAChB,SAAS;QAAE,SAAS,SAAS,SAAA;IAAU,CAAC,0MAClC,WAAA,CAAS,IAAA,CAAK,QAAQ;IAGlC,MAAM,UAAM,8LAAA,EAAgB,SAAS,GAAA,EAAK,cAAc,KAAK,CAAC;IAC9D,MAAM,aAAa,OAAO,aAAa;IACvC,OAAO,cAAc,SAAS,SAAA,GAAkB,yNAAA,EAAa,OAAO;QAAE;IAAI,CAAC,IAAI;AACjF;AAEA,SAAS,WAAA,GAAc;AAMvB,SAAS,YAAY,OAAA,EAAkB;IACrC,MAAM,CAAC,MAAM,OAAO,CAAA,6MAAU,WAAA,CAAsB;IACpD,MAAM,gBAAkB,+MAAA,EAAmC,IAAI;IAC/D,MAAM,2NAAuB,SAAA,EAAO,OAAO;IAC3C,MAAM,wBAA6B,kNAAA,EAAe,MAAM;IACxD,MAAM,eAAe,UAAU,YAAY;IAC3C,MAAM,CAAC,OAAO,IAAI,CAAA,GAAI,gBAAgB,cAAc;QAClD,SAAS;YACP,SAAS;YACT,eAAe;QACjB;QACA,kBAAkB;YAChB,OAAO;YACP,eAAe;QACjB;QACA,WAAW;YACT,OAAO;QACT;IACF,CAAC;IAEK,sNAAA,EAAU,MAAM;QACpB,MAAM,uBAAuB,iBAAiB,UAAU,OAAO;QAC/D,qBAAqB,OAAA,GAAU,UAAU,YAAY,uBAAuB;IAC9E,GAAG;QAAC,KAAK;KAAC;IAEV,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,SAAS,UAAU,OAAA;QACzB,MAAM,aAAa,eAAe,OAAA;QAClC,MAAM,oBAAoB,eAAe;QAEzC,IAAI,mBAAmB;YACrB,MAAM,oBAAoB,qBAAqB,OAAA;YAC/C,MAAM,uBAAuB,iBAAiB,MAAM;YAEpD,IAAI,SAAS;gBACX,KAAK,OAAO;YACd,OAAA,IAAW,yBAAyB,UAAU,QAAQ,YAAY,QAAQ;gBAGxE,KAAK,SAAS;YAChB,OAAO;gBAOL,MAAM,cAAc,sBAAsB;gBAE1C,IAAI,cAAc,aAAa;oBAC7B,KAAK,eAAe;gBACtB,OAAO;oBACL,KAAK,SAAS;gBAChB;YACF;YAEA,eAAe,OAAA,GAAU;QAC3B;IACF,GAAG;QAAC;QAAS,IAAI;KAAC;IAElB,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,MAAM;YACR,IAAI;YACJ,MAAM,cAAc,KAAK,aAAA,CAAc,WAAA,IAAe;YAMtD,MAAM,qBAAqB,CAAC,UAA0B;gBACpD,MAAM,uBAAuB,iBAAiB,UAAU,OAAO;gBAC/D,MAAM,qBAAqB,qBAAqB,QAAA,CAAS,MAAM,aAAa;gBAC5E,IAAI,MAAM,MAAA,KAAW,QAAQ,oBAAoB;oBAW/C,KAAK,eAAe;oBACpB,IAAI,CAAC,eAAe,OAAA,EAAS;wBAC3B,MAAM,kBAAkB,KAAK,KAAA,CAAM,iBAAA;wBACnC,KAAK,KAAA,CAAM,iBAAA,GAAoB;wBAK/B,YAAY,YAAY,UAAA,CAAW,MAAM;4BACvC,IAAI,KAAK,KAAA,CAAM,iBAAA,KAAsB,YAAY;gCAC/C,KAAK,KAAA,CAAM,iBAAA,GAAoB;4BACjC;wBACF,CAAC;oBACH;gBACF;YACF;YACA,MAAM,uBAAuB,CAAC,UAA0B;gBACtD,IAAI,MAAM,MAAA,KAAW,MAAM;oBAEzB,qBAAqB,OAAA,GAAU,iBAAiB,UAAU,OAAO;gBACnE;YACF;YACA,KAAK,gBAAA,CAAiB,kBAAkB,oBAAoB;YAC5D,KAAK,gBAAA,CAAiB,mBAAmB,kBAAkB;YAC3D,KAAK,gBAAA,CAAiB,gBAAgB,kBAAkB;YACxD,OAAO,MAAM;gBACX,YAAY,YAAA,CAAa,SAAS;gBAClC,KAAK,mBAAA,CAAoB,kBAAkB,oBAAoB;gBAC/D,KAAK,mBAAA,CAAoB,mBAAmB,kBAAkB;gBAC9D,KAAK,mBAAA,CAAoB,gBAAgB,kBAAkB;YAC7D;QACF,OAAO;YAGL,KAAK,eAAe;QACtB;IACF,GAAG;QAAC;QAAM,IAAI;KAAC;IAEf,OAAO;QACL,WAAW;YAAC;YAAW,kBAAkB;SAAA,CAAE,QAAA,CAAS,KAAK;QACzD,+MAAW,cAAA,EAAY,CAACC,UAAsB;YAC5C,UAAU,OAAA,GAAUA,QAAO,iBAAiBA,KAAI,IAAI;YACpD,QAAQA,KAAI;QACd,GAAG,CAAC,CAAC;IACP;AACF;AAIA,SAAS,iBAAiB,MAAA,EAAoC;IAC5D,OAAO,QAAQ,iBAAiB;AAClC;AAOA,SAAS,cAAc,OAAA,EAA2D;IAEhF,IAAI,SAAS,OAAO,wBAAA,CAAyB,QAAQ,KAAA,EAAO,KAAK,GAAG;IACpE,IAAI,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IAC7D,IAAI,SAAS;QACX,OAAQ,QAAgB,GAAA;IAC1B;IAGA,SAAS,OAAO,wBAAA,CAAyB,SAAS,KAAK,GAAG;IAC1D,UAAU,UAAU,oBAAoB,UAAU,OAAO,cAAA;IACzD,IAAI,SAAS;QACX,OAAO,QAAQ,KAAA,CAAM,GAAA;IACvB;IAGA,OAAO,QAAQ,KAAA,CAAM,GAAA,IAAQ,QAAgB,GAAA;AAC/C;AAEA,IAAM,OAAO", "ignoreList": [0, 1], "debugId": null}}, {"offset": {"line": 1844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-tabs/src/tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ComponentRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "names": ["Root"], "mappings": ";;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,mCAAmC;AAC5C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAE1B,SAAS,oBAAoB;AAC7B,SAAS,4BAA4B;AACrC,SAAS,aAAa;AAoFd;;;;;;;;;;;;;AA5ER,IAAM,YAAY;AAGlB,IAAM,CAAC,mBAAmB,eAAe,CAAA,2KAAI,qBAAA,EAAmB,WAAW;gLACzE,8BAAA;CACD;AACD,IAAM,2MAA2B,8BAAA,CAA4B;AAW7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AA6BpF,IAAM,iNAAa,aAAA,EACjB,CAAC,OAA+B,iBAAiB;IAC/C,MAAM,EACJ,WAAA,EACA,OAAO,SAAA,EACP,aAAA,EACA,YAAA,EACA,cAAc,YAAA,EACd,GAAA,EACA,iBAAiB,WAAA,EACjB,GAAG,WACL,GAAI;IACJ,MAAM,sLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,UAAU;QACV,aAAa,gBAAgB;QAC7B,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;QACC,OAAO;QACP,2KAAQ,QAAA,CAAM;QACd;QACA,eAAe;QACf;QACA,KAAK;QACL;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,KAAK;YACL,oBAAkB;YACjB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,KAAK,WAAA,GAAc;AAMnB,IAAM,gBAAgB;AAOtB,IAAM,qNAAiB,aAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAA,EAAa,OAAO,IAAA,EAAM,GAAG,UAAU,CAAA,GAAI;IACnD,MAAM,UAAU,eAAe,eAAe,WAAW;IACzD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAkB,mLAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,aAAa,QAAQ,WAAA;QACrB,KAAK,QAAQ,GAAA;QACb;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,MAAK;YACL,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,eAAe;AAQrB,IAAM,wNAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAClE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,6KAAkB,QAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,WAAW,CAAC;QACZ,QAAQ;QAER,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe;YACf,iBAAe;YACf,cAAY,aAAa,WAAW;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACA,IAAI;YACH,GAAG,YAAA;YACJ,KAAK;YACL,cAAa,uLAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,aAAA,CAAc,KAAK;gBAC7B,OAAO;oBAEL,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,4KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI;oBAAC;oBAAK,OAAO;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,aAAA,CAAc,KAAK;YACrE,CAAC;YACD,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,MAAM;gBAGjD,MAAM,wBAAwB,QAAQ,cAAA,KAAmB;gBACzD,IAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;oBACrD,QAAQ,aAAA,CAAc,KAAK;gBAC7B;YACF,CAAC;QAAA;IACH;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,eAAe;AAarB,IAAM,wNAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,UAAA,EAAY,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,MAAM,yOAAqC,SAAA,EAAO,UAAU;8MAEtD,YAAA,EAAU,MAAM;QACpB,MAAM,MAAM,sBAAsB,IAAO,6BAA6B,OAAA,GAAU,KAAM;QACtF,OAAO,IAAM,qBAAqB,GAAG;IACvC,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,sKAAC,YAAA,EAAA;QAAS,SAAS,cAAc;QAC9B,UAAA,CAAC,EAAE,OAAA,CAAQ,CAAA,GACV,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,GAAA,EAAV;gBACC,cAAY,aAAa,WAAW;gBACpC,oBAAkB,QAAQ,WAAA;gBAC1B,MAAK;gBACL,mBAAiB;gBACjB,QAAQ,CAAC;gBACT,IAAI;gBACJ,UAAU;gBACT,GAAG,YAAA;gBACJ,KAAK;gBACL,OAAO;oBACL,GAAG,MAAM,KAAA;oBACT,mBAAmB,6BAA6B,OAAA,GAAU,OAAO,KAAA;gBACnE;gBAEC,UAAA,WAAW;YAAA;IACd,CAEJ;AAEJ;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,IAAMA,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,kNAAc,aAAA,EAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/%40radix-ui/react-separator/src/separator.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n *  Separator\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Separator';\nconst DEFAULT_ORIENTATION = 'horizontal';\nconst ORIENTATIONS = ['horizontal', 'vertical'] as const;\n\ntype Orientation = (typeof ORIENTATIONS)[number];\ntype SeparatorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface SeparatorProps extends PrimitiveDivProps {\n  /**\n   * Either `vertical` or `horizontal`. Defaults to `horizontal`.\n   */\n  orientation?: Orientation;\n  /**\n   * Whether or not the component is purely decorative. When true, accessibility-related attributes\n   * are updated so that that the rendered element is removed from the accessibility tree.\n   */\n  decorative?: boolean;\n}\n\nconst Separator = React.forwardRef<SeparatorElement, SeparatorProps>((props, forwardedRef) => {\n  const { decorative, orientation: orientationProp = DEFAULT_ORIENTATION, ...domProps } = props;\n  const orientation = isValidOrientation(orientationProp) ? orientationProp : DEFAULT_ORIENTATION;\n  // `aria-orientation` defaults to `horizontal` so we only need it if `orientation` is vertical\n  const ariaOrientation = orientation === 'vertical' ? orientation : undefined;\n  const semanticProps = decorative\n    ? { role: 'none' }\n    : { 'aria-orientation': ariaOrientation, role: 'separator' };\n\n  return (\n    <Primitive.div\n      data-orientation={orientation}\n      {...semanticProps}\n      {...domProps}\n      ref={forwardedRef}\n    />\n  );\n});\n\nSeparator.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isValidOrientation(orientation: any): orientation is Orientation {\n  return ORIENTATIONS.includes(orientation);\n}\n\nconst Root = Separator;\n\nexport {\n  Separator,\n  //\n  Root,\n};\nexport type { SeparatorProps };\n"], "names": [], "mappings": ";;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AAmCtB;;;;AA7BJ,IAAM,OAAO;AACb,IAAM,sBAAsB;AAC5B,IAAM,eAAe;IAAC;IAAc,UAAU;CAAA;AAiB9C,IAAM,sNAAkB,aAAA,EAA6C,CAAC,OAAO,iBAAiB;IAC5F,MAAM,EAAE,UAAA,EAAY,aAAa,kBAAkB,mBAAA,EAAqB,GAAG,SAAS,CAAA,GAAI;IACxF,MAAM,cAAc,mBAAmB,eAAe,IAAI,kBAAkB;IAE5E,MAAM,kBAAkB,gBAAgB,aAAa,cAAc,KAAA;IACnE,MAAM,gBAAgB,aAClB;QAAE,MAAM;IAAO,IACf;QAAE,oBAAoB;QAAiB,MAAM;IAAY;IAE7D,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QACC,oBAAkB;QACjB,GAAG,aAAA;QACH,GAAG,QAAA;QACJ,KAAK;IAAA;AAGX,CAAC;AAED,UAAU,WAAA,GAAc;AAIxB,SAAS,mBAAmB,WAAA,EAA8C;IACxE,OAAO,aAAa,QAAA,CAAS,WAAW;AAC1C;AAEA,IAAM,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2110, "column": 0}, "map": {"version": 3, "file": "wifi.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/wifi.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 20h.01', key: 'zekei9' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 20 0', key: 'dnpr2z' }],\n  ['path', { d: 'M5 12.859a10 10 0 0 1 14 0', key: '1x1e6c' }],\n  ['path', { d: 'M8.5 16.429a5 5 0 0 1 7 0', key: '1bycff' }],\n];\n\n/**\n * @component @name Wifi\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjBoLjAxIiAvPgogIDxwYXRoIGQ9Ik0yIDguODJhMTUgMTUgMCAwIDEgMjAgMCIgLz4KICA8cGF0aCBkPSJNNSAxMi44NTlhMTAgMTAgMCAwIDEgMTQgMCIgLz4KICA8cGF0aCBkPSJNOC41IDE2LjQyOWE1IDUgMCAwIDEgNyAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/wifi\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wifi = createLucideIcon('wifi', __iconNode);\n\nexport default Wifi;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2170, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,SAAW,CAAA;QAAA,CAAA;YAAE,QAAQ,oBAAsB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2209, "column": 0}, "map": {"version": 3, "file": "square.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n];\n\n/**\n * @component @name Square\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Square = createLucideIcon('square', __iconNode);\n\nexport default Square;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,EAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,EAAG,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2298, "column": 0}, "map": {"version": 3, "file": "circle-x.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/circle-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm15 9-6 6', key: '1uzhvr' }],\n  ['path', { d: 'm9 9 6 6', key: 'z0biqf' }],\n];\n\n/**\n * @component @name CircleX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleX = createLucideIcon('circle-x', __iconNode);\n\nexport default CircleX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2353, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2401, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2447, "column": 0}, "map": {"version": 3, "file": "network.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/network.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { x: '16', y: '16', width: '6', height: '6', rx: '1', key: '4q2zg0' }],\n  ['rect', { x: '2', y: '16', width: '6', height: '6', rx: '1', key: '8cvhb9' }],\n  ['rect', { x: '9', y: '2', width: '6', height: '6', rx: '1', key: '1egb70' }],\n  ['path', { d: 'M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3', key: '1jsf9p' }],\n  ['path', { d: 'M12 12V8', key: '2874zd' }],\n];\n\n/**\n * @component @name Network\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB4PSIxNiIgeT0iMTYiIHdpZHRoPSI2IiBoZWlnaHQ9IjYiIHJ4PSIxIiAvPgogIDxyZWN0IHg9IjIiIHk9IjE2IiB3aWR0aD0iNiIgaGVpZ2h0PSI2IiByeD0iMSIgLz4KICA8cmVjdCB4PSI5IiB5PSIyIiB3aWR0aD0iNiIgaGVpZ2h0PSI2IiByeD0iMSIgLz4KICA8cGF0aCBkPSJNNSAxNnYtM2ExIDEgMCAwIDEgMS0xaDEyYTEgMSAwIDAgMSAxIDF2MyIgLz4KICA8cGF0aCBkPSJNMTIgMTJWOCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/network\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Network = createLucideIcon('network', __iconNode);\n\nexport default Network;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC7E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,QAAQ,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
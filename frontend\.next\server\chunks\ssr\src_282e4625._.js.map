{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/discovery/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { apiService } from '@/lib/api'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Label } from '@/components/ui/label'\nimport { Separator } from '@/components/ui/separator'\nimport { \n  Search, \n  Wifi, \n  Camera, \n  Play, \n  Square, \n  CheckCircle, \n  XCircle, \n  Clock,\n  Plus,\n  Settings,\n  Network\n} from 'lucide-react'\n\ninterface DiscoveredCamera {\n  ipAddress: string\n  port: number\n  deviceInfo?: any\n  capabilities?: any\n  isReachable: boolean\n  responseTime: number\n  lastSeen: Date\n}\n\ninterface ScanStatus {\n  isScanning: boolean\n  progress: number\n  total: number\n}\n\nexport default function DiscoveryPage() {\n  const [scanStatus, setScanStatus] = useState<ScanStatus>({ isScanning: false, progress: 0, total: 0 })\n  const [discoveredCameras, setDiscoveredCameras] = useState<DiscoveredCamera[]>([])\n  const [scanOptions, setScanOptions] = useState({\n    networkRange: '***********/24',\n    portRange: [80, 8000, 8080],\n    timeout: 5000,\n    maxConcurrent: 20,\n    credentials: [{ username: 'admin', password: 'admin123' }]\n  })\n  const [testConnection, setTestConnection] = useState({\n    ipAddress: '',\n    port: 80,\n    username: 'admin',\n    password: '',\n    useHttps: false\n  })\n  const [testResult, setTestResult] = useState<any>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [loading, setLoading] = useState(false)\n\n  // Poll scan status when scanning\n  useEffect(() => {\n    let interval: NodeJS.Timeout\n    \n    if (scanStatus.isScanning) {\n      interval = setInterval(async () => {\n        try {\n          const status = await apiService.getScanStatus()\n          setScanStatus(status)\n          \n          if (!status.isScanning) {\n            clearInterval(interval)\n          }\n        } catch (error) {\n          console.error('Failed to get scan status:', error)\n        }\n      }, 1000)\n    }\n    \n    return () => {\n      if (interval) clearInterval(interval)\n    }\n  }, [scanStatus.isScanning])\n\n  const startNetworkScan = async () => {\n    try {\n      setError(null)\n      setLoading(true)\n      setDiscoveredCameras([])\n      \n      await apiService.startNetworkScan(scanOptions)\n      setScanStatus({ isScanning: true, progress: 0, total: 0 })\n    } catch (error: any) {\n      setError(error.message || 'Failed to start network scan')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const stopNetworkScan = async () => {\n    try {\n      await apiService.stopNetworkScan()\n      setScanStatus({ isScanning: false, progress: 0, total: 0 })\n    } catch (error: any) {\n      setError(error.message || 'Failed to stop network scan')\n    }\n  }\n\n  const testCameraConnection = async () => {\n    try {\n      setError(null)\n      setLoading(true)\n      setTestResult(null)\n      \n      const result = await apiService.testCameraConnection(testConnection)\n      setTestResult(result)\n    } catch (error: any) {\n      setError(error.message || 'Failed to test camera connection')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addCredential = () => {\n    setScanOptions(prev => ({\n      ...prev,\n      credentials: [...prev.credentials, { username: '', password: '' }]\n    }))\n  }\n\n  const removeCredential = (index: number) => {\n    setScanOptions(prev => ({\n      ...prev,\n      credentials: prev.credentials.filter((_, i) => i !== index)\n    }))\n  }\n\n  const updateCredential = (index: number, field: 'username' | 'password', value: string) => {\n    setScanOptions(prev => ({\n      ...prev,\n      credentials: prev.credentials.map((cred, i) => \n        i === index ? { ...cred, [field]: value } : cred\n      )\n    }))\n  }\n\n  const scanProgress = scanStatus.total > 0 ? (scanStatus.progress / scanStatus.total) * 100 : 0\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Camera Discovery</h1>\n          <p className=\"text-muted-foreground\">\n            Discover and connect HikVision cameras on your network\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Network className=\"h-8 w-8 text-primary\" />\n        </div>\n      </div>\n\n      {error && (\n        <Alert variant=\"destructive\">\n          <XCircle className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      <Tabs defaultValue=\"scan\" className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger value=\"scan\">Network Scan</TabsTrigger>\n          <TabsTrigger value=\"test\">Test Connection</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"scan\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Search className=\"h-5 w-5\" />\n                <span>Network Scan Configuration</span>\n              </CardTitle>\n              <CardDescription>\n                Configure network scanning parameters to discover HikVision cameras\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"networkRange\">Network Range (CIDR)</Label>\n                  <Input\n                    id=\"networkRange\"\n                    value={scanOptions.networkRange}\n                    onChange={(e) => setScanOptions(prev => ({ ...prev, networkRange: e.target.value }))}\n                    placeholder=\"***********/24\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"portRange\">Port Range (comma-separated)</Label>\n                  <Input\n                    id=\"portRange\"\n                    value={scanOptions.portRange.join(', ')}\n                    onChange={(e) => setScanOptions(prev => ({ \n                      ...prev, \n                      portRange: e.target.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p))\n                    }))}\n                    placeholder=\"80, 8000, 8080\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"timeout\">Timeout (ms)</Label>\n                  <Input\n                    id=\"timeout\"\n                    type=\"number\"\n                    value={scanOptions.timeout}\n                    onChange={(e) => setScanOptions(prev => ({ ...prev, timeout: parseInt(e.target.value) }))}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"maxConcurrent\">Max Concurrent</Label>\n                  <Input\n                    id=\"maxConcurrent\"\n                    type=\"number\"\n                    value={scanOptions.maxConcurrent}\n                    onChange={(e) => setScanOptions(prev => ({ ...prev, maxConcurrent: parseInt(e.target.value) }))}\n                  />\n                </div>\n              </div>\n\n              <Separator />\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Label>Default Credentials</Label>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={addCredential}\n                  >\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Add Credential\n                  </Button>\n                </div>\n                \n                {scanOptions.credentials.map((cred, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <Input\n                      placeholder=\"Username\"\n                      value={cred.username}\n                      onChange={(e) => updateCredential(index, 'username', e.target.value)}\n                    />\n                    <Input\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={cred.password}\n                      onChange={(e) => updateCredential(index, 'password', e.target.value)}\n                    />\n                    {scanOptions.credentials.length > 1 && (\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => removeCredential(index)}\n                      >\n                        Remove\n                      </Button>\n                    )}\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                {!scanStatus.isScanning ? (\n                  <Button \n                    onClick={startNetworkScan} \n                    disabled={loading}\n                    className=\"flex items-center space-x-2\"\n                  >\n                    <Play className=\"h-4 w-4\" />\n                    <span>Start Scan</span>\n                  </Button>\n                ) : (\n                  <Button \n                    onClick={stopNetworkScan} \n                    variant=\"destructive\"\n                    className=\"flex items-center space-x-2\"\n                  >\n                    <Square className=\"h-4 w-4\" />\n                    <span>Stop Scan</span>\n                  </Button>\n                )}\n              </div>\n\n              {scanStatus.isScanning && (\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span>Scanning progress</span>\n                    <span>{scanStatus.progress} / {scanStatus.total} ({Math.round(scanProgress)}%)</span>\n                  </div>\n                  <Progress value={scanProgress} className=\"w-full\" />\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {discoveredCameras.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Camera className=\"h-5 w-5\" />\n                  <span>Discovered Cameras ({discoveredCameras.length})</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {discoveredCameras.map((camera, index) => (\n                    <Card key={index} className=\"border-2\">\n                      <CardHeader className=\"pb-3\">\n                        <div className=\"flex items-center justify-between\">\n                          <CardTitle className=\"text-lg\">{camera.ipAddress}:{camera.port}</CardTitle>\n                          <Badge variant={camera.isReachable ? \"default\" : \"destructive\"}>\n                            {camera.isReachable ? \"Online\" : \"Offline\"}\n                          </Badge>\n                        </div>\n                      </CardHeader>\n                      <CardContent className=\"space-y-2\">\n                        <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                          <Clock className=\"h-4 w-4\" />\n                          <span>{camera.responseTime}ms</span>\n                        </div>\n                        {camera.deviceInfo && (\n                          <div className=\"text-sm\">\n                            <p><strong>Model:</strong> {camera.deviceInfo.model || 'Unknown'}</p>\n                            <p><strong>Serial:</strong> {camera.deviceInfo.serialNumber || 'Unknown'}</p>\n                          </div>\n                        )}\n                        <Button size=\"sm\" className=\"w-full\">\n                          <Plus className=\"h-4 w-4 mr-2\" />\n                          Add Camera\n                        </Button>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"test\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Settings className=\"h-5 w-5\" />\n                <span>Test Camera Connection</span>\n              </CardTitle>\n              <CardDescription>\n                Test connection to a specific camera with credentials\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testIp\">IP Address</Label>\n                  <Input\n                    id=\"testIp\"\n                    value={testConnection.ipAddress}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, ipAddress: e.target.value }))}\n                    placeholder=\"*************\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testPort\">Port</Label>\n                  <Input\n                    id=\"testPort\"\n                    type=\"number\"\n                    value={testConnection.port}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, port: parseInt(e.target.value) }))}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testUsername\">Username</Label>\n                  <Input\n                    id=\"testUsername\"\n                    value={testConnection.username}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, username: e.target.value }))}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testPassword\">Password</Label>\n                  <Input\n                    id=\"testPassword\"\n                    type=\"password\"\n                    value={testConnection.password}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, password: e.target.value }))}\n                  />\n                </div>\n              </div>\n\n              <Button \n                onClick={testCameraConnection} \n                disabled={loading || !testConnection.ipAddress || !testConnection.username || !testConnection.password}\n                className=\"flex items-center space-x-2\"\n              >\n                <Wifi className=\"h-4 w-4\" />\n                <span>Test Connection</span>\n              </Button>\n\n              {testResult && (\n                <Card className=\"mt-4\">\n                  <CardContent className=\"pt-6\">\n                    <div className=\"flex items-center space-x-2 mb-4\">\n                      {testResult.isConnected ? (\n                        <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                      ) : (\n                        <XCircle className=\"h-5 w-5 text-red-500\" />\n                      )}\n                      <span className=\"font-medium\">\n                        {testResult.isConnected ? 'Connection Successful' : 'Connection Failed'}\n                      </span>\n                    </div>\n                    <div className=\"space-y-1 text-sm text-muted-foreground\">\n                      <p><strong>IP:</strong> {testResult.ipAddress}:{testResult.port}</p>\n                      <p><strong>Response Time:</strong> {testResult.responseTime}ms</p>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;;;;;AA2Ce,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,YAAY;QAAO,UAAU;QAAG,OAAO;IAAE;IACpG,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,cAAc;QACd,WAAW;YAAC;YAAI;YAAM;SAAK;QAC3B,SAAS;QACT,eAAe;QACf,aAAa;YAAC;gBAAE,UAAU;gBAAS,UAAU;YAAW;SAAE;IAC5D;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,WAAW;QACX,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QAEJ,IAAI,WAAW,UAAU,EAAE;YACzB,WAAW,YAAY;gBACrB,IAAI;oBACF,MAAM,SAAS,MAAM,iHAAA,CAAA,aAAU,CAAC,aAAa;oBAC7C,cAAc;oBAEd,IAAI,CAAC,OAAO,UAAU,EAAE;wBACtB,cAAc;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF,GAAG;QACL;QAEA,OAAO;YACL,IAAI,UAAU,cAAc;QAC9B;IACF,GAAG;QAAC,WAAW,UAAU;KAAC;IAE1B,MAAM,mBAAmB;QACvB,IAAI;YACF,SAAS;YACT,WAAW;YACX,qBAAqB,EAAE;YAEvB,MAAM,iHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC;YAClC,cAAc;gBAAE,YAAY;gBAAM,UAAU;gBAAG,OAAO;YAAE;QAC1D,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,iHAAA,CAAA,aAAU,CAAC,eAAe;YAChC,cAAc;gBAAE,YAAY;gBAAO,UAAU;gBAAG,OAAO;YAAE;QAC3D,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,SAAS;YACT,WAAW;YACX,cAAc;YAEd,MAAM,SAAS,MAAM,iHAAA,CAAA,aAAU,CAAC,oBAAoB,CAAC;YACrD,cAAc;QAChB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa;uBAAI,KAAK,WAAW;oBAAE;wBAAE,UAAU;wBAAI,UAAU;oBAAG;iBAAE;YACpE,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACvD,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC,OAAe,OAAgC;QACvE,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,IACvC,MAAM,QAAQ;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,IAAI;YAEhD,CAAC;IACH;IAEA,MAAM,eAAe,WAAW,KAAK,GAAG,IAAI,AAAC,WAAW,QAAQ,GAAG,WAAW,KAAK,GAAI,MAAM;IAE7F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAItB,uBACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,8OAAC,4MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC,iIAAA,CAAA,mBAAgB;kCAAE;;;;;;;;;;;;0BAIvB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAO,WAAU;;kCAClC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;0CAC1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;;;;;;;kCAG5B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;;0CAClC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAe;;;;;;0EAC9B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,YAAY,YAAY;gEAC/B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAClF,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,YAAY,SAAS,CAAC,IAAI,CAAC;gEAClC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EACvC,GAAG,IAAI;4EACP,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,IAAI,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,MAAM;wEACvF,CAAC;gEACD,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;0EACzB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,OAAO;gEAC1B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAE,CAAC;;;;;;;;;;;;kEAG3F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAgB;;;;;;0EAC/B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,aAAa;gEAChC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAE,CAAC;;;;;;;;;;;;;;;;;;0DAKnG,8OAAC,qIAAA,CAAA,YAAS;;;;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;0EAAC;;;;;;0EACP,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;;kFAET,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;oDAKpC,YAAY,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC,iIAAA,CAAA,QAAK;oEACJ,aAAY;oEACZ,OAAO,KAAK,QAAQ;oEACpB,UAAU,CAAC,IAAM,iBAAiB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;8EAErE,8OAAC,iIAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,aAAY;oEACZ,OAAO,KAAK,QAAQ;oEACpB,UAAU,CAAC,IAAM,iBAAiB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;gEAEpE,YAAY,WAAW,CAAC,MAAM,GAAG,mBAChC,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,iBAAiB;8EACjC;;;;;;;2DAlBK;;;;;;;;;;;0DA0Bd,8OAAC;gDAAI,WAAU;0DACZ,CAAC,WAAW,UAAU,iBACrB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;yEAGR,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,SAAQ;oDACR,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;4CAKX,WAAW,UAAU,kBACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,WAAW,QAAQ;oEAAC;oEAAI,WAAW,KAAK;oEAAC;oEAAG,KAAK,KAAK,CAAC;oEAAc;;;;;;;;;;;;;kEAE9E,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,OAAO;wDAAc,WAAU;;;;;;;;;;;;;;;;;;;;;;;;4BAMhD,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;;wDAAK;wDAAqB,kBAAkB,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAGxD,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,8OAAC,gIAAA,CAAA,OAAI;oDAAa,WAAU;;sEAC1B,8OAAC,gIAAA,CAAA,aAAU;4DAAC,WAAU;sEACpB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;wEAAC,WAAU;;4EAAW,OAAO,SAAS;4EAAC;4EAAE,OAAO,IAAI;;;;;;;kFAC9D,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAS,OAAO,WAAW,GAAG,YAAY;kFAC9C,OAAO,WAAW,GAAG,WAAW;;;;;;;;;;;;;;;;;sEAIvC,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;;gFAAM,OAAO,YAAY;gFAAC;;;;;;;;;;;;;gEAE5B,OAAO,UAAU,kBAChB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FAAE,8OAAC;8FAAO;;;;;;gFAAe;gFAAE,OAAO,UAAU,CAAC,KAAK,IAAI;;;;;;;sFACvD,8OAAC;;8FAAE,8OAAC;8FAAO;;;;;;gFAAgB;gFAAE,OAAO,UAAU,CAAC,YAAY,IAAI;;;;;;;;;;;;;8EAGnE,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,WAAU;;sFAC1B,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;mDArB5B;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiCvB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAO,WAAU;kCAClC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAS;;;;;;sEACxB,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,eAAe,SAAS;4DAC/B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAClF,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,eAAe,IAAI;4DAC1B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;;;;;;;;;;;;8DAG3F,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,eAAe,QAAQ;4DAC9B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;;;;;;;;;;;;8DAGrF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAe;;;;;;sEAC9B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,eAAe,QAAQ;4DAC9B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;;;;;;;;;;;;;;;;;;sDAKvF,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,WAAW,CAAC,eAAe,SAAS,IAAI,CAAC,eAAe,QAAQ,IAAI,CAAC,eAAe,QAAQ;4CACtG,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;wCAGP,4BACC,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;4DACZ,WAAW,WAAW,iBACrB,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;qFAEvB,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EAErB,8OAAC;gEAAK,WAAU;0EACb,WAAW,WAAW,GAAG,0BAA0B;;;;;;;;;;;;kEAGxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAE,8OAAC;kFAAO;;;;;;oEAAY;oEAAE,WAAW,SAAS;oEAAC;oEAAE,WAAW,IAAI;;;;;;;0EAC/D,8OAAC;;kFAAE,8OAAC;kFAAO;;;;;;oEAAuB;oEAAE,WAAW,YAAY;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlF", "debugId": null}}]}
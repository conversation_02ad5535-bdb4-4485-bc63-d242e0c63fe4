{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n  }).format(new Date(date))\n}\n\nexport function formatFileSize(bytes: number): string {\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\"]\n  if (bytes === 0) return \"0 Bytes\"\n  const i = Math.floor(Math.log(bytes) / Math.log(1024))\n  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + \" \" + sizes[i]\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\",\n        secondary:\n          \"border-transparent bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300\",\n        destructive:\n          \"border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n        warning:\n          \"border-transparent bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300\",\n        outline: \n          \"text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600\",\n        purple:\n          \"border-transparent bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300\",\n        pink:\n          \"border-transparent bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,QACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-blue-600 text-white shadow-lg hover:bg-blue-700 hover:shadow-xl\",\n        destructive:\n          \"bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl\",\n        outline:\n          \"border border-gray-300 bg-transparent hover:bg-gray-50 hover:border-gray-400 dark:border-gray-600 dark:hover:bg-gray-800 dark:hover:border-gray-500\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700\",\n        ghost: \n          \"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100\",\n        link: \n          \"text-blue-600 underline-offset-4 hover:underline dark:text-blue-400\",\n        success:\n          \"bg-green-600 text-white shadow-lg hover:bg-green-700 hover:shadow-xl\",\n        warning:\n          \"bg-amber-600 text-white shadow-lg hover:bg-amber-700 hover:shadow-xl\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-12 rounded-lg px-8 text-base\",\n        xl: \"h-14 rounded-xl px-10 text-lg\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,iSACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MACE;YACF,SACE;YACF,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/events/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  Activity,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Eye,\n  Download,\n  Filter,\n  Search,\n  Calendar,\n  Camera,\n  User,\n  Car,\n  Shield,\n  Loader2,\n  AlertCircle\n} from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport { apiService } from \"@/lib/api\"\nimport { Event as EventType } from \"@/types/api\"\n\n// Helper function to format timestamp\nconst formatTimestamp = (timestamp: string): string => {\n  const date = new Date(timestamp)\n  const now = new Date()\n  const diffMs = now.getTime() - date.getTime()\n  const diffMinutes = Math.floor(diffMs / (1000 * 60))\n  const diffHours = Math.floor(diffMinutes / 60)\n  const diffDays = Math.floor(diffHours / 24)\n\n  if (diffMinutes < 1) return \"Just now\"\n  if (diffMinutes < 60) return `${diffMinutes}m ago`\n  if (diffHours < 24) return `${diffHours}h ago`\n  if (diffDays < 7) return `${diffDays}d ago`\n  return date.toLocaleDateString()\n}\n\nexport default function EventsPage() {\n  // State management\n  const [events, setEvents] = useState<EventType[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [statusFilter, setStatusFilter] = useState<\"all\" | \"active\" | \"acknowledged\" | \"resolved\">(\"all\")\n  const [severityFilter, setSeverityFilter] = useState<\"all\" | \"low\" | \"medium\" | \"high\" | \"critical\">(\"all\")\n  const [typeFilter, setTypeFilter] = useState<\"all\" | \"person\" | \"vehicle\" | \"motion\" | \"face\" | \"intrusion\">(\"all\")\n  const [acknowledgingEvent, setAcknowledgingEvent] = useState<string | null>(null)\n  const [resolvingEvent, setResolvingEvent] = useState<string | null>(null)\n\n  // Load events on component mount\n  useEffect(() => {\n    loadEvents()\n  }, [])\n\n  const loadEvents = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n      const response = await apiService.getEvents()\n      setEvents(response.events || [])\n    } catch (err: any) {\n      console.error('Failed to load events:', err)\n      setError(err.error || 'Failed to load events')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Handle event acknowledgment\n  const handleAcknowledgeEvent = async (eventId: string) => {\n    try {\n      setAcknowledgingEvent(eventId)\n      await apiService.acknowledgeEvent(eventId, {\n        notes: \"Acknowledged from dashboard\"\n      })\n      await loadEvents() // Refresh events\n    } catch (err: any) {\n      console.error('Failed to acknowledge event:', err)\n      setError(err.error || 'Failed to acknowledge event')\n    } finally {\n      setAcknowledgingEvent(null)\n    }\n  }\n\n  // Handle event resolution\n  const handleResolveEvent = async (eventId: string) => {\n    try {\n      setResolvingEvent(eventId)\n      await apiService.resolveEvent(eventId, {\n        resolution: \"Resolved from dashboard\",\n        notes: \"Event resolved by operator\"\n      })\n      await loadEvents() // Refresh events\n    } catch (err: any) {\n      console.error('Failed to resolve event:', err)\n      setError(err.error || 'Failed to resolve event')\n    } finally {\n      setResolvingEvent(null)\n    }\n  }\n\n  // Filter events based on search and filters\n  const filteredEvents = events.filter(event => {\n    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         event.description.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesStatus = statusFilter === \"all\" || event.status === statusFilter\n    const matchesSeverity = severityFilter === \"all\" || event.severity === severityFilter\n    const matchesType = typeFilter === \"all\" || event.type === typeFilter\n    return matchesSearch && matchesStatus && matchesSeverity && matchesType\n  })\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\n          <span>Loading events...</span>\n        </div>\n      </div>\n    )\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">Failed to Load Events</h3>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <Button onClick={loadEvents}>\n            Try Again\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"active\":\n        return <Badge variant=\"destructive\" size=\"sm\">Active</Badge>\n      case \"resolved\":\n        return <Badge variant=\"success\" size=\"sm\">Resolved</Badge>\n      case \"acknowledged\":\n        return <Badge variant=\"warning\" size=\"sm\">Acknowledged</Badge>\n      default:\n        return <Badge variant=\"secondary\" size=\"sm\">Unknown</Badge>\n    }\n  }\n\n  const getSeverityBadge = (severity: string) => {\n    switch (severity) {\n      case \"critical\":\n        return <Badge variant=\"destructive\" size=\"sm\">Critical</Badge>\n      case \"high\":\n        return <Badge variant=\"warning\" size=\"sm\">High</Badge>\n      case \"medium\":\n        return <Badge variant=\"default\" size=\"sm\">Medium</Badge>\n      case \"low\":\n        return <Badge variant=\"secondary\" size=\"sm\">Low</Badge>\n      default:\n        return <Badge variant=\"secondary\" size=\"sm\">Unknown</Badge>\n    }\n  }\n\n  const getEventIcon = (type: string) => {\n    switch (type) {\n      case \"person\":\n        return <User className=\"h-5 w-5\" />\n      case \"vehicle\":\n        return <Car className=\"h-5 w-5\" />\n      case \"motion\":\n        return <Activity className=\"h-5 w-5\" />\n      case \"face\":\n        return <Eye className=\"h-5 w-5\" />\n      case \"intrusion\":\n        return <Shield className=\"h-5 w-5\" />\n      case \"object\":\n        return <AlertTriangle className=\"h-5 w-5\" />\n      default:\n        return <Activity className=\"h-5 w-5\" />\n    }\n  }\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffMs = now.getTime() - date.getTime()\n    const diffMins = Math.floor(diffMs / 60000)\n    const diffHours = Math.floor(diffMins / 60)\n    const diffDays = Math.floor(diffHours / 24)\n\n    if (diffMins < 1) return \"Just now\"\n    if (diffMins < 60) return `${diffMins} minutes ago`\n    if (diffHours < 24) return `${diffHours} hours ago`\n    if (diffDays < 7) return `${diffDays} days ago`\n    return date.toLocaleDateString()\n  }\n\n  return (\n    <div className=\"flex-1 space-y-6 p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            Security Events\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Monitor and analyze security events from all cameras\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <Button variant=\"outline\">\n            <Download className=\"mr-2 h-4 w-4\" />\n            Export\n          </Button>\n          <Button variant=\"outline\">\n            <Calendar className=\"mr-2 h-4 w-4\" />\n            Date Range\n          </Button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-wrap items-center gap-4\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search events...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        \n        <select\n          value={statusFilter}\n          onChange={(e) => setStatusFilter(e.target.value as any)}\n          className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Status</option>\n          <option value=\"active\">Active</option>\n          <option value=\"acknowledged\">Acknowledged</option>\n          <option value=\"resolved\">Resolved</option>\n        </select>\n\n        <select\n          value={severityFilter}\n          onChange={(e) => setSeverityFilter(e.target.value as any)}\n          className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Severity</option>\n          <option value=\"critical\">Critical</option>\n          <option value=\"high\">High</option>\n          <option value=\"medium\">Medium</option>\n          <option value=\"low\">Low</option>\n        </select>\n\n        <select\n          value={typeFilter}\n          onChange={(e) => setTypeFilter(e.target.value as any)}\n          className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Types</option>\n          <option value=\"person\">Person</option>\n          <option value=\"vehicle\">Vehicle</option>\n          <option value=\"motion\">Motion</option>\n          <option value=\"face\">Face</option>\n          <option value=\"intrusion\">Intrusion</option>\n        </select>\n\n        <div className=\"ml-auto text-sm text-gray-600 dark:text-gray-400\">\n          {filteredEvents.length} of {events.length} events\n        </div>\n      </div>\n\n      {/* Events List */}\n      <div className=\"space-y-4\">\n        {filteredEvents.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Activity className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              No Events Found\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {events.length === 0\n                ? \"No events have been recorded yet.\"\n                : \"No events match your current filters.\"}\n            </p>\n          </div>\n        ) : (\n          filteredEvents.map((event, index) => (\n            <motion.div\n              key={event._id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n            >\n              <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start space-x-4\">\n                      <div className=\"p-2 rounded-lg bg-blue-100 dark:bg-blue-900\">\n                        {getEventIcon(event.type)}\n                      </div>\n                      <div className=\"flex-1 space-y-2\">\n                        <div className=\"flex items-center space-x-3\">\n                          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                            {event.title}\n                          </h3>\n                          {getStatusBadge(event.status)}\n                          {getSeverityBadge(event.severity)}\n                          <Badge variant=\"outline\" size=\"sm\">\n                            {event.confidence}% confidence\n                          </Badge>\n                        </div>\n                        <p className=\"text-gray-600 dark:text-gray-400\">\n                          {event.description}\n                        </p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n                          <span className=\"flex items-center space-x-1\">\n                            <Camera className=\"h-4 w-4\" />\n                            <span>{event.cameraName || 'Unknown Camera'}</span>\n                          </span>\n                          <span>{event.location || 'Unknown Location'}</span>\n                          <span>{formatTimestamp(event.timestamp)}</span>\n                        </div>\n                        {event.aiAnalysis && (\n                          <div className=\"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                            <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n                              <strong>AI Analysis:</strong> {event.aiAnalysis}\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      {event.status === 'active' && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleAcknowledgeEvent(event._id)}\n                          disabled={acknowledgingEvent === event._id}\n                        >\n                          {acknowledgingEvent === event._id ? (\n                            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                          ) : (\n                            <CheckCircle className=\"mr-2 h-4 w-4\" />\n                          )}\n                          Acknowledge\n                        </Button>\n                      )}\n                      {(event.status === 'active' || event.status === 'acknowledged') && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleResolveEvent(event._id)}\n                          disabled={resolvingEvent === event._id}\n                        >\n                          {resolvingEvent === event._id ? (\n                            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                          ) : (\n                            <Shield className=\"mr-2 h-4 w-4\" />\n                          )}\n                          Resolve\n                        </Button>\n                      )}\n                      <Button variant=\"outline\" size=\"sm\">\n                        <Eye className=\"mr-2 h-4 w-4\" />\n                        View\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;AAxBA;;;;;;;;;AA2BA,sCAAsC;AACtC,MAAM,kBAAkB,CAAC;IACvB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;IAC3C,MAAM,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;IAClD,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc;IAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;IAExC,IAAI,cAAc,GAAG,OAAO;IAC5B,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;IAClD,IAAI,YAAY,IAAI,OAAO,GAAG,UAAU,KAAK,CAAC;IAC9C,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,KAAK,CAAC;IAC3C,OAAO,KAAK,kBAAkB;AAChC;AAEe,SAAS;IACtB,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IACjG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IACrG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkE;IAC7G,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,iHAAA,CAAA,aAAU,CAAC,SAAS;YAC3C,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,IAAI,KAAK,IAAI;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,8BAA8B;IAC9B,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,sBAAsB;YACtB,MAAM,iHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,SAAS;gBACzC,OAAO;YACT;YACA,MAAM,aAAa,iBAAiB;;QACtC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,IAAI,KAAK,IAAI;QACxB,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,kBAAkB;YAClB,MAAM,iHAAA,CAAA,aAAU,CAAC,YAAY,CAAC,SAAS;gBACrC,YAAY;gBACZ,OAAO;YACT;YACA,MAAM,aAAa,iBAAiB;;QACtC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,IAAI,KAAK,IAAI;QACxB,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACpF,MAAM,gBAAgB,iBAAiB,SAAS,MAAM,MAAM,KAAK;QACjE,MAAM,kBAAkB,mBAAmB,SAAS,MAAM,QAAQ,KAAK;QACvE,MAAM,cAAc,eAAe,SAAS,MAAM,IAAI,KAAK;QAC3D,OAAO,iBAAiB,iBAAiB,mBAAmB;IAC9D;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;kCAAY;;;;;;;;;;;;;;;;;IAMrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,MAAK;8BAAK;;;;;;YAChD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,MAAK;8BAAK;;;;;;QAChD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,MAAK;8BAAK;;;;;;YAChD,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C,KAAK;gBACH,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,MAAK;8BAAK;;;;;;YAC9C;gBACE,qBAAO,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,MAAK;8BAAK;;;;;;QAChD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,gMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS;QACrC,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;QACxC,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;QAExC,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,WAAW,IAAI,OAAO,GAAG,SAAS,YAAY,CAAC;QACnD,IAAI,YAAY,IAAI,OAAO,GAAG,UAAU,UAAU,CAAC;QACnD,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,OAAO,KAAK,kBAAkB;IAChC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAIlD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAId,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBAC/C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,8OAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,8OAAC;gCAAO,OAAM;0CAAe;;;;;;0CAC7B,8OAAC;gCAAO,OAAM;0CAAW;;;;;;;;;;;;kCAG3B,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,8OAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,8OAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,8OAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,8OAAC;gCAAO,OAAM;0CAAM;;;;;;;;;;;;kCAGtB,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAU;;0CAEV,8OAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,8OAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,8OAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,8OAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,8OAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,8OAAC;gCAAO,OAAM;0CAAY;;;;;;;;;;;;kCAG5B,8OAAC;wBAAI,WAAU;;4BACZ,eAAe,MAAM;4BAAC;4BAAK,OAAO,MAAM;4BAAC;;;;;;;;;;;;;0BAK9C,8OAAC;gBAAI,WAAU;0BACZ,eAAe,MAAM,KAAK,kBACzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,8OAAC;4BAAE,WAAU;sCACV,OAAO,MAAM,KAAK,IACf,sCACA;;;;;;;;;;;2BAIR,eAAe,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAK;kCAElC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,aAAa,MAAM,IAAI;;;;;;8DAE1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,MAAM,KAAK;;;;;;gEAEb,eAAe,MAAM,MAAM;gEAC3B,iBAAiB,MAAM,QAAQ;8EAChC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,MAAK;;wEAC3B,MAAM,UAAU;wEAAC;;;;;;;;;;;;;sEAGtB,8OAAC;4DAAE,WAAU;sEACV,MAAM,WAAW;;;;;;sEAEpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAM,MAAM,UAAU,IAAI;;;;;;;;;;;;8EAE7B,8OAAC;8EAAM,MAAM,QAAQ,IAAI;;;;;;8EACzB,8OAAC;8EAAM,gBAAgB,MAAM,SAAS;;;;;;;;;;;;wDAEvC,MAAM,UAAU,kBACf,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAU;;kFACX,8OAAC;kFAAO;;;;;;oEAAqB;oEAAE,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMzD,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,MAAM,KAAK,0BAChB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,uBAAuB,MAAM,GAAG;oDAC/C,UAAU,uBAAuB,MAAM,GAAG;;wDAEzC,uBAAuB,MAAM,GAAG,iBAC/B,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,8OAAC,2NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACvB;;;;;;;gDAIL,CAAC,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,cAAc,mBAC5D,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,mBAAmB,MAAM,GAAG;oDAC3C,UAAU,mBAAmB,MAAM,GAAG;;wDAErC,mBAAmB,MAAM,GAAG,iBAC3B,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAClB;;;;;;;8DAIN,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;sEAC7B,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA3ErC,MAAM,GAAG;;;;;;;;;;;;;;;;AAwF5B", "debugId": null}}]}
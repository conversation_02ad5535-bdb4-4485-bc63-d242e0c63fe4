{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/discovery/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { apiClient } from '@/lib/api'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Label } from '@/components/ui/label'\nimport { Separator } from '@/components/ui/separator'\nimport { \n  Search, \n  Wifi, \n  Camera, \n  Play, \n  Square, \n  CheckCircle, \n  XCircle, \n  Clock,\n  Plus,\n  Settings,\n  Network\n} from 'lucide-react'\n\ninterface DiscoveredCamera {\n  ipAddress: string\n  port: number\n  deviceInfo?: any\n  capabilities?: any\n  isReachable: boolean\n  responseTime: number\n  lastSeen: Date\n}\n\ninterface ScanStatus {\n  isScanning: boolean\n  progress: number\n  total: number\n}\n\nexport default function DiscoveryPage() {\n  const [scanStatus, setScanStatus] = useState<ScanStatus>({ isScanning: false, progress: 0, total: 0 })\n  const [discoveredCameras, setDiscoveredCameras] = useState<DiscoveredCamera[]>([])\n  const [scanOptions, setScanOptions] = useState({\n    networkRange: '***********/24',\n    portRange: [80, 8000, 8080],\n    timeout: 5000,\n    maxConcurrent: 20,\n    credentials: [{ username: 'admin', password: 'admin123' }]\n  })\n  const [testConnection, setTestConnection] = useState({\n    ipAddress: '',\n    port: 80,\n    username: 'admin',\n    password: '',\n    useHttps: false\n  })\n  const [testResult, setTestResult] = useState<any>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [loading, setLoading] = useState(false)\n\n  // Poll scan status when scanning\n  useEffect(() => {\n    let interval: NodeJS.Timeout\n    \n    if (scanStatus.isScanning) {\n      interval = setInterval(async () => {\n        try {\n          const status = await apiClient.getScanStatus()\n          setScanStatus(status)\n          \n          if (!status.isScanning) {\n            clearInterval(interval)\n          }\n        } catch (error) {\n          console.error('Failed to get scan status:', error)\n        }\n      }, 1000)\n    }\n    \n    return () => {\n      if (interval) clearInterval(interval)\n    }\n  }, [scanStatus.isScanning])\n\n  const startNetworkScan = async () => {\n    try {\n      setError(null)\n      setLoading(true)\n      setDiscoveredCameras([])\n      \n      await apiClient.startNetworkScan(scanOptions)\n      setScanStatus({ isScanning: true, progress: 0, total: 0 })\n    } catch (error: any) {\n      setError(error.message || 'Failed to start network scan')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const stopNetworkScan = async () => {\n    try {\n      await apiClient.stopNetworkScan()\n      setScanStatus({ isScanning: false, progress: 0, total: 0 })\n    } catch (error: any) {\n      setError(error.message || 'Failed to stop network scan')\n    }\n  }\n\n  const testCameraConnection = async () => {\n    try {\n      setError(null)\n      setLoading(true)\n      setTestResult(null)\n      \n      const result = await apiClient.testCameraConnection(testConnection)\n      setTestResult(result)\n    } catch (error: any) {\n      setError(error.message || 'Failed to test camera connection')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addCredential = () => {\n    setScanOptions(prev => ({\n      ...prev,\n      credentials: [...prev.credentials, { username: '', password: '' }]\n    }))\n  }\n\n  const removeCredential = (index: number) => {\n    setScanOptions(prev => ({\n      ...prev,\n      credentials: prev.credentials.filter((_, i) => i !== index)\n    }))\n  }\n\n  const updateCredential = (index: number, field: 'username' | 'password', value: string) => {\n    setScanOptions(prev => ({\n      ...prev,\n      credentials: prev.credentials.map((cred, i) => \n        i === index ? { ...cred, [field]: value } : cred\n      )\n    }))\n  }\n\n  const scanProgress = scanStatus.total > 0 ? (scanStatus.progress / scanStatus.total) * 100 : 0\n\n  return (\n    <div className=\"container mx-auto p-6 space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold tracking-tight\">Camera Discovery</h1>\n          <p className=\"text-muted-foreground\">\n            Discover and connect HikVision cameras on your network\n          </p>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <Network className=\"h-8 w-8 text-primary\" />\n        </div>\n      </div>\n\n      {error && (\n        <Alert variant=\"destructive\">\n          <XCircle className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      <Tabs defaultValue=\"scan\" className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger value=\"scan\">Network Scan</TabsTrigger>\n          <TabsTrigger value=\"test\">Test Connection</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"scan\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Search className=\"h-5 w-5\" />\n                <span>Network Scan Configuration</span>\n              </CardTitle>\n              <CardDescription>\n                Configure network scanning parameters to discover HikVision cameras\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"networkRange\">Network Range (CIDR)</Label>\n                  <Input\n                    id=\"networkRange\"\n                    value={scanOptions.networkRange}\n                    onChange={(e) => setScanOptions(prev => ({ ...prev, networkRange: e.target.value }))}\n                    placeholder=\"***********/24\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"portRange\">Port Range (comma-separated)</Label>\n                  <Input\n                    id=\"portRange\"\n                    value={scanOptions.portRange.join(', ')}\n                    onChange={(e) => setScanOptions(prev => ({ \n                      ...prev, \n                      portRange: e.target.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p))\n                    }))}\n                    placeholder=\"80, 8000, 8080\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"timeout\">Timeout (ms)</Label>\n                  <Input\n                    id=\"timeout\"\n                    type=\"number\"\n                    value={scanOptions.timeout}\n                    onChange={(e) => setScanOptions(prev => ({ ...prev, timeout: parseInt(e.target.value) }))}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"maxConcurrent\">Max Concurrent</Label>\n                  <Input\n                    id=\"maxConcurrent\"\n                    type=\"number\"\n                    value={scanOptions.maxConcurrent}\n                    onChange={(e) => setScanOptions(prev => ({ ...prev, maxConcurrent: parseInt(e.target.value) }))}\n                  />\n                </div>\n              </div>\n\n              <Separator />\n\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <Label>Default Credentials</Label>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={addCredential}\n                  >\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Add Credential\n                  </Button>\n                </div>\n                \n                {scanOptions.credentials.map((cred, index) => (\n                  <div key={index} className=\"flex items-center space-x-2\">\n                    <Input\n                      placeholder=\"Username\"\n                      value={cred.username}\n                      onChange={(e) => updateCredential(index, 'username', e.target.value)}\n                    />\n                    <Input\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={cred.password}\n                      onChange={(e) => updateCredential(index, 'password', e.target.value)}\n                    />\n                    {scanOptions.credentials.length > 1 && (\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => removeCredential(index)}\n                      >\n                        Remove\n                      </Button>\n                    )}\n                  </div>\n                ))}\n              </div>\n\n              <div className=\"flex items-center space-x-2\">\n                {!scanStatus.isScanning ? (\n                  <Button \n                    onClick={startNetworkScan} \n                    disabled={loading}\n                    className=\"flex items-center space-x-2\"\n                  >\n                    <Play className=\"h-4 w-4\" />\n                    <span>Start Scan</span>\n                  </Button>\n                ) : (\n                  <Button \n                    onClick={stopNetworkScan} \n                    variant=\"destructive\"\n                    className=\"flex items-center space-x-2\"\n                  >\n                    <Square className=\"h-4 w-4\" />\n                    <span>Stop Scan</span>\n                  </Button>\n                )}\n              </div>\n\n              {scanStatus.isScanning && (\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between text-sm\">\n                    <span>Scanning progress</span>\n                    <span>{scanStatus.progress} / {scanStatus.total} ({Math.round(scanProgress)}%)</span>\n                  </div>\n                  <Progress value={scanProgress} className=\"w-full\" />\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {discoveredCameras.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Camera className=\"h-5 w-5\" />\n                  <span>Discovered Cameras ({discoveredCameras.length})</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  {discoveredCameras.map((camera, index) => (\n                    <Card key={index} className=\"border-2\">\n                      <CardHeader className=\"pb-3\">\n                        <div className=\"flex items-center justify-between\">\n                          <CardTitle className=\"text-lg\">{camera.ipAddress}:{camera.port}</CardTitle>\n                          <Badge variant={camera.isReachable ? \"default\" : \"destructive\"}>\n                            {camera.isReachable ? \"Online\" : \"Offline\"}\n                          </Badge>\n                        </div>\n                      </CardHeader>\n                      <CardContent className=\"space-y-2\">\n                        <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                          <Clock className=\"h-4 w-4\" />\n                          <span>{camera.responseTime}ms</span>\n                        </div>\n                        {camera.deviceInfo && (\n                          <div className=\"text-sm\">\n                            <p><strong>Model:</strong> {camera.deviceInfo.model || 'Unknown'}</p>\n                            <p><strong>Serial:</strong> {camera.deviceInfo.serialNumber || 'Unknown'}</p>\n                          </div>\n                        )}\n                        <Button size=\"sm\" className=\"w-full\">\n                          <Plus className=\"h-4 w-4 mr-2\" />\n                          Add Camera\n                        </Button>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"test\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Settings className=\"h-5 w-5\" />\n                <span>Test Camera Connection</span>\n              </CardTitle>\n              <CardDescription>\n                Test connection to a specific camera with credentials\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testIp\">IP Address</Label>\n                  <Input\n                    id=\"testIp\"\n                    value={testConnection.ipAddress}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, ipAddress: e.target.value }))}\n                    placeholder=\"*************\"\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testPort\">Port</Label>\n                  <Input\n                    id=\"testPort\"\n                    type=\"number\"\n                    value={testConnection.port}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, port: parseInt(e.target.value) }))}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testUsername\">Username</Label>\n                  <Input\n                    id=\"testUsername\"\n                    value={testConnection.username}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, username: e.target.value }))}\n                  />\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"testPassword\">Password</Label>\n                  <Input\n                    id=\"testPassword\"\n                    type=\"password\"\n                    value={testConnection.password}\n                    onChange={(e) => setTestConnection(prev => ({ ...prev, password: e.target.value }))}\n                  />\n                </div>\n              </div>\n\n              <Button \n                onClick={testCameraConnection} \n                disabled={loading || !testConnection.ipAddress || !testConnection.username || !testConnection.password}\n                className=\"flex items-center space-x-2\"\n              >\n                <Wifi className=\"h-4 w-4\" />\n                <span>Test Connection</span>\n              </Button>\n\n              {testResult && (\n                <Card className=\"mt-4\">\n                  <CardContent className=\"pt-6\">\n                    <div className=\"flex items-center space-x-2 mb-4\">\n                      {testResult.isConnected ? (\n                        <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                      ) : (\n                        <XCircle className=\"h-5 w-5 text-red-500\" />\n                      )}\n                      <span className=\"font-medium\">\n                        {testResult.isConnected ? 'Connection Successful' : 'Connection Failed'}\n                      </span>\n                    </div>\n                    <div className=\"space-y-1 text-sm text-muted-foreground\">\n                      <p><strong>IP:</strong> {testResult.ipAddress}:{testResult.port}</p>\n                      <p><strong>Response Time:</strong> {testResult.responseTime}ms</p>\n                    </div>\n                  </CardContent>\n                </Card>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;;;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;;;;;;;;;;;;;;AA2Ce,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QAAE,YAAY;QAAO,UAAU;QAAG,OAAO;IAAE;IACpG,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,cAAc;QACd,WAAW;YAAC;YAAI;YAAM;SAAK;QAC3B,SAAS;QACT,eAAe;QACf,aAAa;YAAC;gBAAE,UAAU;gBAAS,UAAU;YAAW;SAAE;IAC5D;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,WAAW;QACX,MAAM;QACN,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAClD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QAEJ,IAAI,WAAW,UAAU,EAAE;YACzB,WAAW,YAAY;gBACrB,IAAI;oBACF,MAAM,SAAS,MAAM,iHAAA,CAAA,YAAS,CAAC,aAAa;oBAC5C,cAAc;oBAEd,IAAI,CAAC,OAAO,UAAU,EAAE;wBACtB,cAAc;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC9C;YACF,GAAG;QACL;QAEA,OAAO;YACL,IAAI,UAAU,cAAc;QAC9B;IACF,GAAG;QAAC,WAAW,UAAU;KAAC;IAE1B,MAAM,mBAAmB;QACvB,IAAI;YACF,SAAS;YACT,WAAW;YACX,qBAAqB,EAAE;YAEvB,MAAM,iHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;YACjC,cAAc;gBAAE,YAAY;gBAAM,UAAU;gBAAG,OAAO;YAAE;QAC1D,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,iHAAA,CAAA,YAAS,CAAC,eAAe;YAC/B,cAAc;gBAAE,YAAY;gBAAO,UAAU;gBAAG,OAAO;YAAE;QAC3D,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,SAAS;YACT,WAAW;YACX,cAAc;YAEd,MAAM,SAAS,MAAM,iHAAA,CAAA,YAAS,CAAC,oBAAoB,CAAC;YACpD,cAAc;QAChB,EAAE,OAAO,OAAY;YACnB,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa;uBAAI,KAAK,WAAW;oBAAE;wBAAE,UAAU;wBAAI,UAAU;oBAAG;iBAAE;YACpE,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YACvD,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC,OAAe,OAAgC;QACvE,eAAe,CAAA,OAAQ,CAAC;gBACtB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,IACvC,MAAM,QAAQ;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,IAAI;YAEhD,CAAC;IACH;IAEA,MAAM,eAAe,WAAW,KAAK,GAAG,IAAI,AAAC,WAAW,QAAQ,GAAG,WAAW,KAAK,GAAI,MAAM;IAE7F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAItB,uBACC,8OAAC;gBAAM,SAAQ;;kCACb,8OAAC,4MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;kCAAkB;;;;;;;;;;;;0BAIvB,8OAAC;gBAAK,cAAa;gBAAO,WAAU;;kCAClC,8OAAC;wBAAS,WAAU;;0CAClB,8OAAC;gCAAY,OAAM;0CAAO;;;;;;0CAC1B,8OAAC;gCAAY,OAAM;0CAAO;;;;;;;;;;;;kCAG5B,8OAAC;wBAAY,OAAM;wBAAO,WAAU;;0CAClC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;0DAER,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;0EAAe;;;;;;0EAC9B,8OAAC;gEACC,IAAG;gEACH,OAAO,YAAY,YAAY;gEAC/B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAClF,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC;gEACC,IAAG;gEACH,OAAO,YAAY,SAAS,CAAC,IAAI,CAAC;gEAClC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EACvC,GAAG,IAAI;4EACP,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,SAAS,EAAE,IAAI,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,MAAM;wEACvF,CAAC;gEACD,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;0EAAU;;;;;;0EACzB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,OAAO;gEAC1B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAE,CAAC;;;;;;;;;;;;kEAG3F,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,SAAQ;0EAAgB;;;;;;0EAC/B,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,YAAY,aAAa;gEAChC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAE,CAAC;;;;;;;;;;;;;;;;;;0DAKnG,8OAAC;;;;;0DAED,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM;;;;;;0EACP,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS;;kFAET,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;oDAKpC,YAAY,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEACC,aAAY;oEACZ,OAAO,KAAK,QAAQ;oEACpB,UAAU,CAAC,IAAM,iBAAiB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;8EAErE,8OAAC;oEACC,MAAK;oEACL,aAAY;oEACZ,OAAO,KAAK,QAAQ;oEACpB,UAAU,CAAC,IAAM,iBAAiB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;gEAEpE,YAAY,WAAW,CAAC,MAAM,GAAG,mBAChC,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,iBAAiB;8EACjC;;;;;;;2DAlBK;;;;;;;;;;;0DA0Bd,8OAAC;gDAAI,WAAU;0DACZ,CAAC,WAAW,UAAU,iBACrB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU;oDACV,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;yEAGR,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,SAAQ;oDACR,WAAU;;sEAEV,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;4CAKX,WAAW,UAAU,kBACpB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,WAAW,QAAQ;oEAAC;oEAAI,WAAW,KAAK;oEAAC;oEAAG,KAAK,KAAK,CAAC;oEAAc;;;;;;;;;;;;;kEAE9E,8OAAC;wDAAS,OAAO;wDAAc,WAAU;;;;;;;;;;;;;;;;;;;;;;;;4BAMhD,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;;wDAAK;wDAAqB,kBAAkB,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAGxD,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,8OAAC,gIAAA,CAAA,OAAI;oDAAa,WAAU;;sEAC1B,8OAAC,gIAAA,CAAA,aAAU;4DAAC,WAAU;sEACpB,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gIAAA,CAAA,YAAS;wEAAC,WAAU;;4EAAW,OAAO,SAAS;4EAAC;4EAAE,OAAO,IAAI;;;;;;;kFAC9D,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAS,OAAO,WAAW,GAAG,YAAY;kFAC9C,OAAO,WAAW,GAAG,WAAW;;;;;;;;;;;;;;;;;sEAIvC,8OAAC,gIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;;gFAAM,OAAO,YAAY;gFAAC;;;;;;;;;;;;;gEAE5B,OAAO,UAAU,kBAChB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FAAE,8OAAC;8FAAO;;;;;;gFAAe;gFAAE,OAAO,UAAU,CAAC,KAAK,IAAI;;;;;;;sFACvD,8OAAC;;8FAAE,8OAAC;8FAAO;;;;;;gFAAgB;gFAAE,OAAO,UAAU,CAAC,YAAY,IAAI;;;;;;;;;;;;;8EAGnE,8OAAC,kIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,WAAU;;sFAC1B,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;mDArB5B;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiCvB,8OAAC;wBAAY,OAAM;wBAAO,WAAU;kCAClC,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,gIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;sEAAS;;;;;;sEACxB,8OAAC;4DACC,IAAG;4DACH,OAAO,eAAe,SAAS;4DAC/B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAClF,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;sEAAW;;;;;;sEAC1B,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,eAAe,IAAI;4DAC1B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;;;;;;;;;;;;8DAG3F,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;sEAAe;;;;;;sEAC9B,8OAAC;4DACC,IAAG;4DACH,OAAO,eAAe,QAAQ;4DAC9B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;;;;;;;;;;;;8DAGrF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,SAAQ;sEAAe;;;;;;sEAC9B,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,eAAe,QAAQ;4DAC9B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;;;;;;;;;;;;;;;;;;sDAKvF,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,WAAW,CAAC,eAAe,SAAS,IAAI,CAAC,eAAe,QAAQ,IAAI,CAAC,eAAe,QAAQ;4CACtG,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;wCAGP,4BACC,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;;4DACZ,WAAW,WAAW,iBACrB,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;qFAEvB,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EAErB,8OAAC;gEAAK,WAAU;0EACb,WAAW,WAAW,GAAG,0BAA0B;;;;;;;;;;;;kEAGxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFAAE,8OAAC;kFAAO;;;;;;oEAAY;oEAAE,WAAW,SAAS;oEAAC;oEAAE,WAAW,IAAI;;;;;;;0EAC/D,8OAAC;;kFAAE,8OAAC;kFAAO;;;;;;oEAAuB;oEAAE,WAAW,YAAY;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlF", "debugId": null}}]}
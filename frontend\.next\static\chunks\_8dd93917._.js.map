{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/settings/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { \n  Settings, \n  Camera,\n  Bell,\n  Shield,\n  Database,\n  Zap,\n  Mail,\n  Smartphone,\n  Save,\n  RefreshCw,\n  AlertTriangle,\n  CheckCircle,\n  Moon,\n  Sun,\n  Monitor\n} from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\n\ninterface SettingSection {\n  id: string\n  title: string\n  description: string\n  icon: React.ElementType\n}\n\nconst settingSections: SettingSection[] = [\n  {\n    id: \"general\",\n    title: \"General Settings\",\n    description: \"Basic system configuration and preferences\",\n    icon: Settings\n  },\n  {\n    id: \"cameras\",\n    title: \"Camera Configuration\",\n    description: \"Manage camera settings and connections\",\n    icon: Camera\n  },\n  {\n    id: \"ai\",\n    title: \"AI Processing\",\n    description: \"Configure AI models and detection settings\",\n    icon: Zap\n  },\n  {\n    id: \"notifications\",\n    title: \"Notifications\",\n    description: \"Set up alerts and notification preferences\",\n    icon: <PERSON>\n  },\n  {\n    id: \"security\",\n    title: \"Security & Access\",\n    description: \"User management and security settings\",\n    icon: Shield\n  },\n  {\n    id: \"storage\",\n    title: \"Storage & Backup\",\n    description: \"Configure data storage and backup options\",\n    icon: Database\n  }\n]\n\nexport default function SettingsPage() {\n  const [activeSection, setActiveSection] = useState(\"general\")\n  const [settings, setSettings] = useState({\n    // General Settings\n    systemName: \"HikVision AI Monitoring System\",\n    timezone: \"UTC-5\",\n    language: \"en\",\n    theme: \"system\",\n    autoRefresh: true,\n    refreshInterval: 30,\n    \n    // Camera Settings\n    defaultResolution: \"1080p\",\n    defaultFps: 25,\n    recordingQuality: \"high\",\n    motionSensitivity: 75,\n    nightVision: true,\n    \n    // AI Settings\n    aiEnabled: true,\n    personDetection: true,\n    vehicleDetection: true,\n    faceRecognition: false,\n    objectDetection: true,\n    confidenceThreshold: 80,\n    \n    // Notification Settings\n    emailNotifications: true,\n    smsNotifications: false,\n    pushNotifications: true,\n    criticalAlertsOnly: false,\n    notificationEmail: \"<EMAIL>\",\n    \n    // Security Settings\n    sessionTimeout: 60,\n    twoFactorAuth: false,\n    passwordExpiry: 90,\n    loginAttempts: 5,\n    \n    // Storage Settings\n    retentionPeriod: 30,\n    autoBackup: true,\n    backupFrequency: \"daily\",\n    compressionEnabled: true\n  })\n\n  const [saving, setSaving] = useState(false)\n  const [saved, setSaved] = useState(false)\n\n  const handleSave = async () => {\n    setSaving(true)\n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1500))\n    setSaving(false)\n    setSaved(true)\n    setTimeout(() => setSaved(false), 3000)\n  }\n\n  const renderGeneralSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid gap-6 md:grid-cols-2\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            System Name\n          </label>\n          <input\n            type=\"text\"\n            value={settings.systemName}\n            onChange={(e) => setSettings({...settings, systemName: e.target.value})}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Timezone\n          </label>\n          <select\n            value={settings.timezone}\n            onChange={(e) => setSettings({...settings, timezone: e.target.value})}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"UTC-8\">Pacific Time (UTC-8)</option>\n            <option value=\"UTC-5\">Eastern Time (UTC-5)</option>\n            <option value=\"UTC+0\">UTC</option>\n            <option value=\"UTC+1\">Central European Time (UTC+1)</option>\n          </select>\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Theme\n          </label>\n          <select\n            value={settings.theme}\n            onChange={(e) => setSettings({...settings, theme: e.target.value})}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          >\n            <option value=\"light\">Light</option>\n            <option value=\"dark\">Dark</option>\n            <option value=\"system\">System</option>\n          </select>\n        </div>\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Auto Refresh Interval (seconds)\n          </label>\n          <input\n            type=\"number\"\n            value={settings.refreshInterval}\n            onChange={(e) => setSettings({...settings, refreshInterval: parseInt(e.target.value)})}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            min=\"5\"\n            max=\"300\"\n          />\n        </div>\n      </div>\n      \n      <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n        <div>\n          <h4 className=\"font-medium text-gray-900 dark:text-white\">Auto Refresh</h4>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            Automatically refresh dashboard data\n          </p>\n        </div>\n        <label className=\"relative inline-flex items-center cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            checked={settings.autoRefresh}\n            onChange={(e) => setSettings({...settings, autoRefresh: e.target.checked})}\n            className=\"sr-only peer\"\n          />\n          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n        </label>\n      </div>\n    </div>\n  )\n\n  const renderAISettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n        <div>\n          <h4 className=\"font-medium text-blue-900 dark:text-blue-100\">AI Processing</h4>\n          <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n            Enable AI-powered detection and analysis\n          </p>\n        </div>\n        <label className=\"relative inline-flex items-center cursor-pointer\">\n          <input\n            type=\"checkbox\"\n            checked={settings.aiEnabled}\n            onChange={(e) => setSettings({...settings, aiEnabled: e.target.checked})}\n            className=\"sr-only peer\"\n          />\n          <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n        </label>\n      </div>\n\n      {settings.aiEnabled && (\n        <div className=\"space-y-4\">\n          <div className=\"grid gap-4 md:grid-cols-2\">\n            {[\n              { key: 'personDetection', label: 'Person Detection', description: 'Detect people in camera feeds' },\n              { key: 'vehicleDetection', label: 'Vehicle Detection', description: 'Identify cars, trucks, and motorcycles' },\n              { key: 'faceRecognition', label: 'Face Recognition', description: 'Recognize known individuals' },\n              { key: 'objectDetection', label: 'Object Detection', description: 'Detect suspicious objects' }\n            ].map((item) => (\n              <div key={item.key} className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\">\n                <div>\n                  <h5 className=\"font-medium text-gray-900 dark:text-white\">{item.label}</h5>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">{item.description}</p>\n                </div>\n                <label className=\"relative inline-flex items-center cursor-pointer\">\n                  <input\n                    type=\"checkbox\"\n                    checked={settings[item.key as keyof typeof settings] as boolean}\n                    onChange={(e) => setSettings({...settings, [item.key]: e.target.checked})}\n                    className=\"sr-only peer\"\n                  />\n                  <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n                </label>\n              </div>\n            ))}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Confidence Threshold: {settings.confidenceThreshold}%\n            </label>\n            <input\n              type=\"range\"\n              min=\"50\"\n              max=\"95\"\n              value={settings.confidenceThreshold}\n              onChange={(e) => setSettings({...settings, confidenceThreshold: parseInt(e.target.value)})}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\n            />\n            <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              <span>Less Accurate</span>\n              <span>More Accurate</span>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n\n  const renderNotificationSettings = () => (\n    <div className=\"space-y-6\">\n      <div className=\"grid gap-4\">\n        {[\n          { key: 'emailNotifications', label: 'Email Notifications', icon: Mail, description: 'Receive alerts via email' },\n          { key: 'smsNotifications', label: 'SMS Notifications', icon: Smartphone, description: 'Receive alerts via SMS' },\n          { key: 'pushNotifications', label: 'Push Notifications', icon: Bell, description: 'Browser push notifications' }\n        ].map((item) => (\n          <div key={item.key} className=\"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg\">\n            <div className=\"flex items-center space-x-3\">\n              <item.icon className=\"h-5 w-5 text-gray-600 dark:text-gray-400\" />\n              <div>\n                <h5 className=\"font-medium text-gray-900 dark:text-white\">{item.label}</h5>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">{item.description}</p>\n              </div>\n            </div>\n            <label className=\"relative inline-flex items-center cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={settings[item.key as keyof typeof settings] as boolean}\n                onChange={(e) => setSettings({...settings, [item.key]: e.target.checked})}\n                className=\"sr-only peer\"\n              />\n              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600\"></div>\n            </label>\n          </div>\n        ))}\n      </div>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Notification Email\n        </label>\n        <input\n          type=\"email\"\n          value={settings.notificationEmail}\n          onChange={(e) => setSettings({...settings, notificationEmail: e.target.value})}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          placeholder=\"<EMAIL>\"\n        />\n      </div>\n    </div>\n  )\n\n  const renderContent = () => {\n    switch (activeSection) {\n      case \"general\":\n        return renderGeneralSettings()\n      case \"ai\":\n        return renderAISettings()\n      case \"notifications\":\n        return renderNotificationSettings()\n      default:\n        return (\n          <div className=\"text-center py-12\">\n            <Settings className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n              Coming Soon\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              This settings section is under development.\n            </p>\n          </div>\n        )\n    }\n  }\n\n  return (\n    <div className=\"flex-1 space-y-6 p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            System Settings\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Configure your HikVision AI monitoring system\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <Button\n            variant=\"outline\"\n            onClick={() => window.location.reload()}\n          >\n            <RefreshCw className=\"mr-2 h-4 w-4\" />\n            Reset\n          </Button>\n          <Button\n            onClick={handleSave}\n            disabled={saving}\n          >\n            {saving ? (\n              <>\n                <RefreshCw className=\"mr-2 h-4 w-4 animate-spin\" />\n                Saving...\n              </>\n            ) : saved ? (\n              <>\n                <CheckCircle className=\"mr-2 h-4 w-4\" />\n                Saved\n              </>\n            ) : (\n              <>\n                <Save className=\"mr-2 h-4 w-4\" />\n                Save Changes\n              </>\n            )}\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"grid gap-6 lg:grid-cols-4\">\n        {/* Settings Navigation */}\n        <div className=\"lg:col-span-1\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Settings</CardTitle>\n              <CardDescription>\n                Choose a category to configure\n              </CardDescription>\n            </CardHeader>\n            <CardContent className=\"p-0\">\n              <nav className=\"space-y-1\">\n                {settingSections.map((section) => (\n                  <button\n                    key={section.id}\n                    onClick={() => setActiveSection(section.id)}\n                    className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${\n                      activeSection === section.id\n                        ? \"bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500 text-blue-700 dark:text-blue-300\"\n                        : \"text-gray-700 dark:text-gray-300\"\n                    }`}\n                  >\n                    <section.icon className=\"h-5 w-5\" />\n                    <div>\n                      <div className=\"font-medium\">{section.title}</div>\n                      <div className=\"text-xs text-gray-500 dark:text-gray-400\">\n                        {section.description}\n                      </div>\n                    </div>\n                  </button>\n                ))}\n              </nav>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Settings Content */}\n        <div className=\"lg:col-span-3\">\n          <Card>\n            <CardHeader>\n              <CardTitle>\n                {settingSections.find(s => s.id === activeSection)?.title}\n              </CardTitle>\n              <CardDescription>\n                {settingSections.find(s => s.id === activeSection)?.description}\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              {renderContent()}\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AAEA;;;AAvBA;;;;;AAgCA,MAAM,kBAAoC;IACxC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;IAChB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,yMAAA,CAAA,SAAM;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,mMAAA,CAAA,MAAG;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,qMAAA,CAAA,OAAI;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,yMAAA,CAAA,SAAM;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM,6MAAA,CAAA,WAAQ;IAChB;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,mBAAmB;QACnB,YAAY;QACZ,UAAU;QACV,UAAU;QACV,OAAO;QACP,aAAa;QACb,iBAAiB;QAEjB,kBAAkB;QAClB,mBAAmB;QACnB,YAAY;QACZ,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;QAEb,cAAc;QACd,WAAW;QACX,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,qBAAqB;QAErB,wBAAwB;QACxB,oBAAoB;QACpB,kBAAkB;QAClB,mBAAmB;QACnB,oBAAoB;QACpB,mBAAmB;QAEnB,oBAAoB;QACpB,gBAAgB;QAChB,eAAe;QACf,gBAAgB;QAChB,eAAe;QAEf,mBAAmB;QACnB,iBAAiB;QACjB,YAAY;QACZ,iBAAiB;QACjB,oBAAoB;IACtB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,aAAa;QACjB,UAAU;QACV,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,UAAU;QACV,SAAS;QACT,WAAW,IAAM,SAAS,QAAQ;IACpC;IAEA,MAAM,wBAAwB,kBAC5B,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,UAAU;oCAC1B,UAAU,CAAC,IAAM,YAAY;4CAAC,GAAG,QAAQ;4CAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wCAAA;oCACrE,WAAU;;;;;;;;;;;;sCAGd,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,SAAS,QAAQ;oCACxB,UAAU,CAAC,IAAM,YAAY;4CAAC,GAAG,QAAQ;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAA;oCACnE,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;;;;;;;;;;;;;sCAG1B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,YAAY;4CAAC,GAAG,QAAQ;4CAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wCAAA;oCAChE,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAQ;;;;;;sDACtB,6LAAC;4CAAO,OAAM;sDAAO;;;;;;sDACrB,6LAAC;4CAAO,OAAM;sDAAS;;;;;;;;;;;;;;;;;;sCAG3B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAAkE;;;;;;8CAGnF,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,eAAe;oCAC/B,UAAU,CAAC,IAAM,YAAY;4CAAC,GAAG,QAAQ;4CAAE,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACpF,WAAU;oCACV,KAAI;oCACJ,KAAI;;;;;;;;;;;;;;;;;;8BAKV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAC1D,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAI1D,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,SAAS,WAAW;oCAC7B,UAAU,CAAC,IAAM,YAAY;4CAAC,GAAG,QAAQ;4CAAE,aAAa,EAAE,MAAM,CAAC,OAAO;wCAAA;oCACxE,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,mBAAmB,kBACvB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;sCAI1D,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCACC,MAAK;oCACL,SAAS,SAAS,SAAS;oCAC3B,UAAU,CAAC,IAAM,YAAY;4CAAC,GAAG,QAAQ;4CAAE,WAAW,EAAE,MAAM,CAAC,OAAO;wCAAA;oCACtE,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;gBAIlB,SAAS,SAAS,kBACjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAmB,OAAO;oCAAoB,aAAa;gCAAgC;gCAClG;oCAAE,KAAK;oCAAoB,OAAO;oCAAqB,aAAa;gCAAyC;gCAC7G;oCAAE,KAAK;oCAAmB,OAAO;oCAAoB,aAAa;gCAA8B;gCAChG;oCAAE,KAAK;oCAAmB,OAAO;oCAAoB,aAAa;gCAA4B;6BAC/F,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;oCAAmB,WAAU;;sDAC5B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C,KAAK,KAAK;;;;;;8DACrE,6LAAC;oDAAE,WAAU;8DAA4C,KAAK,WAAW;;;;;;;;;;;;sDAE3E,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDACC,MAAK;oDACL,SAAS,QAAQ,CAAC,KAAK,GAAG,CAA0B;oDACpD,UAAU,CAAC,IAAM,YAAY;4DAAC,GAAG,QAAQ;4DAAE,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;wDAAA;oDACvE,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;mCAZT,KAAK,GAAG;;;;;;;;;;sCAkBtB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;;wCAAkE;wCAC1D,SAAS,mBAAmB;wCAAC;;;;;;;8CAEtD,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,OAAO,SAAS,mBAAmB;oCACnC,UAAU,CAAC,IAAM,YAAY;4CAAC,GAAG,QAAQ;4CAAE,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACxF,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQlB,MAAM,6BAA6B,kBACjC,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ;wBACC;4BAAE,KAAK;4BAAsB,OAAO;4BAAuB,MAAM,qMAAA,CAAA,OAAI;4BAAE,aAAa;wBAA2B;wBAC/G;4BAAE,KAAK;4BAAoB,OAAO;4BAAqB,MAAM,iNAAA,CAAA,aAAU;4BAAE,aAAa;wBAAyB;wBAC/G;4BAAE,KAAK;4BAAqB,OAAO;4BAAsB,MAAM,qMAAA,CAAA,OAAI;4BAAE,aAAa;wBAA6B;qBAChH,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;4BAAmB,WAAU;;8CAC5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA6C,KAAK,KAAK;;;;;;8DACrE,6LAAC;oDAAE,WAAU;8DAA4C,KAAK,WAAW;;;;;;;;;;;;;;;;;;8CAG7E,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,CAAC,KAAK,GAAG,CAA0B;4CACpD,UAAU,CAAC,IAAM,YAAY;oDAAC,GAAG,QAAQ;oDAAE,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO;gDAAA;4CACvE,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAfT,KAAK,GAAG;;;;;;;;;;8BAqBtB,6LAAC;;sCACC,6LAAC;4BAAM,WAAU;sCAAkE;;;;;;sCAGnF,6LAAC;4BACC,MAAK;4BACL,OAAO,SAAS,iBAAiB;4BACjC,UAAU,CAAC,IAAM,YAAY;oCAAC,GAAG,QAAQ;oCAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAAA;4BAC5E,WAAU;4BACV,aAAY;;;;;;;;;;;;;;;;;;IAMpB,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;QAKxD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAIlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;;kDAErC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;0CAET,uBACC;;sDACE,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAA8B;;mDAGnD,sBACF;;sDACE,6LAAC,8NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;iEAI1C;;sDACE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;0BAQ3C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6LAAC;gDAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;gDAC1C,WAAW,CAAC,iHAAiH,EAC3H,kBAAkB,QAAQ,EAAE,GACxB,+FACA,oCACJ;;kEAEF,6LAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;kEACxB,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;;+CAZnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAuB3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDACP,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB;;;;;;sDAEtD,6LAAC,mIAAA,CAAA,kBAAe;sDACb,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB;;;;;;;;;;;;8CAGxD,6LAAC,mIAAA,CAAA,cAAW;8CACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAnXwB;KAAA", "debugId": null}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "file": "database.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/database.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['ellipse', { cx: '12', cy: '5', rx: '9', ry: '3', key: 'msslwz' }],\n  ['path', { d: 'M3 5V19A9 3 0 0 0 21 19V5', key: '1wlel7' }],\n  ['path', { d: 'M3 12A9 3 0 0 0 21 12', key: 'mv7ke4' }],\n];\n\n/**\n * @component @name Database\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSI1IiByeD0iOSIgcnk9IjMiIC8+CiAgPHBhdGggZD0iTTMgNVYxOUE5IDMgMCAwIDAgMjEgMTlWNSIgLz4KICA8cGF0aCBkPSJNMyAxMkE5IDMgMCAwIDAgMjEgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/database\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Database = createLucideIcon('database', __iconNode);\n\nexport default Database;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAW,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1D;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAyB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACxD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1220, "column": 0}, "map": {"version": 3, "file": "mail.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,EAAK;YAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "file": "smartphone.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/smartphone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '14', height: '20', x: '5', y: '2', rx: '2', ry: '2', key: '1yt0o3' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n];\n\n/**\n * @component @name Smartphone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iMjAiIHg9IjUiIHk9IjIiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/smartphone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Smartphone = createLucideIcon('smartphone', __iconNode);\n\nexport default Smartphone;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "file": "save.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/save.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z',\n      key: '1c8476',\n    },\n  ],\n  ['path', { d: 'M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7', key: '1ydtos' }],\n  ['path', { d: 'M7 3v4a1 1 0 0 0 1 1h7', key: 't51u73' }],\n];\n\n/**\n * @component @name Save\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUuMiAzYTIgMiAwIDAgMSAxLjQuNmwzLjggMy44YTIgMiAwIDAgMSAuNiAxLjRWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWNWEyIDIgMCAwIDEgMi0yeiIgLz4KICA8cGF0aCBkPSJNMTcgMjF2LTdhMSAxIDAgMCAwLTEtMUg4YTEgMSAwIDAgMC0xIDF2NyIgLz4KICA8cGF0aCBkPSJNNyAzdjRhMSAxIDAgMCAwIDEgMWg3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/save\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Save = createLucideIcon('save', __iconNode);\n\nexport default Save;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "file": "refresh-cw.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/refresh-cw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8', key: 'v9h5vc' }],\n  ['path', { d: 'M21 3v5h-5', key: '1q7to0' }],\n  ['path', { d: 'M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16', key: '3uifl3' }],\n  ['path', { d: 'M8 16H3v5', key: '1cv678' }],\n];\n\n/**\n * @component @name RefreshCw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAwIDEgOS05IDkuNzUgOS43NSAwIDAgMSA2Ljc0IDIuNzRMMjEgOCIgLz4KICA8cGF0aCBkPSJNMjEgM3Y1aC01IiAvPgogIDxwYXRoIGQ9Ik0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNiIgLz4KICA8cGF0aCBkPSJNOCAxNkgzdjUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/refresh-cw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RefreshCw = createLucideIcon('refresh-cw', __iconNode);\n\nexport default RefreshCw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACpF;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
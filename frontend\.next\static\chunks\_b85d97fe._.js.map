{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-blue-600 text-white shadow-lg hover:bg-blue-700 hover:shadow-xl\",\n        destructive:\n          \"bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl\",\n        outline:\n          \"border border-gray-300 bg-transparent hover:bg-gray-50 hover:border-gray-400 dark:border-gray-600 dark:hover:bg-gray-800 dark:hover:border-gray-500\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700\",\n        ghost: \n          \"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100\",\n        link: \n          \"text-blue-600 underline-offset-4 hover:underline dark:text-blue-400\",\n        success:\n          \"bg-green-600 text-white shadow-lg hover:bg-green-700 hover:shadow-xl\",\n        warning:\n          \"bg-amber-600 text-white shadow-lg hover:bg-amber-700 hover:shadow-xl\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-12 rounded-lg px-8 text-base\",\n        xl: \"h-14 rounded-xl px-10 text-lg\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,iSACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MACE;YACF,SACE;YACF,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/api.ts"], "sourcesContent": ["// API Service Layer for Frontend-Backend Integration\n\nimport {\n  LoginRequest,\n  RegisterRequest,\n  AuthResponse,\n  RefreshTokenRequest,\n  Camera,\n  CreateCameraRequest,\n  UpdateCameraRequest,\n  CameraStats,\n  Event,\n  CreateEventRequest,\n  EventFilters,\n  AcknowledgeEventRequest,\n  ResolveEventRequest,\n  EventStats,\n  ApiResponse,\n  PaginatedResponse,\n  ApiError,\n  HealthStatus,\n  SystemStatus\n} from '@/types/api'\n\nclass ApiService {\n  private baseUrl: string\n  private accessToken: string | null = null\n  private refreshToken: string | null = null\n\n  constructor() {\n    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'\n    \n    // Load tokens from localStorage on initialization\n    if (typeof window !== 'undefined') {\n      this.accessToken = localStorage.getItem('accessToken')\n      this.refreshToken = localStorage.getItem('refreshToken')\n    }\n  }\n\n  // ===== PRIVATE HELPER METHODS =====\n  private async makeRequest<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<T> {\n    const url = `${this.baseUrl}${endpoint}`\n    \n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      ...options,\n    }\n\n    // Add authorization header if token exists\n    if (this.accessToken) {\n      config.headers = {\n        ...config.headers,\n        Authorization: `Bearer ${this.accessToken}`,\n      }\n    }\n\n    try {\n      const response = await fetch(url, config)\n      \n      // Handle 401 - try to refresh token\n      if (response.status === 401 && this.refreshToken) {\n        const refreshed = await this.refreshAccessToken()\n        if (refreshed) {\n          // Retry the original request with new token\n          config.headers = {\n            ...config.headers,\n            Authorization: `Bearer ${this.accessToken}`,\n          }\n          const retryResponse = await fetch(url, config)\n          return this.handleResponse<T>(retryResponse)\n        }\n      }\n\n      return this.handleResponse<T>(response)\n    } catch (error) {\n      console.error('API request failed:', error)\n      throw new Error('Network error occurred')\n    }\n  }\n\n  private async handleResponse<T>(response: Response): Promise<T> {\n    const contentType = response.headers.get('content-type')\n    const isJson = contentType?.includes('application/json')\n    \n    const data = isJson ? await response.json() : await response.text()\n\n    if (!response.ok) {\n      const error: ApiError = {\n        error: data.error || `HTTP ${response.status}`,\n        details: data.details,\n        status: response.status,\n      }\n      throw error\n    }\n\n    return data\n  }\n\n  private setTokens(accessToken: string, refreshToken: string) {\n    this.accessToken = accessToken\n    this.refreshToken = refreshToken\n    \n    if (typeof window !== 'undefined') {\n      localStorage.setItem('accessToken', accessToken)\n      localStorage.setItem('refreshToken', refreshToken)\n    }\n  }\n\n  private clearTokens() {\n    this.accessToken = null\n    this.refreshToken = null\n    \n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('accessToken')\n      localStorage.removeItem('refreshToken')\n    }\n  }\n\n  // ===== AUTHENTICATION METHODS =====\n  async login(credentials: LoginRequest): Promise<AuthResponse> {\n    const response = await this.makeRequest<AuthResponse>('/api/auth/login', {\n      method: 'POST',\n      body: JSON.stringify(credentials),\n    })\n\n    this.setTokens(response.tokens.accessToken, response.tokens.refreshToken)\n    return response\n  }\n\n  async register(userData: RegisterRequest): Promise<AuthResponse> {\n    const response = await this.makeRequest<AuthResponse>('/api/auth/register', {\n      method: 'POST',\n      body: JSON.stringify(userData),\n    })\n\n    this.setTokens(response.tokens.accessToken, response.tokens.refreshToken)\n    return response\n  }\n\n  async refreshAccessToken(): Promise<boolean> {\n    if (!this.refreshToken) return false\n\n    try {\n      const response = await this.makeRequest<AuthResponse>('/api/auth/refresh', {\n        method: 'POST',\n        body: JSON.stringify({ refreshToken: this.refreshToken }),\n      })\n\n      this.setTokens(response.tokens.accessToken, response.tokens.refreshToken)\n      return true\n    } catch (error) {\n      this.clearTokens()\n      return false\n    }\n  }\n\n  async logout(): Promise<void> {\n    this.clearTokens()\n  }\n\n  async getProfile(): Promise<AuthResponse['user']> {\n    const response = await this.makeRequest<{ user: AuthResponse['user'] }>('/api/auth/profile')\n    return response.user\n  }\n\n  // ===== CAMERA METHODS =====\n  async getCameras(filters?: {\n    status?: string\n    location?: string\n    limit?: number\n    page?: number\n  }): Promise<{ cameras: Camera[]; pagination?: any }> {\n    const params = new URLSearchParams()\n    if (filters?.status) params.append('status', filters.status)\n    if (filters?.location) params.append('location', filters.location)\n    if (filters?.limit) params.append('limit', filters.limit.toString())\n    if (filters?.page) params.append('page', filters.page.toString())\n\n    const queryString = params.toString()\n    const endpoint = `/api/cameras${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<{ cameras: Camera[]; pagination?: any }>(endpoint)\n  }\n\n  async getCamera(id: string): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`)\n    return response.camera\n  }\n\n  async createCamera(cameraData: CreateCameraRequest): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>('/api/cameras', {\n      method: 'POST',\n      body: JSON.stringify(cameraData),\n    })\n    return response.camera\n  }\n\n  async updateCamera(id: string, updates: UpdateCameraRequest): Promise<Camera> {\n    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(updates),\n    })\n    return response.camera\n  }\n\n  async deleteCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}`, {\n      method: 'DELETE',\n    })\n  }\n\n  async armCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}/arm`, {\n      method: 'POST',\n    })\n  }\n\n  async disarmCamera(id: string): Promise<void> {\n    await this.makeRequest(`/api/cameras/${id}/disarm`, {\n      method: 'POST',\n    })\n  }\n\n  async getCameraStats(): Promise<CameraStats> {\n    return this.makeRequest<CameraStats>('/api/cameras/stats')\n  }\n\n  // ===== EVENT METHODS =====\n  async getEvents(filters?: EventFilters): Promise<{ events: Event[]; pagination?: any }> {\n    const params = new URLSearchParams()\n    if (filters?.type) params.append('type', filters.type)\n    if (filters?.severity) params.append('severity', filters.severity)\n    if (filters?.status) params.append('status', filters.status)\n    if (filters?.cameraId) params.append('cameraId', filters.cameraId)\n    if (filters?.startDate) params.append('startDate', filters.startDate)\n    if (filters?.endDate) params.append('endDate', filters.endDate)\n    if (filters?.limit) params.append('limit', filters.limit.toString())\n    if (filters?.page) params.append('page', filters.page.toString())\n\n    const queryString = params.toString()\n    const endpoint = `/api/events${queryString ? `?${queryString}` : ''}`\n    \n    return this.makeRequest<{ events: Event[]; pagination?: any }>(endpoint)\n  }\n\n  async getEvent(id: string): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}`)\n    return response.event\n  }\n\n  async createEvent(eventData: CreateEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>('/api/events', {\n      method: 'POST',\n      body: JSON.stringify(eventData),\n    })\n    return response.event\n  }\n\n  async acknowledgeEvent(id: string, data: AcknowledgeEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/acknowledge`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n    return response.event\n  }\n\n  async resolveEvent(id: string, data: ResolveEventRequest): Promise<Event> {\n    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/resolve`, {\n      method: 'POST',\n      body: JSON.stringify(data),\n    })\n    return response.event\n  }\n\n  async getEventStats(): Promise<EventStats> {\n    return this.makeRequest<EventStats>('/api/events/stats')\n  }\n\n  // ===== SYSTEM METHODS =====\n  async getHealth(): Promise<HealthStatus> {\n    return this.makeRequest<HealthStatus>('/health')\n  }\n\n  async getSystemStatus(): Promise<SystemStatus> {\n    return this.makeRequest<SystemStatus>('/api/status')\n  }\n\n  // ===== UTILITY METHODS =====\n  isAuthenticated(): boolean {\n    return !!this.accessToken\n  }\n\n  getAccessToken(): string | null {\n    return this.accessToken\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService()\nexport default apiService\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AA8BlC;AANnB,MAAM;IACI,QAAe;IACf,cAA6B,KAAI;IACjC,eAA8B,KAAI;IAE1C,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;QAElD,kDAAkD;QAClD,wCAAmC;YACjC,IAAI,CAAC,WAAW,GAAG,aAAa,OAAO,CAAC;YACxC,IAAI,CAAC,YAAY,GAAG,aAAa,OAAO,CAAC;QAC3C;IACF;IAEA,qCAAqC;IACrC,MAAc,YACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACb;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,GAAG,OAAO;QACZ;QAEA,2CAA2C;QAC3C,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,OAAO,GAAG;gBACf,GAAG,OAAO,OAAO;gBACjB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;YAC7C;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,oCAAoC;YACpC,IAAI,SAAS,MAAM,KAAK,OAAO,IAAI,CAAC,YAAY,EAAE;gBAChD,MAAM,YAAY,MAAM,IAAI,CAAC,kBAAkB;gBAC/C,IAAI,WAAW;oBACb,4CAA4C;oBAC5C,OAAO,OAAO,GAAG;wBACf,GAAG,OAAO,OAAO;wBACjB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE;oBAC7C;oBACA,MAAM,gBAAgB,MAAM,MAAM,KAAK;oBACvC,OAAO,IAAI,CAAC,cAAc,CAAI;gBAChC;YACF;YAEA,OAAO,IAAI,CAAC,cAAc,CAAI;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAc,eAAkB,QAAkB,EAAc;QAC9D,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,MAAM,SAAS,aAAa,SAAS;QAErC,MAAM,OAAO,SAAS,MAAM,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI;QAEjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,QAAkB;gBACtB,OAAO,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;gBAC9C,SAAS,KAAK,OAAO;gBACrB,QAAQ,SAAS,MAAM;YACzB;YACA,MAAM;QACR;QAEA,OAAO;IACT;IAEQ,UAAU,WAAmB,EAAE,YAAoB,EAAE;QAC3D,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QAEpB,wCAAmC;YACjC,aAAa,OAAO,CAAC,eAAe;YACpC,aAAa,OAAO,CAAC,gBAAgB;QACvC;IACF;IAEQ,cAAc;QACpB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;QAEpB,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;IAEA,qCAAqC;IACrC,MAAM,MAAM,WAAyB,EAAyB;QAC5D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAe,mBAAmB;YACvE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,YAAY;QACxE,OAAO;IACT;IAEA,MAAM,SAAS,QAAyB,EAAyB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAe,sBAAsB;YAC1E,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,YAAY;QACxE,OAAO;IACT;IAEA,MAAM,qBAAuC;QAC3C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO;QAE/B,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAe,qBAAqB;gBACzE,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;oBAAE,cAAc,IAAI,CAAC,YAAY;gBAAC;YACzD;YAEA,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,MAAM,CAAC,YAAY;YACxE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,CAAC,WAAW;YAChB,OAAO;QACT;IACF;IAEA,MAAM,SAAwB;QAC5B,IAAI,CAAC,WAAW;IAClB;IAEA,MAAM,aAA4C;QAChD,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAiC;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,WAAW,OAKhB,EAAoD;QACnD,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAE9D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAEtE,OAAO,IAAI,CAAC,WAAW,CAA0C;IACnE;IAEA,MAAM,UAAU,EAAU,EAAmB;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,CAAC,aAAa,EAAE,IAAI;QAChF,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,UAA+B,EAAmB;QACnE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,gBAAgB;YAC1E,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,EAAU,EAAE,OAA4B,EAAmB;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAqB,CAAC,aAAa,EAAE,IAAI,EAAE;YAChF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,MAAM;IACxB;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;YAC3C,QAAQ;QACV;IACF;IAEA,MAAM,UAAU,EAAU,EAAiB;QACzC,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,EAAE;YAC/C,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,aAAa,EAAE,GAAG,OAAO,CAAC,EAAE;YAClD,QAAQ;QACV;IACF;IAEA,MAAM,iBAAuC;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAc;IACvC;IAEA,4BAA4B;IAC5B,MAAM,UAAU,OAAsB,EAAkD;QACtF,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI;QACrD,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;QACpE,IAAI,SAAS,SAAS,OAAO,MAAM,CAAC,WAAW,QAAQ,OAAO;QAC9D,IAAI,SAAS,OAAO,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QACjE,IAAI,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAE9D,MAAM,cAAc,OAAO,QAAQ;QACnC,MAAM,WAAW,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAErE,OAAO,IAAI,CAAC,WAAW,CAAwC;IACjE;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,IAAI;QAC7E,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,YAAY,SAA6B,EAAkB;QAC/D,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,eAAe;YACvE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,iBAAiB,EAAU,EAAE,IAA6B,EAAkB;QAChF,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,GAAG,YAAY,CAAC,EAAE;YACzF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,aAAa,EAAU,EAAE,IAAyB,EAAkB;QACxE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAmB,CAAC,YAAY,EAAE,GAAG,QAAQ,CAAC,EAAE;YACrF,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;QACA,OAAO,SAAS,KAAK;IACvB;IAEA,MAAM,gBAAqC;QACzC,OAAO,IAAI,CAAC,WAAW,CAAa;IACtC;IAEA,6BAA6B;IAC7B,MAAM,YAAmC;QACvC,OAAO,IAAI,CAAC,WAAW,CAAe;IACxC;IAEA,MAAM,kBAAyC;QAC7C,OAAO,IAAI,CAAC,WAAW,CAAe;IACxC;IAEA,8BAA8B;IAC9B,kBAA2B;QACzB,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW;IAC3B;IAEA,iBAAgC;QAC9B,OAAO,IAAI,CAAC,WAAW;IACzB;AACF;AAGO,MAAM,aAAa,IAAI;uCACf", "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/events/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from \"react\"\nimport { motion } from \"framer-motion\"\nimport {\n  Activity,\n  AlertTriangle,\n  CheckCircle,\n  Clock,\n  Eye,\n  Download,\n  Filter,\n  Search,\n  Calendar,\n  Camera,\n  User,\n  Car,\n  Shield,\n  Loader2,\n  AlertCircle\n} from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport { apiService } from \"@/lib/api\"\nimport { Event as EventType } from \"@/types/api\"\n\n// Helper function to format timestamp\nconst formatTimestamp = (timestamp: string): string => {\n  const date = new Date(timestamp)\n  const now = new Date()\n  const diffMs = now.getTime() - date.getTime()\n  const diffMinutes = Math.floor(diffMs / (1000 * 60))\n  const diffHours = Math.floor(diffMinutes / 60)\n  const diffDays = Math.floor(diffHours / 24)\n\n  if (diffMinutes < 1) return \"Just now\"\n  if (diffMinutes < 60) return `${diffMinutes}m ago`\n  if (diffHours < 24) return `${diffHours}h ago`\n  if (diffDays < 7) return `${diffDays}d ago`\n  return date.toLocaleDateString()\n}\n\nexport default function EventsPage() {\n  // State management\n  const [events, setEvents] = useState<EventType[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [searchTerm, setSearchTerm] = useState(\"\")\n  const [statusFilter, setStatusFilter] = useState<\"all\" | \"active\" | \"acknowledged\" | \"resolved\">(\"all\")\n  const [severityFilter, setSeverityFilter] = useState<\"all\" | \"low\" | \"medium\" | \"high\" | \"critical\">(\"all\")\n  const [typeFilter, setTypeFilter] = useState<\"all\" | \"person\" | \"vehicle\" | \"motion\" | \"face\" | \"intrusion\">(\"all\")\n  const [acknowledgingEvent, setAcknowledgingEvent] = useState<string | null>(null)\n  const [resolvingEvent, setResolvingEvent] = useState<string | null>(null)\n\n  // Load events on component mount\n  useEffect(() => {\n    loadEvents()\n  }, [])\n\n  const loadEvents = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n      const response = await apiService.getEvents()\n      setEvents(response.events || [])\n    } catch (err: any) {\n      console.error('Failed to load events:', err)\n      setError(err.error || 'Failed to load events')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // Handle event acknowledgment\n  const handleAcknowledgeEvent = async (eventId: string) => {\n    try {\n      setAcknowledgingEvent(eventId)\n      await apiService.acknowledgeEvent(eventId, {\n        notes: \"Acknowledged from dashboard\"\n      })\n      await loadEvents() // Refresh events\n    } catch (err: any) {\n      console.error('Failed to acknowledge event:', err)\n      setError(err.error || 'Failed to acknowledge event')\n    } finally {\n      setAcknowledgingEvent(null)\n    }\n  }\n\n  // Handle event resolution\n  const handleResolveEvent = async (eventId: string) => {\n    try {\n      setResolvingEvent(eventId)\n      await apiService.resolveEvent(eventId, {\n        resolution: \"Resolved from dashboard\",\n        notes: \"Event resolved by operator\"\n      })\n      await loadEvents() // Refresh events\n    } catch (err: any) {\n      console.error('Failed to resolve event:', err)\n      setError(err.error || 'Failed to resolve event')\n    } finally {\n      setResolvingEvent(null)\n    }\n  }\n\n  // Filter events based on search and filters\n  const filteredEvents = events.filter(event => {\n    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         event.description.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesStatus = statusFilter === \"all\" || event.status === statusFilter\n    const matchesSeverity = severityFilter === \"all\" || event.severity === severityFilter\n    const matchesType = typeFilter === \"all\" || event.type === typeFilter\n    return matchesSearch && matchesStatus && matchesSeverity && matchesType\n  })\n\n  // Loading state\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"flex items-center space-x-2\">\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\n          <span>Loading events...</span>\n        </div>\n      </div>\n    )\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"text-center\">\n          <AlertCircle className=\"h-12 w-12 text-red-500 mx-auto mb-4\" />\n          <h3 className=\"text-lg font-semibold mb-2\">Failed to Load Events</h3>\n          <p className=\"text-gray-600 mb-4\">{error}</p>\n          <Button onClick={loadEvents}>\n            Try Again\n          </Button>\n        </div>\n      </div>\n    )\n  }\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case \"active\":\n        return <Badge variant=\"destructive\" size=\"sm\">Active</Badge>\n      case \"resolved\":\n        return <Badge variant=\"success\" size=\"sm\">Resolved</Badge>\n      case \"acknowledged\":\n        return <Badge variant=\"warning\" size=\"sm\">Acknowledged</Badge>\n      default:\n        return <Badge variant=\"secondary\" size=\"sm\">Unknown</Badge>\n    }\n  }\n\n  const getSeverityBadge = (severity: string) => {\n    switch (severity) {\n      case \"critical\":\n        return <Badge variant=\"destructive\" size=\"sm\">Critical</Badge>\n      case \"high\":\n        return <Badge variant=\"warning\" size=\"sm\">High</Badge>\n      case \"medium\":\n        return <Badge variant=\"default\" size=\"sm\">Medium</Badge>\n      case \"low\":\n        return <Badge variant=\"secondary\" size=\"sm\">Low</Badge>\n      default:\n        return <Badge variant=\"secondary\" size=\"sm\">Unknown</Badge>\n    }\n  }\n\n  const getEventIcon = (type: string) => {\n    switch (type) {\n      case \"person\":\n        return <User className=\"h-5 w-5\" />\n      case \"vehicle\":\n        return <Car className=\"h-5 w-5\" />\n      case \"motion\":\n        return <Activity className=\"h-5 w-5\" />\n      case \"face\":\n        return <Eye className=\"h-5 w-5\" />\n      case \"intrusion\":\n        return <Shield className=\"h-5 w-5\" />\n      case \"object\":\n        return <AlertTriangle className=\"h-5 w-5\" />\n      default:\n        return <Activity className=\"h-5 w-5\" />\n    }\n  }\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffMs = now.getTime() - date.getTime()\n    const diffMins = Math.floor(diffMs / 60000)\n    const diffHours = Math.floor(diffMins / 60)\n    const diffDays = Math.floor(diffHours / 24)\n\n    if (diffMins < 1) return \"Just now\"\n    if (diffMins < 60) return `${diffMins} minutes ago`\n    if (diffHours < 24) return `${diffHours} hours ago`\n    if (diffDays < 7) return `${diffDays} days ago`\n    return date.toLocaleDateString()\n  }\n\n  return (\n    <div className=\"flex-1 space-y-6 p-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n            Security Events\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Monitor and analyze security events from all cameras\n          </p>\n        </div>\n        <div className=\"flex space-x-3\">\n          <Button variant=\"outline\">\n            <Download className=\"mr-2 h-4 w-4\" />\n            Export\n          </Button>\n          <Button variant=\"outline\">\n            <Calendar className=\"mr-2 h-4 w-4\" />\n            Date Range\n          </Button>\n        </div>\n      </div>\n\n      {/* Filters */}\n      <div className=\"flex flex-wrap items-center gap-4\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search events...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n        \n        <select\n          value={statusFilter}\n          onChange={(e) => setStatusFilter(e.target.value as any)}\n          className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Status</option>\n          <option value=\"active\">Active</option>\n          <option value=\"acknowledged\">Acknowledged</option>\n          <option value=\"resolved\">Resolved</option>\n        </select>\n\n        <select\n          value={severityFilter}\n          onChange={(e) => setSeverityFilter(e.target.value as any)}\n          className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Severity</option>\n          <option value=\"critical\">Critical</option>\n          <option value=\"high\">High</option>\n          <option value=\"medium\">Medium</option>\n          <option value=\"low\">Low</option>\n        </select>\n\n        <select\n          value={typeFilter}\n          onChange={(e) => setTypeFilter(e.target.value as any)}\n          className=\"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        >\n          <option value=\"all\">All Types</option>\n          <option value=\"person\">Person</option>\n          <option value=\"vehicle\">Vehicle</option>\n          <option value=\"motion\">Motion</option>\n          <option value=\"face\">Face</option>\n          <option value=\"intrusion\">Intrusion</option>\n        </select>\n\n        <div className=\"ml-auto text-sm text-gray-600 dark:text-gray-400\">\n          {filteredEvents.length} of {events.length} events\n        </div>\n      </div>\n\n      {/* Events List */}\n      <div className=\"space-y-4\">\n        {filteredEvents.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Activity className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">\n              No Events Found\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400\">\n              {events.length === 0\n                ? \"No events have been recorded yet.\"\n                : \"No events match your current filters.\"}\n            </p>\n          </div>\n        ) : (\n          filteredEvents.map((event, index) => (\n            <motion.div\n              key={event._id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n            >\n              <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-start space-x-4\">\n                      <div className=\"p-2 rounded-lg bg-blue-100 dark:bg-blue-900\">\n                        {getEventIcon(event.type)}\n                      </div>\n                      <div className=\"flex-1 space-y-2\">\n                        <div className=\"flex items-center space-x-3\">\n                          <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                            {event.title}\n                          </h3>\n                          {getStatusBadge(event.status)}\n                          {getSeverityBadge(event.severity)}\n                          <Badge variant=\"outline\" size=\"sm\">\n                            {event.confidence}% confidence\n                          </Badge>\n                        </div>\n                        <p className=\"text-gray-600 dark:text-gray-400\">\n                          {event.description}\n                        </p>\n                        <div className=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n                          <span className=\"flex items-center space-x-1\">\n                            <Camera className=\"h-4 w-4\" />\n                            <span>{event.cameraName || 'Unknown Camera'}</span>\n                          </span>\n                          <span>{event.location || 'Unknown Location'}</span>\n                          <span>{formatTimestamp(event.timestamp)}</span>\n                        </div>\n                        {event.aiAnalysis && (\n                          <div className=\"mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                            <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n                              <strong>AI Analysis:</strong> {event.aiAnalysis}\n                            </p>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-2\">\n                      {event.status === 'active' && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleAcknowledgeEvent(event._id)}\n                          disabled={acknowledgingEvent === event._id}\n                        >\n                          {acknowledgingEvent === event._id ? (\n                            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                          ) : (\n                            <CheckCircle className=\"mr-2 h-4 w-4\" />\n                          )}\n                          Acknowledge\n                        </Button>\n                      )}\n                      {(event.status === 'active' || event.status === 'acknowledged') && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => handleResolveEvent(event._id)}\n                          disabled={resolvingEvent === event._id}\n                        >\n                          {resolvingEvent === event._id ? (\n                            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                          ) : (\n                            <Shield className=\"mr-2 h-4 w-4\" />\n                          )}\n                          Resolve\n                        </Button>\n                      )}\n                      <Button variant=\"outline\" size=\"sm\">\n                        <Eye className=\"mr-2 h-4 w-4\" />\n                        View\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;AACA;;;AAxBA;;;;;;;;AA2BA,sCAAsC;AACtC,MAAM,kBAAkB,CAAC;IACvB,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,MAAM,IAAI;IAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;IAC3C,MAAM,cAAc,KAAK,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;IAClD,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc;IAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;IAExC,IAAI,cAAc,GAAG,OAAO;IAC5B,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;IAClD,IAAI,YAAY,IAAI,OAAO,GAAG,UAAU,KAAK,CAAC;IAC9C,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,KAAK,CAAC;IAC3C,OAAO,KAAK,kBAAkB;AAChC;AAEe,SAAS;;IACtB,mBAAmB;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IACjG,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IACrG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkE;IAC7G,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,oHAAA,CAAA,aAAU,CAAC,SAAS;YAC3C,UAAU,SAAS,MAAM,IAAI,EAAE;QACjC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS,IAAI,KAAK,IAAI;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,8BAA8B;IAC9B,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,sBAAsB;YACtB,MAAM,oHAAA,CAAA,aAAU,CAAC,gBAAgB,CAAC,SAAS;gBACzC,OAAO;YACT;YACA,MAAM,aAAa,iBAAiB;;QACtC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,IAAI,KAAK,IAAI;QACxB,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,kBAAkB;YAClB,MAAM,oHAAA,CAAA,aAAU,CAAC,YAAY,CAAC,SAAS;gBACrC,YAAY;gBACZ,OAAO;YACT;YACA,MAAM,aAAa,iBAAiB;;QACtC,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,IAAI,KAAK,IAAI;QACxB,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,4CAA4C;IAC5C,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA;QACnC,MAAM,gBAAgB,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACpF,MAAM,gBAAgB,iBAAiB,SAAS,MAAM,MAAM,KAAK;QACjE,MAAM,kBAAkB,mBAAmB,SAAS,MAAM,QAAQ,KAAK;QACvE,MAAM,cAAc,eAAe,SAAS,MAAM,IAAI,KAAK;QAC3D,OAAO,iBAAiB,iBAAiB,mBAAmB;IAC9D;IAEA,gBAAgB;IAChB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;kCAAY;;;;;;;;;;;;;;;;;IAMrC;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,MAAK;8BAAK;;;;;;YAChD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,MAAK;8BAAK;;;;;;QAChD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,MAAK;8BAAK;;;;;;YAChD,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAU,MAAK;8BAAK;;;;;;YAC5C,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,MAAK;8BAAK;;;;;;YAC9C;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAY,MAAK;8BAAK;;;;;;QAChD;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;YACxB,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,WAAW,KAAK,KAAK,CAAC,SAAS;QACrC,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;QACxC,MAAM,WAAW,KAAK,KAAK,CAAC,YAAY;QAExC,IAAI,WAAW,GAAG,OAAO;QACzB,IAAI,WAAW,IAAI,OAAO,GAAG,SAAS,YAAY,CAAC;QACnD,IAAI,YAAY,IAAI,OAAO,GAAG,UAAU,UAAU,CAAC;QACnD,IAAI,WAAW,GAAG,OAAO,GAAG,SAAS,SAAS,CAAC;QAC/C,OAAO,KAAK,kBAAkB;IAChC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAIlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAId,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBAC/C,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,6LAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,6LAAC;gCAAO,OAAM;0CAAe;;;;;;0CAC7B,6LAAC;gCAAO,OAAM;0CAAW;;;;;;;;;;;;kCAG3B,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,6LAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,6LAAC;gCAAO,OAAM;0CAAM;;;;;;;;;;;;kCAGtB,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,6LAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,6LAAC;gCAAO,OAAM;0CAAU;;;;;;0CACxB,6LAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAY;;;;;;;;;;;;kCAG5B,6LAAC;wBAAI,WAAU;;4BACZ,eAAe,MAAM;4BAAC;4BAAK,OAAO,MAAM;4BAAC;;;;;;;;;;;;;0BAK9C,6LAAC;gBAAI,WAAU;0BACZ,eAAe,MAAM,KAAK,kBACzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,6LAAC;4BAAE,WAAU;sCACV,OAAO,MAAM,KAAK,IACf,sCACA;;;;;;;;;;;2BAIR,eAAe,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAK;kCAElC,cAAA,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,aAAa,MAAM,IAAI;;;;;;8DAE1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EACX,MAAM,KAAK;;;;;;gEAEb,eAAe,MAAM,MAAM;gEAC3B,iBAAiB,MAAM,QAAQ;8EAChC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,MAAK;;wEAC3B,MAAM,UAAU;wEAAC;;;;;;;;;;;;;sEAGtB,6LAAC;4DAAE,WAAU;sEACV,MAAM,WAAW;;;;;;sEAEpB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC,yMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,6LAAC;sFAAM,MAAM,UAAU,IAAI;;;;;;;;;;;;8EAE7B,6LAAC;8EAAM,MAAM,QAAQ,IAAI;;;;;;8EACzB,6LAAC;8EAAM,gBAAgB,MAAM,SAAS;;;;;;;;;;;;wDAEvC,MAAM,UAAU,kBACf,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAU;;kFACX,6LAAC;kFAAO;;;;;;oEAAqB;oEAAE,MAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAMzD,6LAAC;4CAAI,WAAU;;gDACZ,MAAM,MAAM,KAAK,0BAChB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,uBAAuB,MAAM,GAAG;oDAC/C,UAAU,uBAAuB,MAAM,GAAG;;wDAEzC,uBAAuB,MAAM,GAAG,iBAC/B,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDACvB;;;;;;;gDAIL,CAAC,MAAM,MAAM,KAAK,YAAY,MAAM,MAAM,KAAK,cAAc,mBAC5D,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,mBAAmB,MAAM,GAAG;oDAC3C,UAAU,mBAAmB,MAAM,GAAG;;wDAErC,mBAAmB,MAAM,GAAG,iBAC3B,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAClB;;;;;;;8DAIN,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAU,MAAK;;sEAC7B,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA3ErC,MAAM,GAAG;;;;;;;;;;;;;;;;AAwF5B;GA3VwB;KAAA", "debugId": null}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "file": "triangle-alert.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC7C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1522, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1570, "column": 0}, "map": {"version": 3, "file": "download.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 15V3', key: 'm9g1x1' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['path', { d: 'm7 10 5 5 5-5', key: 'brsn70' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTVWMyIgLz4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('download', __iconNode);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "file": "calendar.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1735, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1783, "column": 0}, "map": {"version": 3, "file": "car.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/car.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2',\n      key: '5owen',\n    },\n  ],\n  ['circle', { cx: '7', cy: '17', r: '2', key: 'u2ysq9' }],\n  ['path', { d: 'M9 17h6', key: 'r8uit2' }],\n  ['circle', { cx: '17', cy: '17', r: '2', key: 'axvx0g' }],\n];\n\n/**\n * @component @name Car\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTdoMmMuNiAwIDEtLjQgMS0xdi0zYzAtLjktLjctMS43LTEuNS0xLjlDMTguNyAxMC42IDE2IDEwIDE2IDEwcy0xLjMtMS40LTIuMi0yLjNjLS41LS40LTEuMS0uNy0xLjgtLjdINWMtLjYgMC0xLjEuNC0xLjQuOWwtMS40IDIuOUEzLjcgMy43IDAgMCAwIDIgMTJ2NGMwIC42LjQgMSAxIDFoMiIgLz4KICA8Y2lyY2xlIGN4PSI3IiBjeT0iMTciIHI9IjIiIC8+CiAgPHBhdGggZD0iTTkgMTdoNiIgLz4KICA8Y2lyY2xlIGN4PSIxNyIgY3k9IjE3IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/car\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Car = createLucideIcon('car', __iconNode);\n\nexport default Car;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1847, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,6BAA+B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n  }).format(new Date(date))\n}\n\nexport function formatFileSize(bytes: number): string {\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\"]\n  if (bytes === 0) return \"0 Bytes\"\n  const i = Math.floor(Math.log(bytes) / Math.log(1024))\n  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + \" \" + sizes[i]\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300\",\n        secondary:\n          \"border-transparent bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300\",\n        destructive:\n          \"border-transparent bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300\",\n        success:\n          \"border-transparent bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300\",\n        warning:\n          \"border-transparent bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300\",\n        outline: \n          \"text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600\",\n        purple:\n          \"border-transparent bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300\",\n        pink:\n          \"border-transparent bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300\",\n      },\n      size: {\n        default: \"px-2.5 py-0.5 text-xs\",\n        sm: \"px-2 py-0.5 text-xs\",\n        lg: \"px-3 py-1 text-sm\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, size, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant, size }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;YACF,QACE;YACF,MACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAmB;IAC/D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAAa,GAAG,KAAK;;;;;;AAE9E;KAJS", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport React from \"react\"\nimport Link from \"next/link\"\nimport { usePathname } from \"next/navigation\"\nimport { motion } from \"framer-motion\"\nimport { \n  LayoutDashboard, \n  Camera, \n  Activity, \n  Settings, \n  Users, \n  Bell,\n  BarChart3,\n  Shield,\n  Zap\n} from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { Badge } from \"@/components/ui/badge\"\n\nconst navigation = [\n  {\n    name: \"Dashboard\",\n    href: \"/\",\n    icon: LayoutDashboard,\n    badge: null,\n  },\n  {\n    name: \"Live Events\",\n    href: \"/events\",\n    icon: Activity,\n    badge: \"12\",\n  },\n  {\n    name: \"Cameras\",\n    href: \"/cameras\",\n    icon: Camera,\n    badge: null,\n  },\n  {\n    name: \"Analytics\",\n    href: \"/analytics\",\n    icon: BarChart3,\n    badge: null,\n  },\n  {\n    name: \"AI Models\",\n    href: \"/ai-models\",\n    icon: Zap,\n    badge: \"New\",\n  },\n  {\n    name: \"Notifications\",\n    href: \"/notifications\",\n    icon: Bell,\n    badge: \"3\",\n  },\n  {\n    name: \"Users\",\n    href: \"/users\",\n    icon: Users,\n    badge: null,\n  },\n  {\n    name: \"Security\",\n    href: \"/security\",\n    icon: Shield,\n    badge: null,\n  },\n  {\n    name: \"Settings\",\n    href: \"/settings\",\n    icon: Settings,\n    badge: null,\n  },\n]\n\ninterface SidebarProps {\n  className?: string\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const pathname = usePathname()\n\n  return (\n    <div className={cn(\"flex h-full w-64 flex-col bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-800\", className)}>\n      {/* Logo */}\n      <div className=\"flex h-16 items-center px-6 border-b border-gray-200 dark:border-gray-800\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500 to-purple-600\">\n            <Camera className=\"h-5 w-5 text-white\" />\n          </div>\n          <div>\n            <h1 className=\"text-lg font-bold text-gray-900 dark:text-white\">\n              HikVision AI\n            </h1>\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n              Monitoring System\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 space-y-1 px-3 py-4\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href\n          return (\n            <Link key={item.name} href={item.href}>\n              <motion.div\n                className={cn(\n                  \"group flex items-center justify-between rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200\",\n                  isActive\n                    ? \"bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400\"\n                    : \"text-gray-700 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white\"\n                )}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <item.icon\n                    className={cn(\n                      \"h-5 w-5 transition-colors\",\n                      isActive\n                        ? \"text-blue-600 dark:text-blue-400\"\n                        : \"text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300\"\n                    )}\n                  />\n                  <span>{item.name}</span>\n                </div>\n                {item.badge && (\n                  <Badge \n                    variant={isActive ? \"default\" : \"secondary\"} \n                    size=\"sm\"\n                    className=\"ml-auto\"\n                  >\n                    {item.badge}\n                  </Badge>\n                )}\n              </motion.div>\n            </Link>\n          )\n        })}\n      </nav>\n\n      {/* Status Indicator */}\n      <div className=\"border-t border-gray-200 dark:border-gray-800 p-4\">\n        <div className=\"flex items-center space-x-3 rounded-lg bg-green-50 dark:bg-green-900/20 p-3\">\n          <div className=\"flex h-2 w-2 items-center justify-center\">\n            <div className=\"h-2 w-2 rounded-full bg-green-500 animate-pulse\" />\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-sm font-medium text-green-800 dark:text-green-400\">\n              System Online\n            </p>\n            <p className=\"text-xs text-green-600 dark:text-green-500\">\n              All services running\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;;;AAlBA;;;;;;;AAoBA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+NAAA,CAAA,kBAAe;QACrB,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;QACf,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;IACT;CACD;AAMM,SAAS,QAAQ,EAAE,SAAS,EAAgB;;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qGAAqG;;0BAEtH,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAGhE,6LAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;0BAQ9D,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI;oBACvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;wBAAiB,MAAM,KAAK,IAAI;kCACnC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kHACA,WACI,oEACA;4BAEN,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,KAAK,IAAI;4CACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,WACI,qCACA;;;;;;sDAGR,6LAAC;sDAAM,KAAK,IAAI;;;;;;;;;;;;gCAEjB,KAAK,KAAK,kBACT,6LAAC,oIAAA,CAAA,QAAK;oCACJ,SAAS,WAAW,YAAY;oCAChC,MAAK;oCACL,WAAU;8CAET,KAAK,KAAK;;;;;;;;;;;;uBA5BR,KAAK,IAAI;;;;;gBAkCxB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAyD;;;;;;8CAGtE,6LAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;GAlFgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}]}
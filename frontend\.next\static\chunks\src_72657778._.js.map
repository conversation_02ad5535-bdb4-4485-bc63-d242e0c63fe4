{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat(\"en-US\", {\n    year: \"numeric\",\n    month: \"short\",\n    day: \"numeric\",\n    hour: \"2-digit\",\n    minute: \"2-digit\",\n  }).format(new Date(date))\n}\n\nexport function formatFileSize(bytes: number): string {\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\", \"TB\"]\n  if (bytes === 0) return \"0 Bytes\"\n  const i = Math.floor(Math.log(bytes) / Math.log(1024))\n  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + \" \" + sizes[i]\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,KAAa;IAC1C,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;AAC3E;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-200 hover:shadow-md dark:border-gray-800 dark:bg-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900 dark:text-gray-100\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600 dark:text-gray-400\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-blue-600 text-white shadow-lg hover:bg-blue-700 hover:shadow-xl\",\n        destructive:\n          \"bg-red-600 text-white shadow-lg hover:bg-red-700 hover:shadow-xl\",\n        outline:\n          \"border border-gray-300 bg-transparent hover:bg-gray-50 hover:border-gray-400 dark:border-gray-600 dark:hover:bg-gray-800 dark:hover:border-gray-500\",\n        secondary:\n          \"bg-gray-100 text-gray-900 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100 dark:hover:bg-gray-700\",\n        ghost: \n          \"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100\",\n        link: \n          \"text-blue-600 underline-offset-4 hover:underline dark:text-blue-400\",\n        success:\n          \"bg-green-600 text-white shadow-lg hover:bg-green-700 hover:shadow-xl\",\n        warning:\n          \"bg-amber-600 text-white shadow-lg hover:bg-amber-700 hover:shadow-xl\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-12 rounded-lg px-8 text-base\",\n        xl: \"h-14 rounded-xl px-10 text-lg\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"mr-2 h-4 w-4 animate-spin\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            />\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            />\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,iSACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MACE;YACF,SACE;YACF,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,6LAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,6LAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AUGMENT%20NEW%20XML%20HIKVISION/frontend/src/app/register/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState } from \"react\"\nimport { motion } from \"framer-motion\"\nimport { Eye, EyeOff, Loader2, Shield, AlertCircle, CheckCircle } from \"lucide-react\"\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { Button } from \"@/components/ui/button\"\nimport { useAuth } from \"@/contexts/AuthContext\"\nimport { useRouter } from \"next/navigation\"\n\nexport default function RegisterPage() {\n  const router = useRouter()\n  const { register } = useAuth()\n  const [formData, setFormData] = useState({\n    firstName: \"\",\n    lastName: \"\",\n    username: \"\",\n    email: \"\",\n    password: \"\",\n    confirmPassword: \"\"\n  })\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState<string | null>(null)\n  const [success, setSuccess] = useState(false)\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n    // Clear error when user starts typing\n    if (error) setError(null)\n  }\n\n  const validateForm = () => {\n    if (!formData.firstName || !formData.lastName || !formData.username || !formData.email || !formData.password || !formData.confirmPassword) {\n      setError(\"Please fill in all fields\")\n      return false\n    }\n\n    if (formData.password !== formData.confirmPassword) {\n      setError(\"Passwords do not match\")\n      return false\n    }\n\n    if (formData.password.length < 6) {\n      setError(\"Password must be at least 6 characters long\")\n      return false\n    }\n\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (!emailRegex.test(formData.email)) {\n      setError(\"Please enter a valid email address\")\n      return false\n    }\n\n    return true\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!validateForm()) return\n\n    try {\n      setLoading(true)\n      setError(null)\n      \n      await register({\n        firstName: formData.firstName,\n        lastName: formData.lastName,\n        username: formData.username,\n        email: formData.email,\n        password: formData.password\n      })\n\n      console.log(\"Registration successful\")\n      setSuccess(true)\n      \n      // Redirect to dashboard after a short delay\n      setTimeout(() => {\n        router.push(\"/\")\n      }, 2000)\n    } catch (err: any) {\n      console.error(\"Registration failed:\", err)\n      setError(err.error || \"Registration failed. Please try again.\")\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (success) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5 }}\n          className=\"w-full max-w-md\"\n        >\n          <Card className=\"shadow-xl border-0 text-center\">\n            <CardContent className=\"pt-8 pb-8\">\n              <div className=\"mx-auto mb-4 p-3 bg-green-100 dark:bg-green-900 rounded-full w-fit\">\n                <CheckCircle className=\"h-8 w-8 text-green-600 dark:text-green-400\" />\n              </div>\n              <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                Registration Successful!\n              </h2>\n              <p className=\"text-gray-600 dark:text-gray-400 mb-4\">\n                Your account has been created successfully. Redirecting to dashboard...\n              </p>\n              <div className=\"flex justify-center\">\n                <Loader2 className=\"h-5 w-5 animate-spin text-green-600\" />\n              </div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n        className=\"w-full max-w-md\"\n      >\n        <Card className=\"shadow-xl border-0\">\n          <CardHeader className=\"text-center pb-6\">\n            <div className=\"mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit\">\n              <Shield className=\"h-8 w-8 text-blue-600 dark:text-blue-400\" />\n            </div>\n            <CardTitle className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              Create Account\n            </CardTitle>\n            <CardDescription className=\"text-gray-600 dark:text-gray-400\">\n              Join the HikVision AI Dashboard\n            </CardDescription>\n          </CardHeader>\n          \n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\n              {error && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.95 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  className=\"flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\"\n                >\n                  <AlertCircle className=\"h-4 w-4 text-red-500\" />\n                  <span className=\"text-sm text-red-700 dark:text-red-400\">{error}</span>\n                </motion.div>\n              )}\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"firstName\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    First Name\n                  </label>\n                  <input\n                    id=\"firstName\"\n                    name=\"firstName\"\n                    type=\"text\"\n                    required\n                    value={formData.firstName}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                    placeholder=\"First name\"\n                  />\n                </div>\n\n                <div className=\"space-y-2\">\n                  <label htmlFor=\"lastName\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Last Name\n                  </label>\n                  <input\n                    id=\"lastName\"\n                    name=\"lastName\"\n                    type=\"text\"\n                    required\n                    value={formData.lastName}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                    placeholder=\"Last name\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"username\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Username\n                </label>\n                <input\n                  id=\"username\"\n                  name=\"username\"\n                  type=\"text\"\n                  required\n                  value={formData.username}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  placeholder=\"Choose a username\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"email\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Email Address\n                </label>\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"password\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"password\"\n                    name=\"password\"\n                    type={showPassword ? \"text\" : \"password\"}\n                    required\n                    value={formData.password}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                    placeholder=\"Create a password\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowPassword(!showPassword)}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                  >\n                    {showPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"space-y-2\">\n                <label htmlFor=\"confirmPassword\" className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n                  Confirm Password\n                </label>\n                <div className=\"relative\">\n                  <input\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? \"text\" : \"password\"}\n                    required\n                    value={formData.confirmPassword}\n                    onChange={handleInputChange}\n                    className=\"w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                    placeholder=\"Confirm your password\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                  >\n                    {showConfirmPassword ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\n                  </button>\n                </div>\n              </div>\n\n              <Button\n                type=\"submit\"\n                disabled={loading}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors\"\n              >\n                {loading ? (\n                  <>\n                    <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                    Creating Account...\n                  </>\n                ) : (\n                  \"Create Account\"\n                )}\n              </Button>\n            </form>\n\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Already have an account?{\" \"}\n                <button\n                  onClick={() => router.push(\"/login\")}\n                  className=\"text-blue-600 dark:text-blue-400 hover:underline font-medium\"\n                >\n                  Sign in here\n                </button>\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;QACV,iBAAiB;IACnB;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;QACD,sCAAsC;QACtC,IAAI,OAAO,SAAS;IACtB;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,SAAS,eAAe,EAAE;YACzI,SAAS;YACT,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAClD,SAAS;YACT,OAAO;QACT;QAEA,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAChC,SAAS;YACT,OAAO;QACT;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,SAAS,KAAK,GAAG;YACpC,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,SAAS;gBACb,WAAW,SAAS,SAAS;gBAC7B,UAAU,SAAS,QAAQ;gBAC3B,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;YAC7B;YAEA,QAAQ,GAAG,CAAC;YACZ,WAAW;YAEX,4CAA4C;YAC5C,WAAW;gBACT,OAAO,IAAI,CAAC;YACd,GAAG;QACL,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS,IAAI,KAAK,IAAI;QACxB,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAK;gBACnC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,6LAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;0CAGrD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOjC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;sBAEV,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAEpB,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAmD;;;;;;0CAGxE,6LAAC,mIAAA,CAAA,kBAAe;gCAAC,WAAU;0CAAmC;;;;;;;;;;;;kCAKhE,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;oCACrC,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAK;wCACnC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,WAAU;;0DAEV,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAA0C;;;;;;;;;;;;kDAI9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,SAAQ;wDAAY,WAAU;kEAAuD;;;;;;kEAG5F,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,QAAQ;wDACR,OAAO,SAAS,SAAS;wDACzB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAuD;;;;;;kEAG3F,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,QAAQ;wDACR,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAAuD;;;;;;0DAG3F,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAuD;;;;;;0DAGxF,6LAAC;gDACC,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,QAAQ;gDACR,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAAuD;;;;;;0DAG3F,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAM,eAAe,SAAS;wDAC9B,QAAQ;wDACR,OAAO,SAAS,QAAQ;wDACxB,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;kEAEd,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,gBAAgB,CAAC;wDAChC,WAAU;kEAET,6BAAe,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAAe,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAKtE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAkB,WAAU;0DAAuD;;;;;;0DAGlG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAM,sBAAsB,SAAS;wDACrC,QAAQ;wDACR,OAAO,SAAS,eAAe;wDAC/B,UAAU;wDACV,WAAU;wDACV,aAAY;;;;;;kEAEd,6LAAC;wDACC,MAAK;wDACL,SAAS,IAAM,uBAAuB,CAAC;wDACvC,WAAU;kEAET,oCAAsB,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;iFAAe,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAK7E,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,wBACC;;8DACE,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;2DAInD;;;;;;;;;;;;0CAKN,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;;wCAA2C;wCAC7B;sDACzB,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAxSwB;;QACP,qIAAA,CAAA,YAAS;QACH,kIAAA,CAAA,UAAO;;;KAFN", "debugId": null}}]}
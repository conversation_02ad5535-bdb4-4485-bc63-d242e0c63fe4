"use client"

import React, { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  Camera,
  Settings,
  Power,
  PowerOff,
  Eye,
  EyeOff,
  Wifi,
  WifiOff,
  MoreVertical,
  Plus,
  Search,
  Filter,
  Loader2,
  AlertCircle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { apiService } from "@/lib/api"
import { Camera as CameraType } from "@/types/api"
import { ProtectedRoute } from "@/components/ProtectedRoute"

// Helper function to format last seen time
const formatLastSeen = (lastHeartbeat?: string): string => {
  if (!lastHeartbeat) return "Never"

  const now = new Date()
  const lastSeen = new Date(lastHeartbeat)
  const diffMs = now.getTime() - lastSeen.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMinutes < 1) return "Just now"
  if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`
  if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
  return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
}

// Map backend status to frontend status
const mapCameraStatus = (status: string): "online" | "offline" | "maintenance" => {
  switch (status) {
    case 'online': return 'online'
    case 'offline': return 'offline'
    case 'error': return 'maintenance'
    default: return 'offline'
  }
}

function CamerasPage() {
  // State management
  const [cameras, setCameras] = useState<CameraType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [armingCamera, setArmingCamera] = useState<string | null>(null)

  // Load cameras on component mount
  useEffect(() => {
    loadCameras()
  }, [])

  const loadCameras = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiService.getCameras()
      setCameras(response.cameras || [])
    } catch (err: any) {
      console.error('Failed to load cameras:', err)
      setError(err.error || 'Failed to load cameras')
    } finally {
      setLoading(false)
    }
  }

  // Handle ARM/DISARM toggle
  const handleArmToggle = async (cameraId: string, currentArmed: boolean) => {
    try {
      setArmingCamera(cameraId)

      if (currentArmed) {
        await apiService.disarmCamera(cameraId)
      } else {
        await apiService.armCamera(cameraId)
      }

      // Refresh cameras to get updated status
      await loadCameras()
    } catch (err: any) {
      console.error('Failed to toggle camera arm state:', err)
      setError(err.error || 'Failed to update camera')
    } finally {
      setArmingCamera(null)
    }
  }

  // Helper function for status badges
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "online":
        return <Badge variant="success" size="sm">Online</Badge>
      case "offline":
        return <Badge variant="destructive" size="sm">Offline</Badge>
      case "maintenance":
        return <Badge variant="warning" size="sm">Maintenance</Badge>
      default:
        return <Badge variant="secondary" size="sm">Unknown</Badge>
    }
  }

  // Filter cameras based on search and status
  const filteredCameras = cameras.filter(camera => {
    const matchesSearch = camera.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         camera.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         camera.ipAddress.includes(searchTerm)

    const matchesStatus = statusFilter === "all" || camera.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading cameras...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load Cameras</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={loadCameras}>
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Camera Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and manage all your HikVision cameras
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Camera
          </Button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center justify-between space-x-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search cameras..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="online">Online</option>
            <option value="offline">Offline</option>
            <option value="maintenance">Maintenance</option>
          </select>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {filteredCameras.length} of {cameras.length} cameras
          </span>
        </div>
      </div>

      {/* Camera Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredCameras.map((camera, index) => (
          <motion.div
            key={camera.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900">
                      <Camera className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{camera.name}</CardTitle>
                      <CardDescription>{camera.location}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(camera.status)}
                    <Button variant="ghost" size="sm">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">IP Address</span>
                    <p className="font-medium">{camera.ipAddress}:{camera.port}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Model</span>
                    <p className="font-medium">{camera.cameraModel}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Last Seen</span>
                    <p className="font-medium">{formatLastSeen(camera.lastHeartbeat)}</p>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">Status</span>
                    <p className="font-medium capitalize">{camera.status}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-4">
                    <Button
                      variant={camera.isArmed ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleArmToggle(camera._id, camera.isArmed)}
                      disabled={mapCameraStatus(camera.status) === "offline" || armingCamera === camera._id}
                    >
                      {armingCamera === camera._id ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {camera.isArmed ? "Disarming..." : "Arming..."}
                        </>
                      ) : camera.isArmed ? (
                        <>
                          <Eye className="mr-2 h-4 w-4" />
                          Armed
                        </>
                      ) : (
                        <>
                          <EyeOff className="mr-2 h-4 w-4" />
                          Disarmed
                        </>
                      )}
                    </Button>
                    <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                      {mapCameraStatus(camera.status) === "online" ? (
                        <Wifi className="h-4 w-4 text-green-500" />
                      ) : (
                        <WifiOff className="h-4 w-4 text-red-500" />
                      )}
                      <span>{camera.isActive ? "Active" : "Inactive"}</span>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

export default function CamerasPageWrapper() {
  return (
    <ProtectedRoute requiredRole="operator">
      <CamerasPage />
    </ProtectedRoute>
  )
}

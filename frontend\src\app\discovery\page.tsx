'use client'

import { useState, useEffect } from 'react'
import { apiClient } from '@/lib/api'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { 
  Search, 
  Wifi, 
  Camera, 
  Play, 
  Square, 
  CheckCircle, 
  XCircle, 
  Clock,
  Plus,
  Settings,
  Network
} from 'lucide-react'

interface DiscoveredCamera {
  ipAddress: string
  port: number
  deviceInfo?: any
  capabilities?: any
  isReachable: boolean
  responseTime: number
  lastSeen: Date
}

interface ScanStatus {
  isScanning: boolean
  progress: number
  total: number
}

export default function DiscoveryPage() {
  const [scanStatus, setScanStatus] = useState<ScanStatus>({ isScanning: false, progress: 0, total: 0 })
  const [discoveredCameras, setDiscoveredCameras] = useState<DiscoveredCamera[]>([])
  const [scanOptions, setScanOptions] = useState({
    networkRange: '***********/24',
    portRange: [80, 8000, 8080],
    timeout: 5000,
    maxConcurrent: 20,
    credentials: [{ username: 'admin', password: 'admin123' }]
  })
  const [testConnection, setTestConnection] = useState({
    ipAddress: '',
    port: 80,
    username: 'admin',
    password: '',
    useHttps: false
  })
  const [testResult, setTestResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  // Poll scan status when scanning
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (scanStatus.isScanning) {
      interval = setInterval(async () => {
        try {
          const status = await apiClient.getScanStatus()
          setScanStatus(status)
          
          if (!status.isScanning) {
            clearInterval(interval)
          }
        } catch (error) {
          console.error('Failed to get scan status:', error)
        }
      }, 1000)
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [scanStatus.isScanning])

  const startNetworkScan = async () => {
    try {
      setError(null)
      setLoading(true)
      setDiscoveredCameras([])
      
      await apiClient.startNetworkScan(scanOptions)
      setScanStatus({ isScanning: true, progress: 0, total: 0 })
    } catch (error: any) {
      setError(error.message || 'Failed to start network scan')
    } finally {
      setLoading(false)
    }
  }

  const stopNetworkScan = async () => {
    try {
      await apiClient.stopNetworkScan()
      setScanStatus({ isScanning: false, progress: 0, total: 0 })
    } catch (error: any) {
      setError(error.message || 'Failed to stop network scan')
    }
  }

  const testCameraConnection = async () => {
    try {
      setError(null)
      setLoading(true)
      setTestResult(null)
      
      const result = await apiClient.testCameraConnection(testConnection)
      setTestResult(result)
    } catch (error: any) {
      setError(error.message || 'Failed to test camera connection')
    } finally {
      setLoading(false)
    }
  }

  const addCredential = () => {
    setScanOptions(prev => ({
      ...prev,
      credentials: [...prev.credentials, { username: '', password: '' }]
    }))
  }

  const removeCredential = (index: number) => {
    setScanOptions(prev => ({
      ...prev,
      credentials: prev.credentials.filter((_, i) => i !== index)
    }))
  }

  const updateCredential = (index: number, field: 'username' | 'password', value: string) => {
    setScanOptions(prev => ({
      ...prev,
      credentials: prev.credentials.map((cred, i) => 
        i === index ? { ...cred, [field]: value } : cred
      )
    }))
  }

  const scanProgress = scanStatus.total > 0 ? (scanStatus.progress / scanStatus.total) * 100 : 0

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Camera Discovery</h1>
          <p className="text-muted-foreground">
            Discover and connect HikVision cameras on your network
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Network className="h-8 w-8 text-primary" />
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="scan" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="scan">Network Scan</TabsTrigger>
          <TabsTrigger value="test">Test Connection</TabsTrigger>
        </TabsList>

        <TabsContent value="scan" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>Network Scan Configuration</span>
              </CardTitle>
              <CardDescription>
                Configure network scanning parameters to discover HikVision cameras
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="networkRange">Network Range (CIDR)</Label>
                  <Input
                    id="networkRange"
                    value={scanOptions.networkRange}
                    onChange={(e) => setScanOptions(prev => ({ ...prev, networkRange: e.target.value }))}
                    placeholder="***********/24"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="portRange">Port Range (comma-separated)</Label>
                  <Input
                    id="portRange"
                    value={scanOptions.portRange.join(', ')}
                    onChange={(e) => setScanOptions(prev => ({ 
                      ...prev, 
                      portRange: e.target.value.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p))
                    }))}
                    placeholder="80, 8000, 8080"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeout">Timeout (ms)</Label>
                  <Input
                    id="timeout"
                    type="number"
                    value={scanOptions.timeout}
                    onChange={(e) => setScanOptions(prev => ({ ...prev, timeout: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxConcurrent">Max Concurrent</Label>
                  <Input
                    id="maxConcurrent"
                    type="number"
                    value={scanOptions.maxConcurrent}
                    onChange={(e) => setScanOptions(prev => ({ ...prev, maxConcurrent: parseInt(e.target.value) }))}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Default Credentials</Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addCredential}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Credential
                  </Button>
                </div>
                
                {scanOptions.credentials.map((cred, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      placeholder="Username"
                      value={cred.username}
                      onChange={(e) => updateCredential(index, 'username', e.target.value)}
                    />
                    <Input
                      type="password"
                      placeholder="Password"
                      value={cred.password}
                      onChange={(e) => updateCredential(index, 'password', e.target.value)}
                    />
                    {scanOptions.credentials.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCredential(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex items-center space-x-2">
                {!scanStatus.isScanning ? (
                  <Button 
                    onClick={startNetworkScan} 
                    disabled={loading}
                    className="flex items-center space-x-2"
                  >
                    <Play className="h-4 w-4" />
                    <span>Start Scan</span>
                  </Button>
                ) : (
                  <Button 
                    onClick={stopNetworkScan} 
                    variant="destructive"
                    className="flex items-center space-x-2"
                  >
                    <Square className="h-4 w-4" />
                    <span>Stop Scan</span>
                  </Button>
                )}
              </div>

              {scanStatus.isScanning && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Scanning progress</span>
                    <span>{scanStatus.progress} / {scanStatus.total} ({Math.round(scanProgress)}%)</span>
                  </div>
                  <Progress value={scanProgress} className="w-full" />
                </div>
              )}
            </CardContent>
          </Card>

          {discoveredCameras.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Camera className="h-5 w-5" />
                  <span>Discovered Cameras ({discoveredCameras.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {discoveredCameras.map((camera, index) => (
                    <Card key={index} className="border-2">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{camera.ipAddress}:{camera.port}</CardTitle>
                          <Badge variant={camera.isReachable ? "default" : "destructive"}>
                            {camera.isReachable ? "Online" : "Offline"}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-2">
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          <Clock className="h-4 w-4" />
                          <span>{camera.responseTime}ms</span>
                        </div>
                        {camera.deviceInfo && (
                          <div className="text-sm">
                            <p><strong>Model:</strong> {camera.deviceInfo.model || 'Unknown'}</p>
                            <p><strong>Serial:</strong> {camera.deviceInfo.serialNumber || 'Unknown'}</p>
                          </div>
                        )}
                        <Button size="sm" className="w-full">
                          <Plus className="h-4 w-4 mr-2" />
                          Add Camera
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="test" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Test Camera Connection</span>
              </CardTitle>
              <CardDescription>
                Test connection to a specific camera with credentials
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="testIp">IP Address</Label>
                  <Input
                    id="testIp"
                    value={testConnection.ipAddress}
                    onChange={(e) => setTestConnection(prev => ({ ...prev, ipAddress: e.target.value }))}
                    placeholder="*************"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="testPort">Port</Label>
                  <Input
                    id="testPort"
                    type="number"
                    value={testConnection.port}
                    onChange={(e) => setTestConnection(prev => ({ ...prev, port: parseInt(e.target.value) }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="testUsername">Username</Label>
                  <Input
                    id="testUsername"
                    value={testConnection.username}
                    onChange={(e) => setTestConnection(prev => ({ ...prev, username: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="testPassword">Password</Label>
                  <Input
                    id="testPassword"
                    type="password"
                    value={testConnection.password}
                    onChange={(e) => setTestConnection(prev => ({ ...prev, password: e.target.value }))}
                  />
                </div>
              </div>

              <Button 
                onClick={testCameraConnection} 
                disabled={loading || !testConnection.ipAddress || !testConnection.username || !testConnection.password}
                className="flex items-center space-x-2"
              >
                <Wifi className="h-4 w-4" />
                <span>Test Connection</span>
              </Button>

              {testResult && (
                <Card className="mt-4">
                  <CardContent className="pt-6">
                    <div className="flex items-center space-x-2 mb-4">
                      {testResult.isConnected ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <span className="font-medium">
                        {testResult.isConnected ? 'Connection Successful' : 'Connection Failed'}
                      </span>
                    </div>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <p><strong>IP:</strong> {testResult.ipAddress}:{testResult.port}</p>
                      <p><strong>Response Time:</strong> {testResult.responseTime}ms</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

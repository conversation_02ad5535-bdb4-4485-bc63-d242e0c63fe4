"use client"

import React, { useState, useEffect } from "react"
import { motion } from "framer-motion"
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Download,
  Filter,
  Search,
  Calendar,
  Camera,
  User,
  Car,
  Shield,
  Loader2,
  AlertCircle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { apiService } from "@/lib/api"
import { Event as EventType } from "@/types/api"
import { ProtectedRoute } from "@/components/ProtectedRoute"

// Helper function to format timestamp
const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffMinutes < 1) return "Just now"
  if (diffMinutes < 60) return `${diffMinutes}m ago`
  if (diffHours < 24) return `${diffHours}h ago`
  if (diffDays < 7) return `${diffDays}d ago`
  return date.toLocaleDateString()
}

function EventsPage() {
  // State management
  const [events, setEvents] = useState<EventType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "acknowledged" | "resolved">("all")
  const [severityFilter, setSeverityFilter] = useState<"all" | "low" | "medium" | "high" | "critical">("all")
  const [typeFilter, setTypeFilter] = useState<"all" | "person" | "vehicle" | "motion" | "face" | "intrusion">("all")
  const [acknowledgingEvent, setAcknowledgingEvent] = useState<string | null>(null)
  const [resolvingEvent, setResolvingEvent] = useState<string | null>(null)

  // Load events on component mount
  useEffect(() => {
    loadEvents()
  }, [])

  const loadEvents = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiService.getEvents()
      setEvents(response.events || [])
    } catch (err: any) {
      console.error('Failed to load events:', err)
      setError(err.error || 'Failed to load events')
    } finally {
      setLoading(false)
    }
  }

  // Handle event acknowledgment
  const handleAcknowledgeEvent = async (eventId: string) => {
    try {
      setAcknowledgingEvent(eventId)
      await apiService.acknowledgeEvent(eventId, {
        notes: "Acknowledged from dashboard"
      })
      await loadEvents() // Refresh events
    } catch (err: any) {
      console.error('Failed to acknowledge event:', err)
      setError(err.error || 'Failed to acknowledge event')
    } finally {
      setAcknowledgingEvent(null)
    }
  }

  // Handle event resolution
  const handleResolveEvent = async (eventId: string) => {
    try {
      setResolvingEvent(eventId)
      await apiService.resolveEvent(eventId, {
        resolution: "Resolved from dashboard",
        notes: "Event resolved by operator"
      })
      await loadEvents() // Refresh events
    } catch (err: any) {
      console.error('Failed to resolve event:', err)
      setError(err.error || 'Failed to resolve event')
    } finally {
      setResolvingEvent(null)
    }
  }

  // Filter events based on search and filters
  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || event.status === statusFilter
    const matchesSeverity = severityFilter === "all" || event.severity === severityFilter
    const matchesType = typeFilter === "all" || event.type === typeFilter
    return matchesSearch && matchesStatus && matchesSeverity && matchesType
  })

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading events...</span>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to Load Events</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={loadEvents}>
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="destructive" size="sm">Active</Badge>
      case "resolved":
        return <Badge variant="success" size="sm">Resolved</Badge>
      case "acknowledged":
        return <Badge variant="warning" size="sm">Acknowledged</Badge>
      default:
        return <Badge variant="secondary" size="sm">Unknown</Badge>
    }
  }

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case "critical":
        return <Badge variant="destructive" size="sm">Critical</Badge>
      case "high":
        return <Badge variant="warning" size="sm">High</Badge>
      case "medium":
        return <Badge variant="default" size="sm">Medium</Badge>
      case "low":
        return <Badge variant="secondary" size="sm">Low</Badge>
      default:
        return <Badge variant="secondary" size="sm">Unknown</Badge>
    }
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case "person":
        return <User className="h-5 w-5" />
      case "vehicle":
        return <Car className="h-5 w-5" />
      case "motion":
        return <Activity className="h-5 w-5" />
      case "face":
        return <Eye className="h-5 w-5" />
      case "intrusion":
        return <Shield className="h-5 w-5" />
      case "object":
        return <AlertTriangle className="h-5 w-5" />
      default:
        return <Activity className="h-5 w-5" />
    }
  }

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return "Just now"
    if (diffMins < 60) return `${diffMins} minutes ago`
    if (diffHours < 24) return `${diffHours} hours ago`
    if (diffDays < 7) return `${diffDays} days ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Security Events
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and analyze security events from all cameras
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Date Range
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap items-center gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search events..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as any)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="acknowledged">Acknowledged</option>
          <option value="resolved">Resolved</option>
        </select>

        <select
          value={severityFilter}
          onChange={(e) => setSeverityFilter(e.target.value as any)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Severity</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>

        <select
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value as any)}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="all">All Types</option>
          <option value="person">Person</option>
          <option value="vehicle">Vehicle</option>
          <option value="motion">Motion</option>
          <option value="face">Face</option>
          <option value="intrusion">Intrusion</option>
        </select>

        <div className="ml-auto text-sm text-gray-600 dark:text-gray-400">
          {filteredEvents.length} of {events.length} events
        </div>
      </div>

      {/* Events List */}
      <div className="space-y-4">
        {filteredEvents.length === 0 ? (
          <div className="text-center py-12">
            <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No Events Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {events.length === 0
                ? "No events have been recorded yet."
                : "No events match your current filters."}
            </p>
          </div>
        ) : (
          filteredEvents.map((event, index) => (
            <motion.div
              key={event._id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <Card className="hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900">
                        {getEventIcon(event.type)}
                      </div>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {event.title}
                          </h3>
                          {getStatusBadge(event.status)}
                          {getSeverityBadge(event.severity)}
                          <Badge variant="outline" size="sm">
                            {event.confidence}% confidence
                          </Badge>
                        </div>
                        <p className="text-gray-600 dark:text-gray-400">
                          {event.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                          <span className="flex items-center space-x-1">
                            <Camera className="h-4 w-4" />
                            <span>{event.cameraName || 'Unknown Camera'}</span>
                          </span>
                          <span>{event.location || 'Unknown Location'}</span>
                          <span>{formatTimestamp(event.timestamp)}</span>
                        </div>
                        {event.aiAnalysis && (
                          <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <p className="text-sm text-blue-800 dark:text-blue-200">
                              <strong>AI Analysis:</strong> {event.aiAnalysis}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {event.status === 'active' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAcknowledgeEvent(event._id)}
                          disabled={acknowledgingEvent === event._id}
                        >
                          {acknowledgingEvent === event._id ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <CheckCircle className="mr-2 h-4 w-4" />
                          )}
                          Acknowledge
                        </Button>
                      )}
                      {(event.status === 'active' || event.status === 'acknowledged') && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleResolveEvent(event._id)}
                          disabled={resolvingEvent === event._id}
                        >
                          {resolvingEvent === event._id ? (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          ) : (
                            <Shield className="mr-2 h-4 w-4" />
                          )}
                          Resolve
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        )}
      </div>
    </div>
  )
}

export default function EventsPageWrapper() {
  return (
    <ProtectedRoute>
      <EventsPage />
    </ProtectedRoute>
  )
}

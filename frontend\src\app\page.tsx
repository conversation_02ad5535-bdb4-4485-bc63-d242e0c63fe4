"use client"

import React from "react"
import { motion } from "framer-motion"
import {
  Camera,
  Activity,
  Shield,
  Zap,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ProtectedRoute } from "@/components/ProtectedRoute"

const stats = [
  {
    name: "Active Cameras",
    value: "24",
    change: "+2",
    changeType: "positive",
    icon: Camera,
    color: "blue",
  },
  {
    name: "Events Today",
    value: "1,247",
    change: "+12%",
    changeType: "positive",
    icon: Activity,
    color: "green",
  },
  {
    name: "AI Detections",
    value: "89",
    change: "+5",
    changeType: "positive",
    icon: Zap,
    color: "purple",
  },
  {
    name: "Security Alerts",
    value: "3",
    change: "-2",
    changeType: "negative",
    icon: Shield,
    color: "red",
  },
]

const recentEvents = [
  {
    id: 1,
    type: "Person Detected",
    camera: "Front Entrance",
    time: "2 minutes ago",
    status: "active",
    confidence: 94
  },
  {
    id: 2,
    type: "Vehicle Recognition",
    camera: "Parking Lot A",
    time: "5 minutes ago",
    status: "resolved",
    confidence: 87
  },
  {
    id: 3,
    type: "Motion Alert",
    camera: "Warehouse Door",
    time: "12 minutes ago",
    status: "investigating",
    confidence: 76
  },
  {
    id: 4,
    type: "Face Recognition",
    camera: "Main Lobby",
    time: "18 minutes ago",
    status: "resolved",
    confidence: 92
  },
  {
    id: 5,
    type: "Intrusion Alert",
    camera: "Perimeter Fence",
    time: "25 minutes ago",
    status: "active",
    confidence: 89
  }
]

function Dashboard() {
  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Welcome back! Here's what's happening with your security system.
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <TrendingUp className="mr-2 h-4 w-4" />
            View Reports
          </Button>
          <Button>
            <Shield className="mr-2 h-4 w-4" />
            Arm All Cameras
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.name}
                </CardTitle>
                <stat.icon className={`h-4 w-4 text-${stat.color}-600`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value}
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  <span
                    className={`${
                      stat.changeType === "positive"
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {stat.change}
                  </span>{" "}
                  from last hour
                </p>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Recent Events and Analytics */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Recent Events */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Recent Events</span>
              </CardTitle>
              <CardDescription>
                Latest security events from your cameras
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentEvents.map((event) => (
                <motion.div
                  key={event.id}
                  className="flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {event.status === "active" && (
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                      )}
                      {event.status === "resolved" && (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      )}
                      {event.status === "investigating" && (
                        <Clock className="h-5 w-5 text-amber-500" />
                      )}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {event.type}
                      </p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {event.camera} • {event.time}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={
                        event.status === "active"
                          ? "destructive"
                          : event.status === "resolved"
                          ? "success"
                          : "warning"
                      }
                      size="sm"
                    >
                      {event.confidence}%
                    </Badge>
                  </div>
                </motion.div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>System Status</span>
            </CardTitle>
            <CardDescription>
              Current status of all system components
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Camera Network
                </span>
                <Badge variant="success">Online</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  AI Processing
                </span>
                <Badge variant="success">Active</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Database
                </span>
                <Badge variant="success">Connected</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Storage
                </span>
                <Badge variant="warning">78% Full</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Notifications
                </span>
                <Badge variant="success">Enabled</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  )
}

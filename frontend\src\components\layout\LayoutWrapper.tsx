"use client"

import React from 'react'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Sidebar } from './sidebar'
import { Loader2 } from 'lucide-react'

interface LayoutWrapperProps {
  children: React.ReactNode
}

export const LayoutWrapper: React.FC<LayoutWrapperProps> = ({ children }) => {
  const pathname = usePathname()
  const { isAuthenticated, loading } = useAuth()

  // Routes that don't need authentication (public routes)
  const publicRoutes = ['/login', '/register']
  const isPublicRoute = publicRoutes.includes(pathname)

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
          <span className="text-gray-600 dark:text-gray-400">Loading...</span>
        </div>
      </div>
    )
  }

  // For public routes (login/register), show full-width layout
  if (isPublicRoute) {
    return <>{children}</>
  }

  // For authenticated routes, show sidebar + main content
  if (isAuthenticated) {
    return (
      <div className="flex h-full">
        <Sidebar />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    )
  }

  // For protected routes when not authenticated, the ProtectedRoute component will handle redirect
  return <>{children}</>
}

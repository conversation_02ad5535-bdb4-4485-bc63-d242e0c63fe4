// API Service Layer for Frontend-Backend Integration

import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshTokenRequest,
  Camera,
  CreateCameraRequest,
  UpdateCameraRequest,
  CameraStats,
  Event,
  CreateEventRequest,
  EventFilters,
  AcknowledgeEventRequest,
  ResolveEventRequest,
  EventStats,
  ApiResponse,
  PaginatedResponse,
  ApiError,
  HealthStatus,
  SystemStatus
} from '@/types/api'

class ApiService {
  private baseUrl: string
  private accessToken: string | null = null
  private refreshToken: string | null = null

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'
    
    // Load tokens from localStorage on initialization
    if (typeof window !== 'undefined') {
      this.accessToken = localStorage.getItem('accessToken')
      this.refreshToken = localStorage.getItem('refreshToken')
    }
  }

  // ===== PRIVATE HELPER METHODS =====
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    // Add authorization header if token exists
    if (this.accessToken) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${this.accessToken}`,
      }
    }

    try {
      const response = await fetch(url, config)
      
      // Handle 401 - try to refresh token
      if (response.status === 401 && this.refreshToken) {
        const refreshed = await this.refreshAccessToken()
        if (refreshed) {
          // Retry the original request with new token
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${this.accessToken}`,
          }
          const retryResponse = await fetch(url, config)
          return this.handleResponse<T>(retryResponse)
        }
      }

      return this.handleResponse<T>(response)
    } catch (error) {
      console.error('API request failed:', error)
      throw new Error('Network error occurred')
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type')
    const isJson = contentType?.includes('application/json')
    
    const data = isJson ? await response.json() : await response.text()

    if (!response.ok) {
      const error: ApiError = {
        error: data.error || `HTTP ${response.status}`,
        details: data.details,
        status: response.status,
      }
      throw error
    }

    return data
  }

  private setTokens(accessToken: string, refreshToken: string) {
    this.accessToken = accessToken
    this.refreshToken = refreshToken
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessToken', accessToken)
      localStorage.setItem('refreshToken', refreshToken)
    }
  }

  private clearTokens() {
    this.accessToken = null
    this.refreshToken = null
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
    }
  }

  // ===== AUTHENTICATION METHODS =====
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.makeRequest<any>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })

    // Backend returns token and refreshToken directly, not nested under tokens
    this.setTokens(response.token, response.refreshToken)

    // Transform response to match expected AuthResponse format
    return {
      message: response.message,
      user: response.user,
      tokens: {
        accessToken: response.token,
        refreshToken: response.refreshToken
      }
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await this.makeRequest<any>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    })

    // Backend returns token and refreshToken directly, not nested under tokens
    this.setTokens(response.token, response.refreshToken)

    // Transform response to match expected AuthResponse format
    return {
      message: response.message,
      user: response.user,
      tokens: {
        accessToken: response.token,
        refreshToken: response.refreshToken
      }
    }
  }

  async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) return false

    try {
      const response = await this.makeRequest<any>('/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({ refreshToken: this.refreshToken }),
      })

      // Backend returns token and refreshToken directly, not nested under tokens
      this.setTokens(response.token, response.refreshToken)
      return true
    } catch (error) {
      this.clearTokens()
      return false
    }
  }

  async logout(): Promise<void> {
    this.clearTokens()
  }

  async getProfile(): Promise<AuthResponse['user']> {
    const response = await this.makeRequest<{ user: AuthResponse['user'] }>('/api/auth/me')
    return response.user
  }

  // ===== CAMERA METHODS =====
  async getCameras(filters?: {
    status?: string
    location?: string
    limit?: number
    page?: number
  }): Promise<{ cameras: Camera[]; pagination?: any }> {
    const params = new URLSearchParams()
    if (filters?.status) params.append('status', filters.status)
    if (filters?.location) params.append('location', filters.location)
    if (filters?.limit) params.append('limit', filters.limit.toString())
    if (filters?.page) params.append('page', filters.page.toString())

    const queryString = params.toString()
    const endpoint = `/api/cameras${queryString ? `?${queryString}` : ''}`
    
    return this.makeRequest<{ cameras: Camera[]; pagination?: any }>(endpoint)
  }

  async getCamera(id: string): Promise<Camera> {
    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`)
    return response.camera
  }

  async createCamera(cameraData: CreateCameraRequest): Promise<Camera> {
    const response = await this.makeRequest<{ camera: Camera }>('/api/cameras', {
      method: 'POST',
      body: JSON.stringify(cameraData),
    })
    return response.camera
  }

  async updateCamera(id: string, updates: UpdateCameraRequest): Promise<Camera> {
    const response = await this.makeRequest<{ camera: Camera }>(`/api/cameras/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    })
    return response.camera
  }

  async deleteCamera(id: string): Promise<void> {
    await this.makeRequest(`/api/cameras/${id}`, {
      method: 'DELETE',
    })
  }

  async armCamera(id: string): Promise<void> {
    await this.makeRequest(`/api/cameras/${id}/arm`, {
      method: 'POST',
    })
  }

  async disarmCamera(id: string): Promise<void> {
    await this.makeRequest(`/api/cameras/${id}/disarm`, {
      method: 'POST',
    })
  }

  async getCameraStatus(id: string): Promise<any> {
    return this.makeRequest(`/api/cameras/${id}/status`)
  }

  async getCameraSnapshot(id: string): Promise<Blob> {
    const response = await fetch(`${this.baseURL}/api/cameras/${id}/snapshot`, {
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
      },
    })

    if (!response.ok) {
      throw new Error('Failed to get camera snapshot')
    }

    return response.blob()
  }

  async getCameraStreamUrl(id: string): Promise<{ streamUrl: string }> {
    return this.makeRequest(`/api/cameras/${id}/stream`)
  }

  // Discovery API methods
  async startNetworkScan(scanOptions: {
    networkRange: string
    portRange: number[]
    timeout?: number
    maxConcurrent?: number
    credentials?: Array<{ username: string; password: string }>
  }): Promise<{ message: string; scanId: string }> {
    return this.makeRequest('/api/discovery/scan', {
      method: 'POST',
      body: JSON.stringify(scanOptions),
    })
  }

  async getScanStatus(): Promise<{ isScanning: boolean; progress: number; total: number }> {
    return this.makeRequest('/api/discovery/status')
  }

  async stopNetworkScan(): Promise<{ message: string }> {
    return this.makeRequest('/api/discovery/stop', {
      method: 'POST',
    })
  }

  async pingHost(ipAddress: string, port?: number, timeout?: number): Promise<{
    ipAddress: string
    port: number
    isReachable: boolean
    responseTime: number
  }> {
    return this.makeRequest('/api/discovery/ping', {
      method: 'POST',
      body: JSON.stringify({ ipAddress, port, timeout }),
    })
  }

  async testCameraConnection(credentials: {
    ipAddress: string
    port: number
    username: string
    password: string
    useHttps?: boolean
  }): Promise<{
    ipAddress: string
    port: number
    isConnected: boolean
    responseTime: number
  }> {
    return this.makeRequest('/api/discovery/test-camera', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
  }

  async getCameraStats(): Promise<CameraStats> {
    return this.makeRequest<CameraStats>('/api/cameras/stats')
  }

  // ===== EVENT METHODS =====
  async getEvents(filters?: EventFilters): Promise<{ events: Event[]; pagination?: any }> {
    const params = new URLSearchParams()
    if (filters?.type) params.append('type', filters.type)
    if (filters?.severity) params.append('severity', filters.severity)
    if (filters?.status) params.append('status', filters.status)
    if (filters?.cameraId) params.append('cameraId', filters.cameraId)
    if (filters?.startDate) params.append('startDate', filters.startDate)
    if (filters?.endDate) params.append('endDate', filters.endDate)
    if (filters?.limit) params.append('limit', filters.limit.toString())
    if (filters?.page) params.append('page', filters.page.toString())

    const queryString = params.toString()
    const endpoint = `/api/events${queryString ? `?${queryString}` : ''}`
    
    return this.makeRequest<{ events: Event[]; pagination?: any }>(endpoint)
  }

  async getEvent(id: string): Promise<Event> {
    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}`)
    return response.event
  }

  async createEvent(eventData: CreateEventRequest): Promise<Event> {
    const response = await this.makeRequest<{ event: Event }>('/api/events', {
      method: 'POST',
      body: JSON.stringify(eventData),
    })
    return response.event
  }

  async acknowledgeEvent(id: string, data: AcknowledgeEventRequest): Promise<Event> {
    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/acknowledge`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.event
  }

  async resolveEvent(id: string, data: ResolveEventRequest): Promise<Event> {
    const response = await this.makeRequest<{ event: Event }>(`/api/events/${id}/resolve`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
    return response.event
  }

  async getEventStats(): Promise<EventStats> {
    return this.makeRequest<EventStats>('/api/events/stats')
  }

  // ===== SYSTEM METHODS =====
  async getHealth(): Promise<HealthStatus> {
    return this.makeRequest<HealthStatus>('/health')
  }

  async getSystemStatus(): Promise<SystemStatus> {
    return this.makeRequest<SystemStatus>('/api/status')
  }

  // ===== UTILITY METHODS =====
  isAuthenticated(): boolean {
    return !!this.accessToken
  }

  getAccessToken(): string | null {
    return this.accessToken
  }
}

// Export singleton instance
export const apiService = new ApiService()
export default apiService

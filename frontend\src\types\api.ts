// API Types for Frontend-Backend Integration

// ===== AUTHENTICATION TYPES =====
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  firstName: string
  lastName: string
  role?: 'admin' | 'operator' | 'viewer'
}

export interface AuthResponse {
  message: string
  user: {
    id: string
    username: string
    email: string
    firstName: string
    lastName: string
    role: 'admin' | 'operator' | 'viewer'
    isActive: boolean
    lastLogin?: string
    preferences: {
      theme: 'light' | 'dark' | 'system'
      language: string
      timezone: string
      notifications: {
        email: boolean
        push: boolean
        sms: boolean
      }
    }
    createdAt: string
    updatedAt: string
  }
  tokens: {
    accessToken: string
    refreshToken: string
  }
}

export interface RefreshTokenRequest {
  refreshToken: string
}

// ===== CAMERA TYPES =====
export interface Camera {
  _id: string
  name: string
  cameraModel: string
  ipAddress: string
  port: number
  username: string
  // password excluded from responses
  location: string
  isActive: boolean
  status: 'online' | 'offline' | 'error'
  isArmed: boolean
  lastHeartbeat?: string
  createdAt: string
  updatedAt: string
}

export interface CreateCameraRequest {
  name: string
  cameraModel: string
  ipAddress: string
  port: number
  username: string
  password: string
  location: string
}

export interface UpdateCameraRequest {
  name?: string
  cameraModel?: string
  ipAddress?: string
  port?: number
  username?: string
  password?: string
  location?: string
  isActive?: boolean
}

export interface CameraStats {
  total: number
  online: number
  offline: number
  error: number
  armed: number
  disarmed: number
}

// ===== EVENT TYPES =====
export interface Event {
  _id: string
  eventId: string
  type: 'motion' | 'person' | 'vehicle' | 'face' | 'intrusion'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  status: 'active' | 'acknowledged' | 'resolved'
  cameraId: string
  timestamp: string
  acknowledgment?: {
    acknowledgedBy: string
    acknowledgedAt: string
    notes?: string
  }
  resolution?: {
    resolvedBy: string
    resolvedAt: string
    resolution: string
    notes?: string
  }
  createdAt: string
  updatedAt: string
}

export interface CreateEventRequest {
  type: 'motion' | 'person' | 'vehicle' | 'face' | 'intrusion'
  title: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  cameraId: string
}

export interface EventFilters {
  type?: string
  severity?: string
  status?: string
  cameraId?: string
  startDate?: string
  endDate?: string
  limit?: number
  page?: number
}

export interface AcknowledgeEventRequest {
  notes?: string
}

export interface ResolveEventRequest {
  resolution: string
  notes?: string
}

export interface EventStats {
  total: number
  active: number
  acknowledged: number
  resolved: number
  byType: Record<string, number>
  bySeverity: Record<string, number>
}

// ===== API RESPONSE TYPES =====
export interface ApiResponse<T = any> {
  message?: string
  data?: T
  error?: string
  details?: any[]
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// ===== ERROR TYPES =====
export interface ApiError {
  error: string
  details?: any[]
  status?: number
}

// ===== SYSTEM TYPES =====
export interface HealthStatus {
  status: 'healthy' | 'unhealthy'
  timestamp: string
  uptime: number
  database: 'connected' | 'disconnected'
  services: {
    api: boolean
    database: boolean
    socketio: boolean
  }
}

export interface SystemStatus {
  api: {
    version: string
    status: 'operational' | 'maintenance' | 'error'
    features: {
      authentication: boolean
      cameras: boolean
      events: boolean
      realtime: boolean
    }
  }
  database: {
    status: 'connected' | 'disconnected'
    collections: {
      users: number
      cameras: number
      events: number
    }
  }
}


# AI Agent Prompt: Build Full-Stack Hikvision AI Monitoring System from Scratch

## 🧠 Objective:
Build a **high-performance**, **AI-enhanced**, and **modular** full-stack web application for **real-time monitoring**, **event handling**, and **AI analysis** of Hikvision IP camera feeds, using ISAPI, RTSP, YOLOv8/YOLOv11, and PaddleOCR.

## ✅ General Instructions:
- Create a **complete plan and architecture** in `PLAN.md`.
- Save all progress in a markdown file called `PROGRESS_LOG.md` after every major milestone or step.
- The codebase must be modular, readable, scalable, and production-ready.
- The project must be fully restartable and extendable by another AI agent.
- All decisions, reasoning, conflicts, and technical notes must be documented clearly in `NOTES.md`.

## ⚙️ Core Functionalities to Implement:

### 1. 📡 Camera Event Ingestion
- Use **Hikvision ISAPI** for event subscription using Digest Auth.
- **ARM** Button for cameras to subscribe to all events.
- **DISARM** Button to unsubscribe from events.
- **ARM ALL** But<PERSON> to subscribe all cameras to all events.
- **DISARM ALL** But<PERSON> to unsubscribe all cameras from all events.
- **DISARM ALL** But<PERSON> to unsubscribe from all events.
- Maintain **ARM/DISARM** camera state.
- Persistent event stream with reconnect and recovery logic.

### 2. 🎥 Media Capture (FFmpeg)
- Capture video and image snapshots via RTSP.
- Store media in camera-specific folders.
- Generate thumbnails and smart compression.

### 3. 🤖 AI Processing (Python Microservice)
- Use **YOLOv8/YOLOv11**, **CUDA**, and **PaddleOCR**.
- Run dual-model inference with bounding boxes and class labels.
- Return results to the backend via Flask REST API.

### 4. 🔄 Queue & Processing Pipeline
- Use message queue (BullMQ/Redis).
- Process events by priority.
- Throttling per camera/event and retry logic.

### 5. 🌐 Frontend Dashboard
- Use **Next.js**, **TailwindCSS**, **React Query**, **Socket.IO**.
- Key Pages:
  - Dashboard (Live stats)
  - Event Viewer (Media, AI results, filters)
  - Camera Manager
  - Settings Panel

### 6. 👤 User Management
- JWT auth with Admin, Operator, Viewer roles.
- Role-based access and activity logs.

### 7. 💾 Database (MongoDB)
- Models for users, cameras, events, and settings.
- Optimized indexes for queries and filtering.

## 📈 Performance & Robustness Instructions:

### 🧪 Testing & Reliability
- Jest (backend), React Testing Library, Pytest (AI)
- Structured error handling and JSON validation.

### 🚀 Performance
- Smart frame selection before AI inference.
- Run AI on GPU, fallback to CPU.
- Image pre-processing with Sharp.

### 🔍 Monitoring
- Health endpoints and WebSocket updates.
- Time-tracking for AI inference and database write.

### 📋 Logging
- Structured logs with winston or pino.
- Log all AI errors, retries, and request traces.

## 🧩 Feature Enhancements:

### 🔔 Notifications
- Slack/Email/WhatsApp alerts for selected event types.

### 🎒 Evidence Export Tool
- Export events to PDF reports with images and AI results.

### 🧠 AI Feedback Mode
- Let users label incorrect AI results for retraining dataset.

### 📦 Plugin System
- Allow loading custom AI modules or camera drivers.

## 🔍 Conflict Handling Instructions:
- Remove all legacy/traditional event processing logic.
- Keep only AI-enhanced pipelines.
- Mark and resolve all logic conflicts and document in `NOTES.md`.

## 💡 UI/UX Speed & Feel Enhancements:
- Lazy-load media and charts.
- Paginate event feeds with infinite scroll.
- Cache AI result thumbnails.
- Use WebSockets for instant updates.

## 🧱 Architecture Requirements:
- Isolate services by concern (AI, media, camera, user, etc.).
- Maintain strict module boundaries.
- Document module specs in `PLAN.md`.

## 📝 Final Note to Agent:
Design for **future AI agent collaboration**. Log all logic, design, and process in markdown files (`PLAN.md`, `PROGRESS_LOG.md`, `NOTES.md`) to allow continuation without needing re-analysis.

